import { DateTime } from 'luxon';
import EscrowMilestone from '#models/escrow_milestone';

class MilestoneProgressTracker {
  /**
   * Calculate detailed progress for an escrow's milestones
   */
  static async calculateProgress(escrowId) {
    const milestones = await EscrowMilestone.query()
      .where('escrow_id', escrowId)
      .orderBy('order_index');

    if (milestones.length === 0) {
      return {
        totalMilestones: 0,
        completedMilestones: 0,
        approvedMilestones: 0,
        releasedMilestones: 0,
        rejectedMilestones: 0,
        pendingMilestones: 0,
        overdueMilestones: 0,
        progressPercentage: 0,
        completionPercentage: 0,
        releasePercentage: 0,
        amountReleased: 0,
        amountPending: 0,
        timeline: [],
        nextMilestone: null,
        isComplete: false
      };
    }

    const stats = this.calculateBasicStats(milestones);
    const timeline = this.generateTimeline(milestones);
    const nextMilestone = this.findNextMilestone(milestones);
    const amounts = this.calculateAmounts(milestones);

    return {
      ...stats,
      ...amounts,
      timeline,
      nextMilestone,
      isComplete: stats.releasedMilestones === stats.totalMilestones
    };
  }

  /**
   * Calculate basic milestone statistics
   */
  static calculateBasicStats(milestones) {
    const now = DateTime.now();
    
    const stats = {
      totalMilestones: milestones.length,
      completedMilestones: 0,
      approvedMilestones: 0,
      releasedMilestones: 0,
      rejectedMilestones: 0,
      pendingMilestones: 0,
      overdueMilestones: 0
    };

    milestones.forEach(milestone => {
      switch (milestone.status) {
        case 'completed':
          stats.completedMilestones++;
          break;
        case 'approved':
          stats.approvedMilestones++;
          stats.completedMilestones++; // Approved milestones are also completed
          break;
        case 'released':
          stats.releasedMilestones++;
          stats.approvedMilestones++; // Released milestones are also approved
          stats.completedMilestones++; // Released milestones are also completed
          break;
        case 'rejected':
          stats.rejectedMilestones++;
          break;
        default:
          stats.pendingMilestones++;
      }

      // Check if overdue
      if (milestone.dueDate && 
          milestone.status !== 'released' && 
          milestone.status !== 'approved' &&
          DateTime.fromJSDate(milestone.dueDate) < now) {
        stats.overdueMilestones++;
      }
    });

    // Calculate percentages
    stats.progressPercentage = Math.round((stats.completedMilestones / stats.totalMilestones) * 100);
    stats.completionPercentage = Math.round((stats.approvedMilestones / stats.totalMilestones) * 100);
    stats.releasePercentage = Math.round((stats.releasedMilestones / stats.totalMilestones) * 100);

    return stats;
  }

  /**
   * Calculate amount-based statistics
   */
  static calculateAmounts(milestones) {
    let amountReleased = 0;
    let amountApproved = 0;
    let amountCompleted = 0;
    let amountPending = 0;
    let totalAmount = 0;

    milestones.forEach(milestone => {
      totalAmount += milestone.amount;

      switch (milestone.status) {
        case 'released':
          amountReleased += milestone.amount;
          amountApproved += milestone.amount;
          amountCompleted += milestone.amount;
          break;
        case 'approved':
          amountApproved += milestone.amount;
          amountCompleted += milestone.amount;
          break;
        case 'completed':
          amountCompleted += milestone.amount;
          break;
        default:
          amountPending += milestone.amount;
      }
    });

    return {
      amountReleased: Math.round(amountReleased * 100) / 100,
      amountApproved: Math.round(amountApproved * 100) / 100,
      amountCompleted: Math.round(amountCompleted * 100) / 100,
      amountPending: Math.round(amountPending * 100) / 100,
      totalAmount: Math.round(totalAmount * 100) / 100,
      releasePercentageByAmount: totalAmount > 0 ? Math.round((amountReleased / totalAmount) * 100) : 0,
      approvalPercentageByAmount: totalAmount > 0 ? Math.round((amountApproved / totalAmount) * 100) : 0
    };
  }

  /**
   * Generate milestone timeline
   */
  static generateTimeline(milestones) {
    const timeline = [];

    milestones.forEach(milestone => {
      const timelineItem = {
        milestoneId: milestone.id,
        title: milestone.title,
        orderIndex: milestone.orderIndex,
        amount: milestone.amount,
        percentage: milestone.percentage,
        status: milestone.status,
        dueDate: milestone.dueDate,
        completedAt: milestone.completedAt,
        releasedAt: milestone.releasedAt,
        isOverdue: milestone.isOverdue,
        events: []
      };

      // Add events based on milestone status and timestamps
      if (milestone.completedAt) {
        timelineItem.events.push({
          type: 'completed',
          timestamp: milestone.completedAt,
          description: 'Milestone marked as completed'
        });
      }

      if (milestone.senderApprovedAt) {
        timelineItem.events.push({
          type: 'sender_approved',
          timestamp: milestone.senderApprovedAt,
          description: 'Approved by sender'
        });
      }

      if (milestone.recipientApprovedAt) {
        timelineItem.events.push({
          type: 'recipient_approved',
          timestamp: milestone.recipientApprovedAt,
          description: 'Approved by recipient'
        });
      }

      if (milestone.releasedAt) {
        timelineItem.events.push({
          type: 'released',
          timestamp: milestone.releasedAt,
          description: 'Funds released'
        });
      }

      if (milestone.status === 'rejected') {
        timelineItem.events.push({
          type: 'rejected',
          timestamp: milestone.updatedAt,
          description: `Rejected: ${milestone.rejectionReason || 'No reason provided'}`
        });
      }

      // Sort events by timestamp
      timelineItem.events.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

      timeline.push(timelineItem);
    });

    return timeline;
  }

  /**
   * Find the next milestone to work on
   */
  static findNextMilestone(milestones) {
    // Find the first milestone that is not released
    const nextMilestone = milestones.find(milestone => 
      milestone.status !== 'released'
    );

    if (!nextMilestone) {
      return null; // All milestones are released
    }

    return {
      id: nextMilestone.id,
      title: nextMilestone.title,
      orderIndex: nextMilestone.orderIndex,
      amount: nextMilestone.amount,
      percentage: nextMilestone.percentage,
      status: nextMilestone.status,
      dueDate: nextMilestone.dueDate,
      isOverdue: nextMilestone.isOverdue,
      canBeCompleted: nextMilestone.status === 'pending' || nextMilestone.status === 'in_progress',
      canBeApproved: nextMilestone.status === 'completed',
      canBeReleased: nextMilestone.canBeReleased,
      description: nextMilestone.description,
      deliverables: nextMilestone.deliverablesParsed,
      evidence: nextMilestone.evidenceParsed
    };
  }

  /**
   * Get milestone performance metrics
   */
  static calculatePerformanceMetrics(milestones) {
    const now = DateTime.now();
    let totalDaysToComplete = 0;
    let completedOnTime = 0;
    let completedLate = 0;
    let completedMilestones = 0;

    milestones.forEach(milestone => {
      if (milestone.completedAt) {
        completedMilestones++;
        
        if (milestone.dueDate) {
          const dueDate = DateTime.fromJSDate(milestone.dueDate);
          const completedDate = DateTime.fromJSDate(milestone.completedAt);
          
          if (completedDate <= dueDate) {
            completedOnTime++;
          } else {
            completedLate++;
          }

          // Calculate days to complete (from creation to completion)
          const createdDate = DateTime.fromJSDate(milestone.createdAt);
          const daysToComplete = completedDate.diff(createdDate, 'days').days;
          totalDaysToComplete += daysToComplete;
        }
      }
    });

    const averageDaysToComplete = completedMilestones > 0 ? 
      Math.round((totalDaysToComplete / completedMilestones) * 10) / 10 : 0;

    const onTimePercentage = completedMilestones > 0 ? 
      Math.round((completedOnTime / completedMilestones) * 100) : 0;

    return {
      completedMilestones,
      completedOnTime,
      completedLate,
      onTimePercentage,
      averageDaysToComplete,
      totalMilestones: milestones.length
    };
  }

  /**
   * Get upcoming deadlines
   */
  static getUpcomingDeadlines(milestones, daysAhead = 7) {
    const now = DateTime.now();
    const futureDate = now.plus({ days: daysAhead });

    return milestones
      .filter(milestone => {
        if (!milestone.dueDate || milestone.status === 'released') {
          return false;
        }

        const dueDate = DateTime.fromJSDate(milestone.dueDate);
        return dueDate >= now && dueDate <= futureDate;
      })
      .map(milestone => ({
        id: milestone.id,
        title: milestone.title,
        orderIndex: milestone.orderIndex,
        amount: milestone.amount,
        status: milestone.status,
        dueDate: milestone.dueDate,
        daysUntilDue: Math.ceil(DateTime.fromJSDate(milestone.dueDate).diff(now, 'days').days),
        isUrgent: DateTime.fromJSDate(milestone.dueDate).diff(now, 'days').days <= 2
      }))
      .sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate));
  }

  /**
   * Validate milestone sequence
   */
  static validateMilestoneSequence(milestones) {
    const issues = [];

    // Check if milestones are being completed in order
    let lastReleasedIndex = 0;
    
    milestones.forEach(milestone => {
      if (milestone.status === 'released') {
        if (milestone.orderIndex !== lastReleasedIndex + 1) {
          issues.push({
            type: 'out_of_order',
            milestoneId: milestone.id,
            message: `Milestone ${milestone.orderIndex} was released out of order`
          });
        }
        lastReleasedIndex = milestone.orderIndex;
      }
    });

    // Check for gaps in completion
    const completedMilestones = milestones.filter(m => 
      ['completed', 'approved', 'released'].includes(m.status)
    );

    if (completedMilestones.length > 0) {
      const maxCompletedIndex = Math.max(...completedMilestones.map(m => m.orderIndex));
      
      for (let i = 1; i < maxCompletedIndex; i++) {
        const milestone = milestones.find(m => m.orderIndex === i);
        if (milestone && !['completed', 'approved', 'released'].includes(milestone.status)) {
          issues.push({
            type: 'gap_in_sequence',
            milestoneId: milestone.id,
            message: `Milestone ${i} should be completed before milestone ${maxCompletedIndex}`
          });
        }
      }
    }

    return issues;
  }
}

export default MilestoneProgressTracker;

class NotificationTemplateManager {
  /**
   * Get notification template configuration
   */
  static getTemplate(type, subType = null) {
    const templateKey = subType || type;
    
    const templates = {
      // Escrow notifications
      'escrow_created': {
        title: 'New Escrow Created',
        emailTemplate: 'escrow_created',
        priority: 'high',
        channels: ['email', 'push', 'in_app']
      },
      'escrow_funded': {
        title: 'Escrow Funded',
        emailTemplate: 'escrow_funded',
        priority: 'high',
        channels: ['email', 'push', 'in_app']
      },
      'escrow_confirmed': {
        title: 'Escrow Confirmed',
        emailTemplate: 'escrow_confirmed',
        priority: 'medium',
        channels: ['email', 'push', 'in_app']
      },
      'escrow_released': {
        title: 'Escrow Released',
        emailTemplate: 'escrow_released',
        priority: 'high',
        channels: ['email', 'push', 'in_app']
      },
      'escrow_expired': {
        title: 'Escrow Expired',
        emailTemplate: 'escrow_expired',
        priority: 'high',
        channels: ['email', 'push', 'in_app']
      },
      'deadline_reminder_24h': {
        title: 'Escrow Deadline Reminder - 24 Hours',
        emailTemplate: 'escrow_deadline_reminder',
        priority: 'high',
        channels: ['email', 'push', 'in_app']
      },
      'deadline_reminder_6h': {
        title: 'Escrow Deadline Reminder - 6 Hours',
        emailTemplate: 'escrow_deadline_reminder',
        priority: 'urgent',
        channels: ['email', 'push', 'in_app']
      },
      'deadline_reminder_1h': {
        title: 'Escrow Deadline Reminder - 1 Hour',
        emailTemplate: 'escrow_deadline_reminder',
        priority: 'urgent',
        channels: ['email', 'push', 'in_app', 'sms']
      },

      // Milestone notifications
      'milestone_completed': {
        title: 'Milestone Completed',
        emailTemplate: 'milestone_completed',
        priority: 'high',
        channels: ['email', 'push', 'in_app']
      },
      'milestone_approved': {
        title: 'Milestone Approved',
        emailTemplate: 'milestone_approved',
        priority: 'medium',
        channels: ['email', 'push', 'in_app']
      },
      'milestone_rejected': {
        title: 'Milestone Rejected',
        emailTemplate: 'milestone_rejected',
        priority: 'high',
        channels: ['email', 'push', 'in_app']
      },

      // Recurring transfer notifications
      'recurring_transfer_created': {
        title: 'Recurring Transfer Created',
        emailTemplate: 'recurring_transfer_created',
        priority: 'medium',
        channels: ['email', 'in_app']
      },
      'recurring_transfer_executed': {
        title: 'Recurring Transfer Executed',
        emailTemplate: 'recurring_transfer_executed',
        priority: 'low',
        channels: ['email', 'in_app']
      },
      'recurring_transfer_failed': {
        title: 'Recurring Transfer Failed',
        emailTemplate: 'recurring_transfer_failed',
        priority: 'high',
        channels: ['email', 'push', 'in_app']
      },
      'recurring_transfer_paused': {
        title: 'Recurring Transfer Paused',
        emailTemplate: 'recurring_transfer_paused',
        priority: 'medium',
        channels: ['email', 'in_app']
      },
      'recurring_transfer_cancelled': {
        title: 'Recurring Transfer Cancelled',
        emailTemplate: 'recurring_transfer_cancelled',
        priority: 'medium',
        channels: ['email', 'in_app']
      },

      // Fundraising pool notifications
      'pool_activated': {
        title: 'Fundraising Pool Activated',
        emailTemplate: 'pool_activated',
        priority: 'medium',
        channels: ['email', 'in_app']
      },
      'pool_contribution_received': {
        title: 'New Contribution Received',
        emailTemplate: 'pool_contribution_received',
        priority: 'medium',
        channels: ['email', 'push', 'in_app']
      },
      'pool_target_reached': {
        title: 'Fundraising Target Reached',
        emailTemplate: 'pool_target_reached',
        priority: 'high',
        channels: ['email', 'push', 'in_app']
      },
      'pool_funds_distributed': {
        title: 'Funds Distributed',
        emailTemplate: 'pool_funds_distributed',
        priority: 'high',
        channels: ['email', 'push', 'in_app']
      },

      // Admin notifications
      'admin_escrow_dispute': {
        title: 'Escrow Dispute Reported',
        emailTemplate: 'admin_escrow_dispute',
        priority: 'urgent',
        channels: ['email', 'push', 'in_app']
      },
      'admin_high_value_escrow': {
        title: 'High Value Escrow Created',
        emailTemplate: 'admin_high_value_escrow',
        priority: 'medium',
        channels: ['email', 'in_app']
      },
      'admin_failed_transfers': {
        title: 'Multiple Failed Transfers',
        emailTemplate: 'admin_failed_transfers',
        priority: 'high',
        channels: ['email', 'in_app']
      }
    };

    return templates[templateKey] || {
      title: 'Notification',
      emailTemplate: 'generic_email',
      priority: 'medium',
      channels: ['in_app']
    };
  }

  /**
   * Get message template for notification type
   */
  static getMessage(type, data = {}) {
    const messages = {
      'escrow_created': `A new escrow ${data.escrowId} has been created for ${data.amount} ${data.currency}.`,
      'escrow_funded': `Escrow ${data.escrowId} has been funded with ${data.amount} ${data.currency}.`,
      'escrow_confirmed': `Escrow ${data.escrowId} has been confirmed by both parties.`,
      'escrow_released': `Escrow ${data.escrowId} has been released. ${data.amount} ${data.currency} transferred.`,
      'escrow_expired': `Escrow ${data.escrowId} has expired and been processed automatically.`,
      'deadline_reminder_24h': `Your escrow ${data.escrowId} expires in 24 hours. Please take action.`,
      'deadline_reminder_6h': `Your escrow ${data.escrowId} expires in 6 hours. Urgent action required.`,
      'deadline_reminder_1h': `Your escrow ${data.escrowId} expires in 1 hour. Immediate action required.`,
      
      'milestone_completed': `Milestone "${data.milestoneTitle}" in escrow ${data.escrowId} has been completed.`,
      'milestone_approved': `Milestone "${data.milestoneTitle}" in escrow ${data.escrowId} has been approved.`,
      'milestone_rejected': `Milestone "${data.milestoneTitle}" in escrow ${data.escrowId} has been rejected.`,
      
      'recurring_transfer_created': `Recurring transfer of ${data.amount} ${data.currency} to ${data.recipientName} has been set up.`,
      'recurring_transfer_executed': `Recurring transfer #${data.executionCount} of ${data.amount} ${data.currency} has been executed.`,
      'recurring_transfer_failed': `Recurring transfer failed: ${data.error}`,
      'recurring_transfer_paused': `Recurring transfer of ${data.amount} ${data.currency} has been paused.`,
      'recurring_transfer_cancelled': `Recurring transfer of ${data.amount} ${data.currency} has been cancelled.`,
      
      'pool_activated': `Your fundraising pool "${data.poolTitle}" is now active.`,
      'pool_contribution_received': `${data.contributorName} contributed ${data.amount} ${data.currency} to your pool.`,
      'pool_target_reached': `Your fundraising pool "${data.poolTitle}" has reached its target!`,
      'pool_funds_distributed': `${data.amount} ${data.currency} has been distributed from your pool.`,
      
      'admin_escrow_dispute': `Escrow ${data.escrowId} has been disputed and requires admin attention.`,
      'admin_high_value_escrow': `High value escrow ${data.escrowId} created for ${data.amount} ${data.currency}.`,
      'admin_failed_transfers': `${data.failedCount} recurring transfers have failed in the last hour.`
    };

    return messages[type] || 'You have a new notification.';
  }

  /**
   * Get navigation URL for notification type
   */
  static getNavigationUrl(type, data = {}) {
    const baseUrl = process.env.FRONTEND_URL || '';
    
    const routes = {
      'escrow_created': `/escrow/${data.escrowId}`,
      'escrow_funded': `/escrow/${data.escrowId}`,
      'escrow_confirmed': `/escrow/${data.escrowId}`,
      'escrow_released': `/escrow/${data.escrowId}`,
      'escrow_expired': `/escrow/${data.escrowId}`,
      'deadline_reminder_24h': `/escrow/${data.escrowId}`,
      'deadline_reminder_6h': `/escrow/${data.escrowId}`,
      'deadline_reminder_1h': `/escrow/${data.escrowId}`,
      
      'milestone_completed': `/escrow/${data.escrowId}/milestones`,
      'milestone_approved': `/escrow/${data.escrowId}/milestones`,
      'milestone_rejected': `/escrow/${data.escrowId}/milestones`,
      
      'recurring_transfer_created': `/recurring-transfers/${data.recurringId}`,
      'recurring_transfer_executed': `/recurring-transfers/${data.recurringId}`,
      'recurring_transfer_failed': `/recurring-transfers/${data.recurringId}`,
      'recurring_transfer_paused': `/recurring-transfers/${data.recurringId}`,
      'recurring_transfer_cancelled': `/recurring-transfers`,
      
      'pool_activated': `/fundraising/${data.poolId}`,
      'pool_contribution_received': `/fundraising/${data.poolId}`,
      'pool_target_reached': `/fundraising/${data.poolId}`,
      'pool_funds_distributed': `/fundraising/${data.poolId}`,
      
      'admin_escrow_dispute': `/admin/escrows/${data.escrowId}`,
      'admin_high_value_escrow': `/admin/escrows/${data.escrowId}`,
      'admin_failed_transfers': `/admin/recurring-transfers`
    };

    const route = routes[type] || '/dashboard';
    return `${baseUrl}${route}`;
  }

  /**
   * Check if notification should be sent based on user preferences
   */
  static shouldSendNotification(type, channel, userPreferences = {}) {
    // Default preferences if not provided
    const defaultPreferences = {
      email: {
        escrow: true,
        milestones: true,
        recurring_transfers: false,
        fundraising: true,
        admin: true
      },
      push: {
        escrow: true,
        milestones: true,
        recurring_transfers: false,
        fundraising: true,
        admin: true
      },
      sms: {
        escrow: false,
        milestones: false,
        recurring_transfers: false,
        fundraising: false,
        admin: false
      }
    };

    const preferences = { ...defaultPreferences, ...userPreferences };
    
    // Determine category from type
    let category = 'escrow';
    if (type.includes('milestone')) category = 'milestones';
    else if (type.includes('recurring')) category = 'recurring_transfers';
    else if (type.includes('pool')) category = 'fundraising';
    else if (type.includes('admin')) category = 'admin';

    return preferences[channel]?.[category] ?? true;
  }

  /**
   * Get email data for template
   */
  static getEmailData(type, data = {}) {
    const template = this.getTemplate(type);
    const baseData = {
      logo: process.env.APP_LOGO_URL || '',
      title: template.title,
      body: this.getMessage(type, data),
      actionUrl: this.getNavigationUrl(type, data),
      actionText: this.getActionText(type),
      action: true,
      ...data
    };

    // Add specific data based on type
    switch (type) {
      case 'deadline_reminder_24h':
      case 'deadline_reminder_6h':
      case 'deadline_reminder_1h':
        baseData.hoursRemaining = type.includes('24h') ? 24 : type.includes('6h') ? 6 : 1;
        break;
    }

    return baseData;
  }

  /**
   * Get action button text for notification type
   */
  static getActionText(type) {
    const actionTexts = {
      'escrow_created': 'View Escrow',
      'escrow_funded': 'Manage Escrow',
      'escrow_confirmed': 'View Details',
      'escrow_released': 'View Transaction',
      'escrow_expired': 'View Details',
      'deadline_reminder_24h': 'Take Action',
      'deadline_reminder_6h': 'Take Action',
      'deadline_reminder_1h': 'Take Action Now',
      
      'milestone_completed': 'Review Milestone',
      'milestone_approved': 'View Progress',
      'milestone_rejected': 'View Feedback',
      
      'recurring_transfer_created': 'View Transfer',
      'recurring_transfer_executed': 'View History',
      'recurring_transfer_failed': 'Fix Issue',
      'recurring_transfer_paused': 'Manage Transfer',
      'recurring_transfer_cancelled': 'View Transfers',
      
      'pool_activated': 'View Pool',
      'pool_contribution_received': 'View Pool',
      'pool_target_reached': 'Celebrate!',
      'pool_funds_distributed': 'View Details',
      
      'admin_escrow_dispute': 'Review Dispute',
      'admin_high_value_escrow': 'Review Escrow',
      'admin_failed_transfers': 'View Issues'
    };

    return actionTexts[type] || 'View Details';
  }

  /**
   * Get notification priority level
   */
  static getPriority(type) {
    const template = this.getTemplate(type);
    return template.priority;
  }

  /**
   * Get supported channels for notification type
   */
  static getChannels(type) {
    const template = this.getTemplate(type);
    return template.channels;
  }
}

export default NotificationTemplateManager;

import { DateTime } from 'luxon';
import EscrowMilestone from '#models/escrow_milestone';
import Escrow from '#models/escrow';
import EscrowNotification from '#models/escrow_notification';
import Transaction from '#models/transaction';
import User from '#models/user';
import Wallet from '#models/wallet';
import Currency from '#models/currency';
import formatPrecision from '#utils/format_precision';

class MilestoneService {
  /**
   * Complete a milestone (mark as completed by recipient)
   */
  async completeMilestone(milestoneId, userId, completionData) {
    const milestone = await EscrowMilestone.query()
      .where('id', milestoneId)
      .preload('escrow')
      .first();

    if (!milestone) {
      throw new Error('Milestone not found');
    }

    const escrow = milestone.escrow;

    // Validate authorization (only recipient can complete)
    if (escrow.recipientId !== userId) {
      throw new Error('Unauthorized: Only recipient can complete milestones');
    }

    if (milestone.status !== 'pending' && milestone.status !== 'in_progress') {
      throw new Error('Milestone cannot be completed in current status');
    }

    // Mark milestone as completed
    await milestone.markCompleted(
      completionData.completionNotes,
      completionData.evidence
    );

    // Notify sender about completion
    await EscrowNotification.createEscrowNotification(
      escrow.id,
      escrow.senderId,
      'milestone_completed',
      {
        milestoneId: milestone.id,
        milestoneTitle: milestone.title,
        orderIndex: milestone.orderIndex
      }
    );

    return milestone;
  }

  /**
   * Approve a milestone (by sender or recipient)
   */
  async approveMilestone(milestoneId, userId, userType) {
    const milestone = await EscrowMilestone.query()
      .where('id', milestoneId)
      .preload('escrow')
      .first();

    if (!milestone) {
      throw new Error('Milestone not found');
    }

    const escrow = milestone.escrow;

    // Validate authorization
    if (userType === 'sender' && escrow.senderId !== userId) {
      throw new Error('Unauthorized: Not the sender');
    }
    
    if (userType === 'recipient' && escrow.recipientId !== userId) {
      throw new Error('Unauthorized: Not the recipient');
    }

    if (milestone.status !== 'completed') {
      throw new Error('Milestone must be completed before approval');
    }

    // Approve based on user type
    if (userType === 'sender') {
      await milestone.approveBySender(userId);
    } else {
      await milestone.approveByRecipient(userId);
    }

    // Check if both parties have approved
    if (milestone.senderApproved && milestone.recipientApproved) {
      milestone.status = 'approved';
      await milestone.save();

      // Notify both parties
      await EscrowNotification.createEscrowNotification(
        escrow.id,
        escrow.senderId,
        'milestone_approved',
        {
          milestoneId: milestone.id,
          milestoneTitle: milestone.title,
          orderIndex: milestone.orderIndex
        }
      );
      await EscrowNotification.createEscrowNotification(
        escrow.id,
        escrow.recipientId,
        'milestone_approved',
        {
          milestoneId: milestone.id,
          milestoneTitle: milestone.title,
          orderIndex: milestone.orderIndex
        }
      );

      // Auto-release milestone if configured
      await this.releaseMilestone(milestone.id, null, 'automatic');
    }

    return milestone;
  }

  /**
   * Reject a milestone
   */
  async rejectMilestone(milestoneId, userId, rejectionReason) {
    const milestone = await EscrowMilestone.query()
      .where('id', milestoneId)
      .preload('escrow')
      .first();

    if (!milestone) {
      throw new Error('Milestone not found');
    }

    const escrow = milestone.escrow;

    // Only sender can reject milestones
    if (escrow.senderId !== userId) {
      throw new Error('Unauthorized: Only sender can reject milestones');
    }

    if (milestone.status !== 'completed') {
      throw new Error('Milestone must be completed before rejection');
    }

    // Reject milestone
    await milestone.reject(rejectionReason, userId);

    // Notify recipient about rejection
    await EscrowNotification.createEscrowNotification(
      escrow.id,
      escrow.recipientId,
      'milestone_rejected',
      {
        milestoneId: milestone.id,
        milestoneTitle: milestone.title,
        orderIndex: milestone.orderIndex,
        rejectionReason
      }
    );

    return milestone;
  }

  /**
   * Release milestone funds to recipient
   */
  async releaseMilestone(milestoneId, releasedByUserId = null, releaseType = 'manual') {
    const milestone = await EscrowMilestone.query()
      .where('id', milestoneId)
      .preload('escrow')
      .first();

    if (!milestone) {
      throw new Error('Milestone not found');
    }

    const escrow = milestone.escrow;

    if (!milestone.canBeReleased) {
      throw new Error('Milestone conditions not met for release');
    }

    // Get recipient's wallet
    const currency = await Currency.query().where('code', escrow.currencyCode).first();
    const recipientWallet = await Wallet.query()
      .where('user_id', escrow.recipientId)
      .where('currency_id', currency.id)
      .where('default', true)
      .first();

    if (!recipientWallet) {
      throw new Error('Recipient wallet not found');
    }

    // Calculate milestone release amount (considering recipient fees)
    const milestoneRecipientFee = (escrow.recipientFeeAmount * milestone.percentage) / 100;
    const releaseAmount = formatPrecision(milestone.amount - milestoneRecipientFee);

    // Add funds to recipient's wallet
    recipientWallet.balance = formatPrecision(recipientWallet.balance + releaseAmount);
    await recipientWallet.save();

    // Create milestone release transaction
    const fromData = {
      image: (await escrow.related('sender').query().preload('customer').first()).customer?.profileImage ?? '',
      label: (await escrow.related('sender').query().preload('customer').first()).customer?.name ?? '',
      email: escrow.sender?.email ?? '',
      currency: escrow.currencyCode
    };

    const toData = {
      image: (await escrow.related('recipient').query().preload('customer').first()).customer?.profileImage ?? '',
      label: (await escrow.related('recipient').query().preload('customer').first()).customer?.name ?? '',
      email: escrow.recipient?.email ?? '',
      currency: escrow.currencyCode
    };

    const transaction = await Transaction.create({
      type: 'milestone_release',
      from: fromData,
      to: toData,
      amount: releaseAmount,
      fee: milestoneRecipientFee,
      total: releaseAmount,
      status: 'completed',
      escrowId: escrow.id,
      userId: escrow.recipientId,
      metaData: {
        currency: escrow.currencyCode,
        trxAction: 'milestone_receive',
        escrowId: escrow.escrowId,
        milestoneId: milestone.id,
        milestoneTitle: milestone.title,
        orderIndex: milestone.orderIndex,
        releaseType
      }
    });

    // Release milestone
    await milestone.release(releasedByUserId);

    // Update escrow milestone progress
    await escrow.updateMilestoneProgress();

    // Check if all milestones are completed
    const allMilestones = await EscrowMilestone.query()
      .where('escrow_id', escrow.id)
      .orderBy('order_index');

    const releasedMilestones = allMilestones.filter(m => m.status === 'released');
    
    if (releasedMilestones.length === allMilestones.length) {
      // All milestones released, complete the escrow
      await escrow.release(releaseType, releasedByUserId);
    }

    return { milestone, transaction };
  }

  /**
   * Get milestone progress for an escrow
   */
  async getMilestoneProgress(escrowId) {
    const milestones = await EscrowMilestone.query()
      .where('escrow_id', escrowId)
      .orderBy('order_index');

    const progress = {
      totalMilestones: milestones.length,
      completedMilestones: milestones.filter(m => m.status === 'completed' || m.status === 'approved' || m.status === 'released').length,
      releasedMilestones: milestones.filter(m => m.status === 'released').length,
      pendingMilestones: milestones.filter(m => m.status === 'pending' || m.status === 'in_progress').length,
      rejectedMilestones: milestones.filter(m => m.status === 'rejected').length,
      progressPercentage: 0,
      releasedPercentage: 0,
      milestones: milestones.map(m => ({
        id: m.id,
        title: m.title,
        description: m.description,
        orderIndex: m.orderIndex,
        amount: m.amount,
        percentage: m.percentage,
        status: m.status,
        senderApproved: m.senderApproved,
        recipientApproved: m.recipientApproved,
        dueDate: m.dueDate,
        completedAt: m.completedAt,
        releasedAt: m.releasedAt,
        isOverdue: m.isOverdue,
        canBeApproved: m.canBeApproved,
        canBeReleased: m.canBeReleased
      }))
    };

    if (progress.totalMilestones > 0) {
      progress.progressPercentage = Math.round((progress.completedMilestones / progress.totalMilestones) * 100);
      progress.releasedPercentage = Math.round((progress.releasedMilestones / progress.totalMilestones) * 100);
    }

    return progress;
  }

  /**
   * Update milestone deliverables
   */
  async updateMilestoneDeliverables(milestoneId, userId, deliverables) {
    const milestone = await EscrowMilestone.query()
      .where('id', milestoneId)
      .preload('escrow')
      .first();

    if (!milestone) {
      throw new Error('Milestone not found');
    }

    const escrow = milestone.escrow;

    // Only sender can update deliverables
    if (escrow.senderId !== userId) {
      throw new Error('Unauthorized: Only sender can update deliverables');
    }

    milestone.deliverables = JSON.stringify(deliverables);
    await milestone.save();

    return milestone;
  }

  /**
   * Submit evidence for milestone completion
   */
  async submitMilestoneEvidence(milestoneId, userId, evidence) {
    const milestone = await EscrowMilestone.query()
      .where('id', milestoneId)
      .preload('escrow')
      .first();

    if (!milestone) {
      throw new Error('Milestone not found');
    }

    const escrow = milestone.escrow;

    // Only recipient can submit evidence
    if (escrow.recipientId !== userId) {
      throw new Error('Unauthorized: Only recipient can submit evidence');
    }

    milestone.evidence = JSON.stringify(evidence);
    await milestone.save();

    return milestone;
  }

  /**
   * Get overdue milestones
   */
  async getOverdueMilestones() {
    const now = DateTime.now();
    
    const milestones = await EscrowMilestone.query()
      .whereNotNull('due_date')
      .where('due_date', '<', now.toSQL())
      .whereNotIn('status', ['completed', 'approved', 'released', 'rejected'])
      .preload('escrow', (query) => {
        query.preload('sender').preload('recipient');
      });

    return milestones;
  }

  /**
   * Reset milestone to pending state
   */
  async resetMilestone(milestoneId, userId) {
    const milestone = await EscrowMilestone.query()
      .where('id', milestoneId)
      .preload('escrow')
      .first();

    if (!milestone) {
      throw new Error('Milestone not found');
    }

    const escrow = milestone.escrow;

    // Only sender can reset milestones
    if (escrow.senderId !== userId) {
      throw new Error('Unauthorized: Only sender can reset milestones');
    }

    if (milestone.status === 'released') {
      throw new Error('Cannot reset released milestone');
    }

    await milestone.reset();

    return milestone;
  }
}

export default new MilestoneService();

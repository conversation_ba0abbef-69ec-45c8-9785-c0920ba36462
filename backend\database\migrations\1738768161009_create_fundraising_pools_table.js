import { BaseSchema } from '@adonisjs/lucid/schema';

export default class extends BaseSchema {
  tableName = 'fundraising_pools';

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id');
      table.string('pool_id').notNullable().unique();
      
      // Pool creator/owner
      table
        .integer('creator_id')
        .unsigned()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .notNullable();
      
      // Pool details
      table.string('title').notNullable();
      table.text('description').nullable();
      table.string('category').nullable();
      table.json('tags').nullable();
      
      // Financial targets
      table.double('target_amount').notNullable();
      table.double('current_amount').defaultTo(0);
      table.string('currency_code').notNullable();
      table.double('minimum_contribution').defaultTo(0);
      table.double('maximum_contribution').nullable();
      
      // Timing
      table.timestamp('start_date').notNullable();
      table.timestamp('end_date').nullable();
      table.boolean('has_deadline').defaultTo(false);
      
      // Status and visibility
      table.enum('status', [
        'draft',
        'active',
        'paused',
        'completed',
        'cancelled',
        'expired'
      ]).defaultTo('draft');
      
      table.enum('visibility', ['public', 'private', 'invite_only']).defaultTo('public');
      table.boolean('allow_anonymous').defaultTo(true);
      
      // Progress and statistics
      table.integer('contributors_count').defaultTo(0);
      table.double('progress_percentage').defaultTo(0);
      table.timestamp('target_reached_at').nullable();
      
      // Fund distribution
      table.boolean('auto_distribute').defaultTo(true);
      table.enum('distribution_type', ['immediate', 'on_completion', 'manual']).defaultTo('on_completion');
      table.timestamp('distributed_at').nullable();
      table.boolean('funds_distributed').defaultTo(false);
      
      // Media and presentation
      table.string('cover_image').nullable();
      table.json('gallery').nullable();
      table.string('video_url').nullable();
      
      // Social features
      table.boolean('allow_comments').defaultTo(true);
      table.boolean('show_contributors').defaultTo(true);
      table.boolean('send_updates').defaultTo(true);
      
      // Fees and charges
      table.double('platform_fee_percentage').defaultTo(0);
      table.double('platform_fee_amount').defaultTo(0);
      
      // Metadata
      table.json('metadata').nullable();
      table.json('settings').nullable();
      
      table.timestamp('created_at');
      table.timestamp('updated_at');
      
      // Indexes
      table.index(['creator_id', 'status']);
      table.index(['status', 'visibility']);
      table.index(['category', 'status']);
      table.index(['end_date', 'status']);
      table.index(['created_at']);
    });
  }

  async down() {
    this.schema.dropTable(this.tableName);
  }
}

"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Escrow } from "@/types/escrow";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  CheckCircle, 
  XCircle, 
  DollarSign, 
  AlertTriangle,
  Clock,
  Shield
} from "lucide-react";

interface EscrowActionsProps {
  escrow: Escrow;
  userRole: 'sender' | 'recipient';
  onAction?: (action: string, escrowId: number, data?: any) => Promise<void>;
  isLoading?: boolean;
}

export function EscrowActions({ 
  escrow, 
  userRole, 
  onAction,
  isLoading = false 
}: EscrowActionsProps) {
  const { t } = useTranslation();
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [releaseDialogOpen, setReleaseDialogOpen] = useState(false);
  const [cancelReason, setCancelReason] = useState("");

  const canConfirm = escrow.status === 'pending' && 
    ((userRole === 'sender' && !escrow.senderConfirmed) || 
     (userRole === 'recipient' && !escrow.recipientConfirmed));

  const canRelease = escrow.status === 'active' && 
    userRole === 'sender' && 
    escrow.canBeReleased;

  const canCancel = escrow.canBeCancelled && 
    (userRole === 'sender' || escrow.status === 'pending');

  const handleConfirm = async () => {
    await onAction?.('confirm', escrow.id, { userType: userRole });
    setConfirmDialogOpen(false);
  };

  const handleRelease = async () => {
    await onAction?.('release', escrow.id);
    setReleaseDialogOpen(false);
  };

  const handleCancel = async () => {
    await onAction?.('cancel', escrow.id, { reason: cancelReason });
    setCancelDialogOpen(false);
    setCancelReason("");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Shield className="h-5 w-5" />
          <span>{t('Actions')}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Confirm Escrow */}
        {canConfirm && (
          <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full" variant="default">
                <CheckCircle className="h-4 w-4 mr-2" />
                {t('Confirm Escrow')}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t('Confirm Escrow')}</DialogTitle>
                <DialogDescription>
                  {t('Are you sure you want to confirm this escrow? This action cannot be undone.')}
                </DialogDescription>
              </DialogHeader>
              
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {userRole === 'sender' 
                    ? t('By confirming, you agree to fund this escrow and the funds will be held until completion.')
                    : t('By confirming, you agree to the terms and conditions of this escrow.')
                  }
                </AlertDescription>
              </Alert>

              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setConfirmDialogOpen(false)}
                  disabled={isLoading}
                >
                  {t('Cancel')}
                </Button>
                <Button 
                  onClick={handleConfirm}
                  disabled={isLoading}
                >
                  {isLoading ? t('Confirming...') : t('Confirm')}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* Release Funds */}
        {canRelease && (
          <Dialog open={releaseDialogOpen} onOpenChange={setReleaseDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full" variant="default">
                <DollarSign className="h-4 w-4 mr-2" />
                {t('Release Funds')}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t('Release Funds')}</DialogTitle>
                <DialogDescription>
                  {t('Are you sure you want to release the funds to the recipient?')}
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <Alert>
                  <DollarSign className="h-4 w-4" />
                  <AlertDescription>
                    {t('This will release {{amount}} {{currency}} to the recipient. This action cannot be undone.', {
                      amount: escrow.amount.toLocaleString(),
                      currency: escrow.currencyCode
                    })}
                  </AlertDescription>
                </Alert>

                {escrow.hasMilestones && (
                  <div className="text-sm text-muted-foreground">
                    <p>{t('Milestone Status:')}</p>
                    <p>{escrow.completedMilestones} of {escrow.totalMilestones} milestones completed</p>
                  </div>
                )}
              </div>

              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setReleaseDialogOpen(false)}
                  disabled={isLoading}
                >
                  {t('Cancel')}
                </Button>
                <Button 
                  onClick={handleRelease}
                  disabled={isLoading}
                >
                  {isLoading ? t('Releasing...') : t('Release Funds')}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* Cancel Escrow */}
        {canCancel && (
          <Dialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full" variant="destructive">
                <XCircle className="h-4 w-4 mr-2" />
                {t('Cancel Escrow')}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t('Cancel Escrow')}</DialogTitle>
                <DialogDescription>
                  {t('Are you sure you want to cancel this escrow? This action cannot be undone.')}
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    {escrow.status === 'active' 
                      ? t('Cancelling an active escrow will return the funds to the sender.')
                      : t('Cancelling this escrow will prevent it from becoming active.')
                    }
                  </AlertDescription>
                </Alert>

                <div className="space-y-2">
                  <Label htmlFor="cancelReason">{t('Reason for Cancellation')}</Label>
                  <Textarea
                    id="cancelReason"
                    value={cancelReason}
                    onChange={(e) => setCancelReason(e.target.value)}
                    placeholder={t('Please provide a reason for cancelling this escrow...')}
                    rows={3}
                  />
                </div>
              </div>

              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setCancelDialogOpen(false);
                    setCancelReason("");
                  }}
                  disabled={isLoading}
                >
                  {t('Keep Escrow')}
                </Button>
                <Button 
                  variant="destructive"
                  onClick={handleCancel}
                  disabled={isLoading}
                >
                  {isLoading ? t('Cancelling...') : t('Cancel Escrow')}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* Status Messages */}
        {escrow.status === 'pending' && !canConfirm && (
          <Alert>
            <Clock className="h-4 w-4" />
            <AlertDescription>
              {userRole === 'sender' && escrow.senderConfirmed && (
                t('Waiting for recipient confirmation.')
              )}
              {userRole === 'recipient' && escrow.recipientConfirmed && (
                t('Waiting for sender confirmation.')
              )}
            </AlertDescription>
          </Alert>
        )}

        {escrow.status === 'active' && !canRelease && userRole === 'sender' && (
          <Alert>
            <Clock className="h-4 w-4" />
            <AlertDescription>
              {escrow.hasMilestones 
                ? t('Waiting for milestones to be completed before funds can be released.')
                : t('Funds can be released when conditions are met.')
              }
            </AlertDescription>
          </Alert>
        )}

        {escrow.status === 'active' && userRole === 'recipient' && (
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              {escrow.hasMilestones 
                ? t('Complete the milestones to receive payment.')
                : t('The sender will release funds when satisfied with delivery.')
              }
            </AlertDescription>
          </Alert>
        )}

        {escrow.status === 'completed' && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              {t('This escrow has been completed successfully.')}
            </AlertDescription>
          </Alert>
        )}

        {escrow.status === 'cancelled' && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              {t('This escrow has been cancelled.')}
            </AlertDescription>
          </Alert>
        )}

        {escrow.status === 'expired' && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {t('This escrow has expired and may require admin intervention.')}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}

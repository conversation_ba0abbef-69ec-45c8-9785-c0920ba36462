import { BaseSchema } from '@adonisjs/lucid/schema';

export default class extends BaseSchema {
  tableName = 'recurring_transfers';

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id');
      table.string('recurring_id').notNullable().unique();
      
      // Parties involved
      table
        .integer('sender_id')
        .unsigned()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .notNullable();
      
      table
        .integer('recipient_id')
        .unsigned()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .notNullable();
      
      // Financial details
      table.double('amount').notNullable();
      table.double('fee').notNullable().defaultTo(0);
      table.string('currency_code').notNullable();
      
      // Schedule configuration
      table.enum('frequency', ['daily', 'weekly', 'monthly', 'yearly']).notNullable();
      table.integer('interval_value').defaultTo(1); // Every X days/weeks/months
      table.json('schedule_config').nullable(); // Additional schedule settings
      
      // Timing and limits
      table.timestamp('start_date').notNullable();
      table.timestamp('end_date').nullable();
      table.timestamp('next_execution').notNullable();
      table.integer('max_occurrences').nullable();
      table.integer('executed_count').defaultTo(0);
      
      // Status and control
      table.enum('status', [
        'active',
        'paused',
        'completed',
        'cancelled',
        'failed'
      ]).defaultTo('active');
      
      table.boolean('auto_retry').defaultTo(true);
      table.integer('retry_count').defaultTo(0);
      table.integer('max_retries').defaultTo(3);
      
      // Metadata
      table.text('description').nullable();
      table.json('metadata').nullable();
      
      // Last execution tracking
      table.timestamp('last_executed_at').nullable();
      table.enum('last_execution_status', ['success', 'failed', 'skipped']).nullable();
      table.text('last_execution_error').nullable();
      table.integer('last_transaction_id').unsigned().nullable();
      
      // Notifications
      table.boolean('notify_sender').defaultTo(true);
      table.boolean('notify_recipient').defaultTo(true);
      table.boolean('notify_on_failure').defaultTo(true);
      
      table.timestamp('created_at');
      table.timestamp('updated_at');
      
      // Indexes
      table.index(['sender_id', 'status']);
      table.index(['recipient_id', 'status']);
      table.index(['status', 'next_execution']);
      table.index(['frequency', 'status']);
      table.index(['next_execution']);
    });
  }

  async down() {
    this.schema.dropTable(this.tableName);
  }
}

"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { IMilestone } from "@/types/milestone";
import { MilestoneCard } from "./MilestoneCard";
import { MilestoneProgress } from "./MilestoneProgress";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Target, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  TrendingUp,
  Calendar
} from "lucide-react";

interface MilestoneListProps {
  escrowId: number;
  milestones: IMilestone[];
  userRole: 'sender' | 'recipient';
  onAction?: (action: string, escrowId: number, data?: any) => Promise<void>;
  isLoading?: boolean;
}

export function MilestoneList({ 
  escrowId, 
  milestones, 
  userRole, 
  onAction,
  isLoading = false 
}: MilestoneListProps) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("all");

  // Filter milestones based on active tab
  const getFilteredMilestones = () => {
    switch (activeTab) {
      case 'pending':
        return milestones.filter(m => m.status === 'pending');
      case 'in_progress':
        return milestones.filter(m => m.status === 'in_progress');
      case 'completed':
        return milestones.filter(m => m.status === 'completed');
      case 'approved':
        return milestones.filter(m => m.status === 'approved');
      case 'released':
        return milestones.filter(m => m.status === 'released');
      case 'overdue':
        return milestones.filter(m => m.isOverdue);
      default:
        return milestones;
    }
  };

  const filteredMilestones = getFilteredMilestones();

  // Calculate statistics
  const stats = {
    total: milestones.length,
    pending: milestones.filter(m => m.status === 'pending').length,
    inProgress: milestones.filter(m => m.status === 'in_progress').length,
    completed: milestones.filter(m => m.status === 'completed').length,
    approved: milestones.filter(m => m.status === 'approved').length,
    released: milestones.filter(m => m.status === 'released').length,
    overdue: milestones.filter(m => m.isOverdue).length,
  };

  const getTabBadge = (count: number) => {
    if (count === 0) return null;
    return (
      <Badge variant="secondary" className="ml-2 text-xs">
        {count}
      </Badge>
    );
  };

  if (milestones.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Target className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">{t('No Milestones')}</h3>
          <p className="text-muted-foreground text-center">
            {t('This escrow does not have any milestones defined.')}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Progress Overview */}
      <MilestoneProgress milestones={milestones} />

      {/* Overdue Alert */}
      {stats.overdue > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {t('{{count}} milestone(s) are overdue and require immediate attention.', {
              count: stats.overdue
            })}
          </AlertDescription>
        </Alert>
      )}

      {/* Milestone Tabs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>{t('Milestones')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-7">
              <TabsTrigger value="all" className="text-xs">
                {t('All')}
                {getTabBadge(stats.total)}
              </TabsTrigger>
              <TabsTrigger value="pending" className="text-xs">
                {t('Pending')}
                {getTabBadge(stats.pending)}
              </TabsTrigger>
              <TabsTrigger value="in_progress" className="text-xs">
                {t('In Progress')}
                {getTabBadge(stats.inProgress)}
              </TabsTrigger>
              <TabsTrigger value="completed" className="text-xs">
                {t('Completed')}
                {getTabBadge(stats.completed)}
              </TabsTrigger>
              <TabsTrigger value="approved" className="text-xs">
                {t('Approved')}
                {getTabBadge(stats.approved)}
              </TabsTrigger>
              <TabsTrigger value="released" className="text-xs">
                {t('Released')}
                {getTabBadge(stats.released)}
              </TabsTrigger>
              <TabsTrigger value="overdue" className="text-xs">
                {t('Overdue')}
                {getTabBadge(stats.overdue)}
              </TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-6">
              {filteredMilestones.length === 0 ? (
                <div className="text-center py-8">
                  <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                    {activeTab === 'pending' && <Clock className="h-8 w-8 text-muted-foreground" />}
                    {activeTab === 'completed' && <CheckCircle className="h-8 w-8 text-muted-foreground" />}
                    {activeTab === 'overdue' && <AlertTriangle className="h-8 w-8 text-muted-foreground" />}
                    {activeTab === 'all' && <Target className="h-8 w-8 text-muted-foreground" />}
                    {!['pending', 'completed', 'overdue', 'all'].includes(activeTab) && (
                      <Target className="h-8 w-8 text-muted-foreground" />
                    )}
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    {t('No {{status}} milestones', { status: t(activeTab) })}
                  </h3>
                  <p className="text-muted-foreground">
                    {activeTab === 'pending' && t('No milestones are currently pending.')}
                    {activeTab === 'completed' && t('No milestones have been completed yet.')}
                    {activeTab === 'overdue' && t('No milestones are overdue.')}
                    {activeTab === 'all' && t('No milestones found.')}
                    {!['pending', 'completed', 'overdue', 'all'].includes(activeTab) && 
                      t('No milestones match this status.')
                    }
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredMilestones.map((milestone) => (
                    <MilestoneCard
                      key={milestone.id}
                      milestone={milestone}
                      userRole={userRole}
                      onAction={onAction}
                      isLoading={isLoading}
                    />
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">{t('Progress')}</p>
                <p className="text-lg font-semibold">
                  {Math.round((stats.released / stats.total) * 100)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">{t('Completed')}</p>
                <p className="text-lg font-semibold">
                  {stats.completed + stats.approved + stats.released}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm text-muted-foreground">{t('Pending')}</p>
                <p className="text-lg font-semibold">
                  {stats.pending + stats.inProgress}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm text-muted-foreground">{t('Overdue')}</p>
                <p className="text-lg font-semibold">{stats.overdue}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

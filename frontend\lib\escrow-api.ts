import { 
  Create<PERSON>crowD<PERSON>, 
  IEscrow, 
  Escrow<PERSON><PERSON><PERSON>,
  EscrowStatistics 
} from "@/types/escrow";
import { 
  CreateMilestoneData, 
  IMilestone,
  MilestoneProgress 
} from "@/types/milestone";
import { 
  CreateRecurringTransferData, 
  IRecurringTransfer,
  RecurringTransferFilters,
  SchedulePreview 
} from "@/types/recurring-transfer";
import { 
  CreatePoolData, 
  IFundraisingPool,
  ContributeToPoolData,
  PoolFilters 
} from "@/types/fundraising-pool";
import {
  EscrowListResponse,
  EscrowDetailResponse,
  CreateEscrowResponse,
  EscrowStatisticsResponse,
  MilestoneListResponse,
  RecurringTransferListResponse,
  FundraisingPoolListResponse,
  ApiErrorResponse
} from "@/types/api-responses";

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';

class EscrowApiClient {
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API request error:', error);
      throw error;
    }
  }

  // Escrow API methods
  async getEscrows(filters?: EscrowFilters): Promise<EscrowListResponse> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, String(value));
        }
      });
    }
    
    return this.request<EscrowListResponse>(
      `/escrows?${params.toString()}`
    );
  }

  async getEscrow(id: number): Promise<EscrowDetailResponse> {
    return this.request<EscrowDetailResponse>(`/escrows/${id}`);
  }

  async createEscrow(data: CreateEscrowData): Promise<CreateEscrowResponse> {
    return this.request<CreateEscrowResponse>('/escrows', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async confirmEscrow(id: number, userType: 'sender' | 'recipient'): Promise<EscrowDetailResponse> {
    return this.request<EscrowDetailResponse>(`/escrows/${id}/confirm`, {
      method: 'POST',
      body: JSON.stringify({ userType }),
    });
  }

  async releaseEscrow(id: number): Promise<EscrowDetailResponse> {
    return this.request<EscrowDetailResponse>(`/escrows/${id}/release`, {
      method: 'POST',
    });
  }

  async cancelEscrow(id: number, reason: string): Promise<EscrowDetailResponse> {
    return this.request<EscrowDetailResponse>(`/escrows/${id}/cancel`, {
      method: 'POST',
      body: JSON.stringify({ reason }),
    });
  }

  async getEscrowStatistics(): Promise<EscrowStatisticsResponse> {
    return this.request<EscrowStatisticsResponse>('/escrows/statistics');
  }

  // Milestone API methods
  async getMilestones(escrowId: number): Promise<MilestoneListResponse> {
    return this.request<MilestoneListResponse>(`/escrows/${escrowId}/milestones`);
  }

  async completeMilestone(
    milestoneId: number, 
    data: { completionNotes: string; evidence: any[] }
  ): Promise<any> {
    return this.request(`/milestones/${milestoneId}/complete`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async approveMilestone(milestoneId: number, userType: 'sender' | 'recipient'): Promise<any> {
    return this.request(`/milestones/${milestoneId}/approve`, {
      method: 'POST',
      body: JSON.stringify({ userType }),
    });
  }

  async rejectMilestone(milestoneId: number, rejectionReason: string): Promise<any> {
    return this.request(`/milestones/${milestoneId}/reject`, {
      method: 'POST',
      body: JSON.stringify({ rejectionReason }),
    });
  }

  async releaseMilestone(milestoneId: number): Promise<any> {
    return this.request(`/milestones/${milestoneId}/release`, {
      method: 'POST',
    });
  }

  // Recurring Transfer API methods
  async getRecurringTransfers(filters?: RecurringTransferFilters): Promise<RecurringTransferListResponse> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, String(value));
        }
      });
    }
    
    return this.request<RecurringTransferListResponse>(
      `/recurring-transfers?${params.toString()}`
    );
  }

  async createRecurringTransfer(data: CreateRecurringTransferData): Promise<any> {
    return this.request('/recurring-transfers', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async pauseRecurringTransfer(id: number): Promise<any> {
    return this.request(`/recurring-transfers/${id}/pause`, {
      method: 'POST',
    });
  }

  async resumeRecurringTransfer(id: number): Promise<any> {
    return this.request(`/recurring-transfers/${id}/resume`, {
      method: 'POST',
    });
  }

  async cancelRecurringTransfer(id: number): Promise<any> {
    return this.request(`/recurring-transfers/${id}/cancel`, {
      method: 'POST',
    });
  }

  async getSchedulePreview(data: Partial<CreateRecurringTransferData>): Promise<SchedulePreview> {
    return this.request('/recurring-transfers/preview', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Fundraising Pool API methods
  async getFundraisingPools(filters?: PoolFilters): Promise<FundraisingPoolListResponse> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, String(value));
        }
      });
    }
    
    return this.request<FundraisingPoolListResponse>(
      `/fundraising-pools?${params.toString()}`
    );
  }

  async createFundraisingPool(data: CreatePoolData): Promise<any> {
    return this.request('/fundraising-pools', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async contributeToPool(poolId: number, data: ContributeToPoolData): Promise<any> {
    return this.request(`/fundraising-pools/${poolId}/contribute`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async activatePool(poolId: number): Promise<any> {
    return this.request(`/fundraising-pools/${poolId}/activate`, {
      method: 'POST',
    });
  }

  async pausePool(poolId: number): Promise<any> {
    return this.request(`/fundraising-pools/${poolId}/pause`, {
      method: 'POST',
    });
  }

  async distributePoolFunds(poolId: number): Promise<any> {
    return this.request(`/fundraising-pools/${poolId}/distribute`, {
      method: 'POST',
    });
  }

  // Admin API methods
  async getAdminDashboard(): Promise<any> {
    return this.request('/admin/dashboard');
  }

  async forceReleaseEscrow(escrowId: number, reason: string): Promise<any> {
    return this.request(`/admin/escrows/${escrowId}/force-release`, {
      method: 'POST',
      body: JSON.stringify({ reason }),
    });
  }

  async forceCancelEscrow(escrowId: number, reason: string): Promise<any> {
    return this.request(`/admin/escrows/${escrowId}/force-cancel`, {
      method: 'POST',
      body: JSON.stringify({ reason }),
    });
  }

  async retryFailedTransfers(transferIds: number[]): Promise<any> {
    return this.request('/admin/recurring-transfers/retry', {
      method: 'POST',
      body: JSON.stringify({ transferIds }),
    });
  }
}

// Create singleton instance
export const escrowApi = new EscrowApiClient();

// Error handling utilities
export function isApiError(error: any): error is ApiErrorResponse {
  return error && typeof error.success === 'boolean' && error.success === false;
}

export function getErrorMessage(error: any): string {
  if (isApiError(error)) {
    return error.message;
  }
  if (error instanceof Error) {
    return error.message;
  }
  return 'An unexpected error occurred';
}

// Response utilities
export function extractData<T>(response: { success: boolean; data?: T }): T | null {
  return response.success ? response.data || null : null;
}

export function extractPaginatedData<T>(response: { success: boolean; data?: { data: T[] } }): T[] {
  return response.success && response.data ? response.data.data : [];
}

// Hook utilities for React components
export const useEscrowApi = () => {
  return {
    api: escrowApi,
    isApiError,
    getErrorMessage,
    extractData,
    extractPaginatedData,
  };
};

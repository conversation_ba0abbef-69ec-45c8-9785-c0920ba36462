import { DateTime } from 'luxon';
import { BaseModel, column, belongsTo, hasMany, beforeCreate, computed } from '@adonisjs/lucid/orm';
import { generateNanoId } from '../utils/generate_unique_id.js';
import User from './user.js';
import Transaction from './transaction.js';

export default class RecurringTransfer extends BaseModel {
  @column({ isPrimary: true })
  id;

  @column()
  recurringId;

  @column()
  senderId;

  @column()
  recipientId;

  @column()
  amount;

  @column()
  fee;

  @column()
  currencyCode;

  @column()
  frequency;

  @column()
  intervalValue;

  @column()
  scheduleConfig;

  @column.dateTime()
  startDate;

  @column.dateTime()
  endDate;

  @column.dateTime()
  nextExecution;

  @column()
  maxOccurrences;

  @column()
  executedCount;

  @column()
  status;

  @column()
  autoRetry;

  @column()
  retryCount;

  @column()
  maxRetries;

  @column()
  description;

  @column()
  metadata;

  @column.dateTime()
  lastExecutedAt;

  @column()
  lastExecutionStatus;

  @column()
  lastExecutionError;

  @column()
  lastTransactionId;

  @column()
  notifySender;

  @column()
  notifyRecipient;

  @column()
  notifyOnFailure;

  @column.dateTime({ autoCreate: true })
  createdAt;

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  updatedAt;

  // Relationships
  @belongsTo(() => User, { foreignKey: 'senderId' })
  sender;

  @belongsTo(() => User, { foreignKey: 'recipientId' })
  recipient;

  @belongsTo(() => Transaction, { foreignKey: 'lastTransactionId' })
  lastTransaction;

  @hasMany(() => Transaction)
  transactions;

  // Computed properties
  @computed()
  get scheduleConfigParsed() {
    return typeof this.scheduleConfig !== 'object' 
      ? JSON.parse(this?.scheduleConfig || '{}') 
      : this?.scheduleConfig;
  }

  @computed()
  get metadataParsed() {
    return typeof this.metadata !== 'object' 
      ? JSON.parse(this?.metadata || '{}') 
      : this?.metadata;
  }

  @computed()
  get isActive() {
    return this.status === 'active';
  }

  @computed()
  get isCompleted() {
    if (this.status === 'completed' || this.status === 'cancelled') return true;
    if (this.maxOccurrences && this.executedCount >= this.maxOccurrences) return true;
    if (this.endDate && DateTime.now() > DateTime.fromJSDate(this.endDate)) return true;
    return false;
  }

  @computed()
  get nextExecutionFormatted() {
    if (!this.nextExecution) return null;
    return DateTime.fromJSDate(this.nextExecution).toFormat('yyyy-MM-dd HH:mm:ss');
  }

  @computed()
  get progressPercentage() {
    if (!this.maxOccurrences) return 0;
    return Math.round((this.executedCount / this.maxOccurrences) * 100);
  }

  @computed()
  get remainingExecutions() {
    if (!this.maxOccurrences) return null;
    return Math.max(0, this.maxOccurrences - this.executedCount);
  }

  @computed()
  get shouldExecute() {
    if (!this.isActive) return false;
    if (this.isCompleted) return false;
    return DateTime.now() >= DateTime.fromJSDate(this.nextExecution);
  }

  // Hooks
  @beforeCreate()
  static assignRecurringId(recurringTransfer) {
    recurringTransfer.recurringId = generateNanoId(16);
  }

  // Instance methods
  calculateNextExecution() {
    const current = this.nextExecution ? DateTime.fromJSDate(this.nextExecution) : DateTime.now();
    let next;

    switch (this.frequency) {
      case 'daily':
        next = current.plus({ days: this.intervalValue });
        break;
      case 'weekly':
        next = current.plus({ weeks: this.intervalValue });
        break;
      case 'monthly':
        next = current.plus({ months: this.intervalValue });
        break;
      case 'yearly':
        next = current.plus({ years: this.intervalValue });
        break;
      default:
        throw new Error(`Invalid frequency: ${this.frequency}`);
    }

    return next.toJSDate();
  }

  async updateNextExecution() {
    this.nextExecution = this.calculateNextExecution();
    await this.save();
  }

  async markExecuted(transactionId = null, status = 'success', error = null) {
    this.executedCount += 1;
    this.lastExecutedAt = DateTime.now();
    this.lastExecutionStatus = status;
    this.lastTransactionId = transactionId;
    this.lastExecutionError = error;
    this.retryCount = 0; // Reset retry count on successful execution

    // Update next execution time
    await this.updateNextExecution();

    // Check if completed
    if (this.isCompleted) {
      this.status = 'completed';
    }

    await this.save();
  }

  async markFailed(error) {
    this.lastExecutionStatus = 'failed';
    this.lastExecutionError = error;
    this.retryCount += 1;

    // If max retries reached, pause the recurring transfer
    if (this.retryCount >= this.maxRetries) {
      this.status = 'failed';
    }

    await this.save();
  }

  async pause() {
    this.status = 'paused';
    await this.save();
  }

  async resume() {
    if (this.status === 'paused') {
      this.status = 'active';
      // Recalculate next execution if needed
      if (DateTime.now() > DateTime.fromJSDate(this.nextExecution)) {
        await this.updateNextExecution();
      }
      await this.save();
    }
  }

  async cancel() {
    this.status = 'cancelled';
    await this.save();
  }

  async resetRetryCount() {
    this.retryCount = 0;
    if (this.status === 'failed') {
      this.status = 'active';
    }
    await this.save();
  }
}

"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { RecurringTransferCard } from "./RecurringTransferCard";
import { RecurringTransferFilters } from "./RecurringTransferFilters";
import { RecurringTransfer, RecurringTransferFilters as IRecurringTransferFilters } from "@/types/recurring-transfer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Plus, RefreshCw, RotateCcw } from "lucide-react";
import Link from "next/link";

interface RecurringTransferListProps {
  transfers: RecurringTransfer[];
  isLoading?: boolean;
  error?: string;
  currentUserId: number;
  onFiltersChange?: (filters: IRecurringTransferFilters) => void;
  onAction?: (action: string, transferId: number, data?: any) => void;
  onRefresh?: () => void;
  showCreateButton?: boolean;
  title?: string;
}

export function RecurringTransferList({
  transfers,
  isLoading = false,
  error,
  currentUserId,
  onFiltersChange,
  onAction,
  onRefresh,
  showCreateButton = true,
  title = "Recurring Transfers"
}: RecurringTransferListProps) {
  const { t } = useTranslation();
  const [filters, setFilters] = useState<IRecurringTransferFilters>({});

  const handleFiltersChange = (newFilters: IRecurringTransferFilters) => {
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const getUserRole = (transfer: RecurringTransfer): 'sender' | 'recipient' => {
    return transfer.senderId === currentUserId ? 'sender' : 'recipient';
  };

  const filteredTransfers = transfers.filter(transfer => {
    if (filters.status && transfer.status !== filters.status) return false;
    if (filters.type) {
      const userRole = getUserRole(transfer);
      if (filters.type === 'sent' && userRole !== 'sender') return false;
      if (filters.type === 'received' && userRole !== 'recipient') return false;
    }
    if (filters.frequency && transfer.frequency !== filters.frequency) return false;
    return true;
  });

  // Calculate statistics
  const stats = {
    total: transfers.length,
    active: transfers.filter(t => t.status === 'active').length,
    paused: transfers.filter(t => t.status === 'paused').length,
    failed: transfers.filter(t => t.status === 'failed').length,
    completed: transfers.filter(t => t.status === 'completed').length,
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold">{t(title)}</CardTitle>
            <div className="flex items-center space-x-2">
              {onRefresh && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRefresh}
                  disabled={isLoading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  {t('Refresh')}
                </Button>
              )}
              {showCreateButton && (
                <Button asChild>
                  <Link href="/recurring-transfers/create">
                    <Plus className="h-4 w-4 mr-2" />
                    {t('Create Transfer')}
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <RecurringTransferFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
          />
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold">{stats.total}</p>
              <p className="text-sm text-muted-foreground">{t('Total')}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{stats.active}</p>
              <p className="text-sm text-muted-foreground">{t('Active')}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">{stats.paused}</p>
              <p className="text-sm text-muted-foreground">{t('Paused')}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">{stats.failed}</p>
              <p className="text-sm text-muted-foreground">{t('Failed')}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{stats.completed}</p>
              <p className="text-sm text-muted-foreground">{t('Completed')}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Failed Transfers Alert */}
      {stats.failed > 0 && (
        <Alert variant="destructive">
          <RotateCcw className="h-4 w-4" />
          <AlertDescription>
            {t('{{count}} recurring transfer(s) have failed and may need attention.', {
              count: stats.failed
            })}
          </AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-8 w-8" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-4 w-24" />
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-2 w-full" />
                <div className="grid grid-cols-2 gap-4">
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && filteredTransfers.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-center space-y-4">
              <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center">
                <RotateCcw className="h-12 w-12 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">{t('No recurring transfers found')}</h3>
                <p className="text-muted-foreground">
                  {transfers.length === 0 
                    ? t('You haven\'t created any recurring transfers yet.')
                    : t('No transfers match your current filters.')
                  }
                </p>
              </div>
              {showCreateButton && transfers.length === 0 && (
                <Button asChild>
                  <Link href="/recurring-transfers/create">
                    <Plus className="h-4 w-4 mr-2" />
                    {t('Create Your First Transfer')}
                  </Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transfer Grid */}
      {!isLoading && filteredTransfers.length > 0 && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredTransfers.map((transfer) => (
            <RecurringTransferCard
              key={transfer.id}
              transfer={transfer}
              userRole={getUserRole(transfer)}
              onAction={onAction}
            />
          ))}
        </div>
      )}

      {/* Results Summary */}
      {!isLoading && filteredTransfers.length > 0 && (
        <div className="text-center text-sm text-muted-foreground">
          {t('Showing {{count}} of {{total}} recurring transfers', {
            count: filteredTransfers.length,
            total: transfers.length
          })}
        </div>
      )}
    </div>
  );
}

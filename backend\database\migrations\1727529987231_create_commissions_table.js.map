{"version": 3, "file": "1727529987231_create_commissions_table.js", "sourceRoot": "", "sources": ["../../../database/migrations/1727529987231_create_commissions_table.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AAEpD,MAAM,CAAC,OAAO,MAAO,SAAQ,UAAU;IAC3B,SAAS,GAAG,aAAa,CAAC;IAEpC,KAAK,CAAC,EAAE;QACN,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;YAChD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACvB,KAAK;iBACF,OAAO,CAAC,UAAU,CAAC;iBACnB,QAAQ,EAAE;iBACV,UAAU,CAAC,IAAI,CAAC;iBAChB,OAAO,CAAC,QAAQ,CAAC;iBACjB,QAAQ,CAAC,SAAS,CAAC;iBACnB,QAAQ,EAAE,CAAC;YACd,KAAK;iBACF,OAAO,CAAC,gBAAgB,CAAC;iBACzB,QAAQ,EAAE;iBACV,UAAU,CAAC,IAAI,CAAC;iBAChB,OAAO,CAAC,cAAc,CAAC;iBACvB,QAAQ,CAAC,UAAU,CAAC;iBACpB,QAAQ,EAAE,CAAC;YACd,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACrC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;YACzD,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAC9B,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;CACF"}
// Unified Contact Types for PaySnap - Resolves conflicts between contact.ts and contacts.ts
import { User } from "@/types/auth";

// Legacy contact class for backward compatibility (from contacts.ts)
export class CustomerContact {
  id: number;
  userId: number;
  contactId: number;
  createdAt: Date;
  updatedAt: Date;
  contact: User;

  constructor(data: any) {
    this.id = data?.id;
    this.userId = data?.userId;
    this.contactId = data?.contactId;
    this.createdAt = new Date(data?.createdAt);
    this.updatedAt = new Date(data?.updatedAt);
    this.contact = new User(data?.contact);
  }
}

// Enhanced contact interface (from contact.ts)
export interface IContact {
  id: number;
  name: string;
  email: string;
  avatar?: string;
  status: ContactStatus;
  isVerified: boolean;
  verificationLevel: VerificationLevel;
  lastTransactionAt?: Date;
  totalTransactions: number;
  totalVolume: number;
  riskScore: number; // 0-100
  tags: string[];
  capabilities: ContactCapabilities;
  relationshipType: RelationshipType;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Type definitions
export type ContactStatus = 'active' | 'blocked' | 'pending';
export type VerificationLevel = 'basic' | 'enhanced' | 'premium';
export type RelationshipType = 'contact' | 'frequent' | 'new' | 'blocked';
export type ContactContext = 'escrow' | 'recurring-transfer' | 'fundraising-pool' | 'general';

export interface ContactCapabilities {
  canReceiveEscrow: boolean;
  canReceiveRecurringTransfer: boolean;
  canReceiveFundraisingContribution: boolean;
}

export interface ContactFilters {
  search?: string;
  status?: ContactStatus;
  verificationLevel?: VerificationLevel;
  canReceiveEscrow?: boolean;
  canReceiveRecurringTransfer?: boolean;
  canReceiveFundraisingContribution?: boolean;
  riskScoreMax?: number;
  relationshipType?: RelationshipType;
  tags?: string[];
  limit?: number;
  offset?: number;
  sortBy?: 'name' | 'lastTransactionAt' | 'totalVolume' | 'riskScore';
  sortOrder?: 'asc' | 'desc';
}

export interface ContactSearchSuggestion {
  id: number;
  name: string;
  email: string;
  avatar?: string;
  verificationLevel: VerificationLevel;
  riskScore: number;
  relationshipType: RelationshipType;
}

export interface CreateContactData {
  name: string;
  email: string;
  notes?: string;
  tags?: string[];
}

// API Response Types
export interface ContactListResponse {
  success: boolean;
  data: {
    contacts: IContact[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
    filters: ContactFilters;
  };
  message?: string;
}

export interface ContactSearchResponse {
  success: boolean;
  data: {
    contacts: IContact[];
    suggestions: ContactSearchSuggestion[];
    recentContacts: IContact[];
    frequentContacts: IContact[];
  };
  message?: string;
}

export interface ContactDetailResponse {
  success: boolean;
  data: IContact;
  message?: string;
}

// Real-time Contact Updates
export interface ContactStatusUpdate {
  contactId: number;
  status: ContactStatus;
  timestamp: Date;
}

export interface ContactVerificationUpdate {
  contactId: number;
  verificationLevel: VerificationLevel;
  isVerified: boolean;
  timestamp: Date;
}

export interface ContactRiskScoreUpdate {
  contactId: number;
  riskScore: number;
  reason: string;
  timestamp: Date;
}

// Utility function to convert legacy to enhanced format
export function convertLegacyContact(legacy: CustomerContact): IContact {
  return {
    id: legacy.contact.id,
    name: legacy.contact.name || legacy.contact.email,
    email: legacy.contact.email,
    avatar: legacy.contact.avatar,
    status: 'active', // Default for legacy
    isVerified: false, // Default for legacy
    verificationLevel: 'basic', // Default for legacy
    lastTransactionAt: legacy.updatedAt,
    totalTransactions: 0, // Default for legacy
    totalVolume: 0, // Default for legacy
    riskScore: 0, // Default for legacy
    tags: [],
    capabilities: {
      canReceiveEscrow: true,
      canReceiveRecurringTransfer: true,
      canReceiveFundraisingContribution: true,
    },
    relationshipType: 'contact',
    createdAt: legacy.createdAt,
    updatedAt: legacy.updatedAt,
  };
}

// Utility function to convert enhanced to legacy format (for backward compatibility)
export function convertEnhancedToLegacy(enhanced: IContact): any {
  return {
    id: enhanced.id,
    contactId: enhanced.id,
    userId: enhanced.id,
    contact: {
      customer: {
        id: enhanced.id,
        name: enhanced.name,
        email: enhanced.email,
        profileImage: enhanced.avatar,
      },
      email: enhanced.email,
    },
    createdAt: enhanced.createdAt,
    updatedAt: enhanced.updatedAt,
  };
}

// Type guard to check if contact is enhanced format
export function isEnhancedContact(contact: any): contact is IContact {
  return contact && 
         typeof contact.id === 'number' &&
         typeof contact.name === 'string' &&
         typeof contact.email === 'string' &&
         'status' in contact &&
         'verificationLevel' in contact &&
         'capabilities' in contact;
}

// Type guard to check if contact is legacy format
export function isLegacyContact(contact: any): contact is CustomerContact {
  return contact && 
         typeof contact.id === 'number' &&
         'contactId' in contact &&
         'contact' in contact &&
         contact.contact?.customer;
}

// Unified contact type that can handle both formats
export type UnifiedContact = IContact | CustomerContact | any;

// Helper function to normalize any contact format to IContact
export function normalizeContact(contact: UnifiedContact): IContact {
  if (isEnhancedContact(contact)) {
    return contact;
  }
  
  if (isLegacyContact(contact)) {
    return convertLegacyContact(contact);
  }
  
  // Handle other legacy formats
  if (contact?.contact?.customer) {
    return {
      id: contact.contact.customer.id || contact.id,
      name: contact.contact.customer.name || contact.name || contact.email,
      email: contact.contact.email || contact.email,
      avatar: contact.contact.customer.profileImage || contact.avatar,
      status: contact.status || 'active',
      isVerified: contact.isVerified || false,
      verificationLevel: contact.verificationLevel || 'basic',
      lastTransactionAt: contact.lastTransactionAt ? new Date(contact.lastTransactionAt) : undefined,
      totalTransactions: contact.totalTransactions || 0,
      totalVolume: contact.totalVolume || 0,
      riskScore: contact.riskScore || 0,
      tags: contact.tags || [],
      capabilities: contact.capabilities || {
        canReceiveEscrow: true,
        canReceiveRecurringTransfer: true,
        canReceiveFundraisingContribution: true,
      },
      relationshipType: contact.relationshipType || 'contact',
      createdAt: contact.createdAt ? new Date(contact.createdAt) : new Date(),
      updatedAt: contact.updatedAt ? new Date(contact.updatedAt) : new Date(),
    };
  }
  
  // Handle simple contact objects
  return {
    id: contact.id,
    name: contact.name || contact.email,
    email: contact.email,
    avatar: contact.avatar,
    status: contact.status || 'active',
    isVerified: contact.isVerified || false,
    verificationLevel: contact.verificationLevel || 'basic',
    lastTransactionAt: contact.lastTransactionAt ? new Date(contact.lastTransactionAt) : undefined,
    totalTransactions: contact.totalTransactions || 0,
    totalVolume: contact.totalVolume || 0,
    riskScore: contact.riskScore || 0,
    tags: contact.tags || [],
    capabilities: contact.capabilities || {
      canReceiveEscrow: true,
      canReceiveRecurringTransfer: true,
      canReceiveFundraisingContribution: true,
    },
    relationshipType: contact.relationshipType || 'contact',
    createdAt: contact.createdAt ? new Date(contact.createdAt) : new Date(),
    updatedAt: contact.updatedAt ? new Date(contact.updatedAt) : new Date(),
  };
}

// Export legacy types for backward compatibility
export { CustomerContact as LegacyCustomerContact };

// Re-export commonly used types
export type {
  IContact as EnhancedContact,
  ContactFilters as EnhancedContactFilters,
  ContactSearchResponse as EnhancedContactSearchResponse,
};

"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { FundraisingPool } from "@/types/fundraising-pool";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { imageURL } from "@/lib/utils";
import { 
  Target, 
  Users, 
  Calendar, 
  DollarSign, 
  Eye, 
  Heart,
  Share2,
  MoreHorizontal,
  Edit,
  Play,
  Pause,
  TrendingUp,
  Clock
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface FundraisingPoolCardProps {
  pool: FundraisingPool;
  isOwner?: boolean;
  onAction?: (action: string, poolId: number, data?: any) => void;
  showActions?: boolean;
}

export function FundraisingPoolCard({ 
  pool, 
  isOwner = false, 
  onAction,
  showActions = true 
}: FundraisingPoolCardProps) {
  const { t } = useTranslation();

  const getStatusIcon = () => {
    switch (pool.status) {
      case 'draft':
        return <Edit className="h-4 w-4" />;
      case 'active':
        return <Play className="h-4 w-4" />;
      case 'paused':
        return <Pause className="h-4 w-4" />;
      case 'completed':
        return <Target className="h-4 w-4" />;
      case 'cancelled':
        return <Clock className="h-4 w-4" />;
      case 'expired':
        return <Clock className="h-4 w-4" />;
      default:
        return <Target className="h-4 w-4" />;
    }
  };

  const getStatusColor = () => {
    switch (pool.status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'expired':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const canContribute = pool.canReceiveContributions;
  const canEdit = isOwner && ['draft', 'active', 'paused'].includes(pool.status);
  const canActivate = isOwner && pool.status === 'draft';
  const canPause = isOwner && pool.status === 'active';
  const canResume = isOwner && pool.status === 'paused';

  return (
    <Card className="w-full hover:shadow-md transition-shadow group">
      <CardHeader className="pb-3">
        {/* Cover Image */}
        {pool.coverImage && (
          <div className="relative w-full h-48 mb-4 rounded-lg overflow-hidden">
            <Image
              src={imageURL(pool.coverImage)}
              alt={pool.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
            />
            <div className="absolute top-2 right-2">
              <Badge 
                variant="outline" 
                className={`${getStatusColor()} flex items-center space-x-1 bg-white/90 backdrop-blur-sm`}
              >
                {getStatusIcon()}
                <span className="capitalize">{t(pool.status)}</span>
              </Badge>
            </div>
          </div>
        )}

        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              {!pool.coverImage && (
                <Badge 
                  variant="outline" 
                  className={`${getStatusColor()} flex items-center space-x-1`}
                >
                  {getStatusIcon()}
                  <span className="capitalize">{t(pool.status)}</span>
                </Badge>
              )}
              {pool.category && (
                <Badge variant="secondary" className="text-xs">
                  {t(pool.category)}
                </Badge>
              )}
              {pool.visibility === 'private' && (
                <Badge variant="outline" className="text-xs">
                  {t('Private')}
                </Badge>
              )}
            </div>
            
            <h3 className="font-semibold text-lg line-clamp-2 mb-2">
              {pool.title}
            </h3>
            
            <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
              {pool.description}
            </p>
          </div>
          
          {showActions && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href={`/fundraising-pools/${pool.id}`}>
                    <Eye className="h-4 w-4 mr-2" />
                    {t('View Details')}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onAction?.('share', pool.id)}>
                  <Share2 className="h-4 w-4 mr-2" />
                  {t('Share')}
                </DropdownMenuItem>
                {canEdit && (
                  <DropdownMenuItem asChild>
                    <Link href={`/fundraising-pools/${pool.id}/edit`}>
                      <Edit className="h-4 w-4 mr-2" />
                      {t('Edit')}
                    </Link>
                  </DropdownMenuItem>
                )}
                {canActivate && (
                  <DropdownMenuItem onClick={() => onAction?.('activate', pool.id)}>
                    <Play className="h-4 w-4 mr-2" />
                    {t('Activate')}
                  </DropdownMenuItem>
                )}
                {canPause && (
                  <DropdownMenuItem onClick={() => onAction?.('pause', pool.id)}>
                    <Pause className="h-4 w-4 mr-2" />
                    {t('Pause')}
                  </DropdownMenuItem>
                )}
                {canResume && (
                  <DropdownMenuItem onClick={() => onAction?.('resume', pool.id)}>
                    <Play className="h-4 w-4 mr-2" />
                    {t('Resume')}
                  </DropdownMenuItem>
                )}
                {isOwner && pool.status === 'completed' && !pool.fundsDistributed && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onAction?.('distribute', pool.id)}>
                      <DollarSign className="h-4 w-4 mr-2" />
                      {t('Distribute Funds')}
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">
              {pool.currentAmount.toLocaleString()} {pool.currencyCode}
            </span>
            <span className="text-muted-foreground">
              {pool.progressPercentage}% of {pool.targetAmount.toLocaleString()} {pool.currencyCode}
            </span>
          </div>
          <Progress value={pool.progressPercentage} className="h-3" />
          {pool.isTargetReached && (
            <div className="flex items-center space-x-1 text-green-600 text-sm">
              <Target className="h-3 w-3" />
              <span>{t('Target Reached!')}</span>
            </div>
          )}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-muted-foreground mb-1">
              <Users className="h-3 w-3" />
              <span>{t('Contributors')}</span>
            </div>
            <p className="font-semibold">{pool.contributorsCount}</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-muted-foreground mb-1">
              <TrendingUp className="h-3 w-3" />
              <span>{t('Raised')}</span>
            </div>
            <p className="font-semibold">{pool.progressPercentage}%</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-muted-foreground mb-1">
              <Calendar className="h-3 w-3" />
              <span>{t('Days Left')}</span>
            </div>
            <p className="font-semibold">
              {pool.daysRemaining !== null ? pool.daysRemaining : '∞'}
            </p>
          </div>
        </div>

        {/* Creator */}
        <div className="flex items-center space-x-2 text-sm">
          {pool.creator?.customer?.avatar && (
            <Image
              src={imageURL(pool.creator.customer.avatar)}
              alt={pool.creator.name}
              width={20}
              height={20}
              className="rounded-full"
            />
          )}
          <span className="text-muted-foreground">{t('by')}</span>
          <span className="font-medium">{pool.creator?.name || t('Anonymous')}</span>
        </div>

        {/* Tags */}
        {pool.tags && pool.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {pool.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {pool.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{pool.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-2 pt-2">
          {canContribute && (
            <Button 
              className="flex-1" 
              onClick={() => onAction?.('contribute', pool.id)}
            >
              <Heart className="h-4 w-4 mr-2" />
              {t('Contribute')}
            </Button>
          )}
          
          <Button variant="outline" asChild>
            <Link href={`/fundraising-pools/${pool.id}`}>
              <Eye className="h-4 w-4 mr-2" />
              {t('View')}
            </Link>
          </Button>
        </div>

        {/* Time Info */}
        <div className="text-xs text-muted-foreground text-center pt-2 border-t">
          {pool.hasDeadline && pool.endDate ? (
            <span>
              {pool.isExpired 
                ? t('Ended {{date}}', { date: pool.getEndDate() })
                : t('Ends {{date}}', { date: pool.getEndDate() })
              }
            </span>
          ) : (
            <span>{t('Created {{date}}', { date: pool.getCreatedAt() })}</span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

{"version": 3, "file": "fees_calculation_service.js", "sourceRoot": "", "sources": ["../../../app/services/fees_calculation_service.ts"], "names": [], "mappings": "AACA,OAAO,OAAO,MAAM,iBAAiB,CAAC;AAGtC,OAAO,eAAe,MAAM,yBAAyB,CAAC;AAEtD,MAAM,CAAC,MAAM,yBAAyB,GAAG,KAAK,EAAE,MAAc,EAAE,IAAU,EAAE,KAAY,EAAE,EAAE;IAC1F,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAC9E,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,oBAAoB,GAAG,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5F,IAAI,KAAK,CAAC,aAAa,KAAK,IAAI,EAAE,CAAC;YACjC,oBAAoB,GAAG,oBAAoB,GAAG,KAAK,CAAC,aAAa,CAAC;QACpE,CAAC;QACD,IAAI,GAAG,GAAG,MAAM,GAAG,CAAC,oBAAoB,GAAG,GAAG,CAAC,CAAC;QAChD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;gBACtC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAChD,IAAI,KAAK,CAAC,aAAa,KAAK,IAAI,EAAE,CAAC;oBACjC,oBAAoB,GAAG,oBAAoB,GAAG,KAAK,CAAC,aAAa,CAAC;gBACpE,CAAC;gBACD,GAAG,GAAG,MAAM,GAAG,CAAC,oBAAoB,GAAG,GAAG,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QACD,OAAO,EAAE,MAAM,EAAE,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC5D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACtD,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAG,KAAK,EAAE,MAAc,EAAE,IAAU,EAAE,EAAE;IACrE,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAC9E,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,oBAAoB,GAAG,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5F,IAAI,GAAG,GAAG,MAAM,GAAG,CAAC,oBAAoB,GAAG,GAAG,CAAC,CAAC;QAChD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;gBACtC,GAAG,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACpC,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;gBACnC,GAAG,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QACD,OAAO,EAAE,MAAM,EAAE,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC5D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAAG,KAAK,EAAE,MAAc,EAAE,IAAU,EAAE,MAAsB,EAAE,EAAE;IAC9F,IAAI,CAAC;QACH,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAChF,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,oBAAoB,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9F,IAAI,MAAM,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;YACrC,oBAAoB,GAAG,MAAM,CAAC,gBAAgB,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBACnE,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;YAC/E,CAAC;YACD,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,KAAK,IAAI,EAAE,CAAC;gBACzC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;YACrD,CAAC;QACH,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACpC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBAC7D,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;YAC/E,CAAC;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,KAAK,IAAI,EAAE,CAAC;gBACtC,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;YAClD,CAAC;QACH,CAAC;QAED,IAAI,aAAa,GAAG,MAAM,GAAG,CAAC,oBAAoB,GAAG,GAAG,CAAC,CAAC;QAE1D,MAAM,GAAG,GAAG,eAAe,CAAC,aAAa,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;QAChE,OAAO,EAAE,aAAa,EAAE,eAAe,CAAC,MAAM,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC;IACvF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC"}
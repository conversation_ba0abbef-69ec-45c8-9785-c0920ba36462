import { DateTime } from 'luxon';
import FundraisingPool from '#models/fundraising_pool';
import PoolContribution from '#models/pool_contribution';
import Transaction from '#models/transaction';
import User from '#models/user';
import Wallet from '#models/wallet';
import Currency from '#models/currency';
import formatPrecision from '#utils/format_precision';
import notification_service from '#services/notification_service';

class FundraisingPoolService {
  /**
   * Create a new fundraising pool
   */
  async createPool(data) {
    const {
      creatorId,
      title,
      description,
      category,
      tags = [],
      targetAmount,
      currencyCode,
      minimumContribution = 0,
      maximumContribution = null,
      startDate = null,
      endDate = null,
      hasDeadline = false,
      visibility = 'public',
      allowAnonymous = true,
      autoDistribute = true,
      distributionType = 'on_completion',
      coverImage = null,
      gallery = [],
      videoUrl = null,
      allowComments = true,
      showContributors = true,
      sendUpdates = true,
      platformFeePercentage = 0
    } = data;

    // Validate creator exists
    const creator = await User.find(creatorId);
    if (!creator) {
      throw new Error('Invalid creator');
    }

    // Validate currency
    const currency = await Currency.query().where('code', currencyCode).first();
    if (!currency) {
      throw new Error('Invalid currency');
    }

    // Validate dates
    let startDateTime = null;
    let endDateTime = null;

    if (startDate) {
      startDateTime = DateTime.fromISO(startDate);
      if (!startDateTime.isValid) {
        throw new Error('Invalid start date');
      }
    } else {
      startDateTime = DateTime.now();
    }

    if (hasDeadline && endDate) {
      endDateTime = DateTime.fromISO(endDate);
      if (!endDateTime.isValid) {
        throw new Error('Invalid end date');
      }
      
      if (endDateTime <= startDateTime) {
        throw new Error('End date must be after start date');
      }
    }

    // Create fundraising pool
    const pool = await FundraisingPool.create({
      creatorId,
      title,
      description,
      category,
      tags: JSON.stringify(tags),
      targetAmount: formatPrecision(targetAmount),
      currencyCode: currencyCode.toUpperCase(),
      minimumContribution: formatPrecision(minimumContribution),
      maximumContribution: maximumContribution ? formatPrecision(maximumContribution) : null,
      startDate: startDateTime.toJSDate(),
      endDate: endDateTime ? endDateTime.toJSDate() : null,
      hasDeadline,
      visibility,
      allowAnonymous,
      autoDistribute,
      distributionType,
      coverImage,
      gallery: JSON.stringify(gallery),
      videoUrl,
      allowComments,
      showContributors,
      sendUpdates,
      platformFeePercentage,
      status: 'draft'
    });

    return pool;
  }

  /**
   * Activate a fundraising pool
   */
  async activatePool(poolId, creatorId) {
    const pool = await FundraisingPool.query()
      .where('id', poolId)
      .where('creator_id', creatorId)
      .first();

    if (!pool) {
      throw new Error('Pool not found or unauthorized');
    }

    if (pool.status !== 'draft') {
      throw new Error('Only draft pools can be activated');
    }

    await pool.activate();

    // Send notification to creator
    await this.sendNotification(creatorId, 'pool_activated', {
      poolId: pool.poolId,
      title: pool.title,
      targetAmount: pool.targetAmount,
      currency: pool.currencyCode
    });

    return pool;
  }

  /**
   * Make a contribution to a pool
   */
  async contributeToPool(poolId, contributionData) {
    const {
      contributorId = null,
      amount,
      contributorName = null,
      contributorEmail = null,
      isAnonymous = false,
      showAmount = true,
      message = null,
      allowContact = false,
      socialLinks = {},
      paymentMethod = null
    } = contributionData;

    // Get the pool
    const pool = await FundraisingPool.find(poolId);
    if (!pool) {
      throw new Error('Pool not found');
    }

    // Validate pool can receive contributions
    if (!pool.canReceiveContributions) {
      throw new Error('Pool cannot receive contributions at this time');
    }

    // Validate contribution amount
    if (amount <= 0) {
      throw new Error('Contribution amount must be positive');
    }

    if (pool.minimumContribution > 0 && amount < pool.minimumContribution) {
      throw new Error(`Minimum contribution is ${pool.minimumContribution} ${pool.currencyCode}`);
    }

    if (pool.maximumContribution && amount > pool.maximumContribution) {
      throw new Error(`Maximum contribution is ${pool.maximumContribution} ${pool.currencyCode}`);
    }

    // Validate contributor
    let contributor = null;
    if (contributorId) {
      contributor = await User.find(contributorId);
      if (!contributor) {
        throw new Error('Invalid contributor');
      }
    } else if (!pool.allowAnonymous) {
      throw new Error('Anonymous contributions are not allowed for this pool');
    }

    // Calculate fees
    const currency = await Currency.query().where('code', pool.currencyCode).first();
    const feePercentage = currency.transferFee || 0;
    const fee = formatPrecision((amount * feePercentage) / 100);
    const netAmount = formatPrecision(amount - fee);

    // Create contribution
    const contribution = await PoolContribution.create({
      poolId: pool.id,
      contributorId,
      amount: formatPrecision(amount),
      fee,
      netAmount,
      currencyCode: pool.currencyCode,
      contributorName,
      contributorEmail,
      isAnonymous,
      showAmount,
      message,
      allowContact,
      socialLinks: JSON.stringify(socialLinks),
      paymentMethod,
      status: 'pending'
    });

    // If contributor is registered, process payment from their wallet
    if (contributorId) {
      await this.processContributionPayment(contribution, contributor);
    }

    return contribution;
  }

  /**
   * Process payment for a contribution
   */
  async processContributionPayment(contribution, contributor) {
    // Get contributor's wallet
    const currency = await Currency.query().where('code', contribution.currencyCode).first();
    const contributorWallet = await Wallet.query()
      .where('user_id', contributor.id)
      .where('currency_id', currency.id)
      .where('default', true)
      .first();

    if (!contributorWallet) {
      throw new Error('Contributor wallet not found');
    }

    // Check sufficient balance
    if (contributorWallet.balance < contribution.amount) {
      throw new Error('Insufficient balance');
    }

    // Deduct from contributor's wallet
    contributorWallet.balance = formatPrecision(contributorWallet.balance - contribution.amount);
    await contributorWallet.save();

    // Create transaction record
    const pool = await contribution.related('pool').query().first();
    
    const transaction = await Transaction.create({
      type: 'pool_contribution',
      from: {
        image: contributor.customer?.profileImage ?? '',
        label: contributor.customer?.name ?? contributor.name ?? '',
        email: contributor.email ?? '',
        currency: contribution.currencyCode
      },
      to: {
        label: pool.title,
        currency: contribution.currencyCode
      },
      amount: contribution.amount,
      fee: contribution.fee,
      total: contribution.netAmount,
      status: 'completed',
      poolContributionId: contribution.id,
      userId: contributor.id,
      metaData: {
        currency: contribution.currencyCode,
        trxAction: 'pool_contribute',
        poolId: pool.poolId,
        poolTitle: pool.title
      }
    });

    // Update contribution status and link transaction
    await contribution.updateTransaction(transaction.id);
    await contribution.complete();

    // Send notifications
    await this.sendContributionNotifications(contribution, pool, contributor);

    return transaction;
  }

  /**
   * Distribute funds when pool reaches target or deadline
   */
  async distributeFunds(poolId, distributedByUserId = null) {
    const pool = await FundraisingPool.query()
      .where('id', poolId)
      .preload('creator')
      .first();

    if (!pool) {
      throw new Error('Pool not found');
    }

    if (pool.fundsDistributed) {
      throw new Error('Funds have already been distributed');
    }

    if (pool.currentAmount <= 0) {
      throw new Error('No funds to distribute');
    }

    // Calculate platform fee
    await pool.calculatePlatformFee();

    // Get creator's wallet
    const currency = await Currency.query().where('code', pool.currencyCode).first();
    const creatorWallet = await Wallet.query()
      .where('user_id', pool.creatorId)
      .where('currency_id', currency.id)
      .where('default', true)
      .first();

    if (!creatorWallet) {
      throw new Error('Creator wallet not found');
    }

    // Calculate amount to distribute (after platform fees)
    const distributionAmount = formatPrecision(pool.currentAmount - pool.platformFeeAmount);

    // Add funds to creator's wallet
    creatorWallet.balance = formatPrecision(creatorWallet.balance + distributionAmount);
    await creatorWallet.save();

    // Create distribution transaction
    const transaction = await Transaction.create({
      type: 'pool_distribution',
      from: {
        label: pool.title,
        currency: pool.currencyCode
      },
      to: {
        image: pool.creator.customer?.profileImage ?? '',
        label: pool.creator.customer?.name ?? pool.creator.name ?? '',
        email: pool.creator.email ?? '',
        currency: pool.currencyCode
      },
      amount: distributionAmount,
      fee: pool.platformFeeAmount,
      total: distributionAmount,
      status: 'completed',
      userId: pool.creatorId,
      metaData: {
        currency: pool.currencyCode,
        trxAction: 'pool_receive',
        poolId: pool.poolId,
        poolTitle: pool.title,
        distributedBy: distributedByUserId
      }
    });

    // Mark pool as distributed
    await pool.distributeFunds();

    // Send notifications
    await this.sendDistributionNotifications(pool, distributionAmount);

    return { pool, transaction, distributionAmount };
  }

  /**
   * Get pool statistics
   */
  async getPoolStatistics(poolId) {
    const pool = await FundraisingPool.find(poolId);
    if (!pool) {
      throw new Error('Pool not found');
    }

    const contributions = await PoolContribution.query()
      .where('pool_id', poolId)
      .where('status', 'completed');

    const stats = {
      totalContributions: contributions.length,
      totalAmount: pool.currentAmount,
      averageContribution: contributions.length > 0 ? pool.currentAmount / contributions.length : 0,
      progressPercentage: pool.progressPercentage,
      daysRemaining: pool.daysRemaining,
      isTargetReached: pool.isTargetReached,
      topContributors: await pool.getTopContributors(5),
      recentContributions: await pool.getRecentContributions(10),
      contributionsByDay: await this.getContributionsByDay(poolId),
      uniqueContributors: new Set(contributions.filter(c => c.contributorId).map(c => c.contributorId)).size
    };

    return stats;
  }

  /**
   * Get contributions grouped by day
   */
  async getContributionsByDay(poolId, days = 30) {
    const startDate = DateTime.now().minus({ days }).toSQL();
    
    const contributions = await PoolContribution.query()
      .where('pool_id', poolId)
      .where('status', 'completed')
      .where('created_at', '>=', startDate)
      .orderBy('created_at');

    const contributionsByDay = {};
    
    contributions.forEach(contribution => {
      const day = DateTime.fromJSDate(contribution.createdAt).toISODate();
      if (!contributionsByDay[day]) {
        contributionsByDay[day] = {
          date: day,
          count: 0,
          amount: 0
        };
      }
      contributionsByDay[day].count += 1;
      contributionsByDay[day].amount += contribution.netAmount;
    });

    return Object.values(contributionsByDay);
  }

  /**
   * Search pools
   */
  async searchPools(criteria = {}) {
    const {
      query = null,
      category = null,
      status = 'active',
      visibility = 'public',
      sortBy = 'created_at',
      sortOrder = 'desc',
      page = 1,
      limit = 20
    } = criteria;

    let poolQuery = FundraisingPool.query()
      .where('status', status)
      .where('visibility', visibility)
      .preload('creator');

    if (query) {
      poolQuery = poolQuery.where((builder) => {
        builder
          .where('title', 'like', `%${query}%`)
          .orWhere('description', 'like', `%${query}%`);
      });
    }

    if (category) {
      poolQuery = poolQuery.where('category', category);
    }

    poolQuery = poolQuery.orderBy(sortBy, sortOrder);

    const pools = await poolQuery.paginate(page, limit);

    return {
      pools: pools.all(),
      pagination: {
        page: pools.currentPage,
        perPage: pools.perPage,
        total: pools.total,
        lastPage: pools.lastPage
      }
    };
  }

  /**
   * Send contribution notifications
   */
  async sendContributionNotifications(contribution, pool, contributor) {
    // Notify pool creator
    await this.sendNotification(pool.creatorId, 'pool_contribution_received', {
      poolId: pool.poolId,
      poolTitle: pool.title,
      amount: contribution.netAmount,
      currency: contribution.currencyCode,
      contributorName: contribution.isAnonymous ? 'Anonymous' : (contributor?.name || contribution.contributorName),
      currentAmount: pool.currentAmount,
      targetAmount: pool.targetAmount,
      progressPercentage: pool.progressPercentage
    });

    // Notify contributor
    if (contributor && !contribution.isAnonymous) {
      await this.sendNotification(contributor.id, 'pool_contribution_confirmed', {
        poolId: pool.poolId,
        poolTitle: pool.title,
        amount: contribution.netAmount,
        currency: contribution.currencyCode
      });
    }

    // Notify other contributors if pool allows updates
    if (pool.sendUpdates) {
      await this.notifyPoolUpdates(pool, 'new_contribution', {
        amount: contribution.netAmount,
        contributorName: contribution.isAnonymous ? 'Anonymous' : (contributor?.name || contribution.contributorName)
      });
    }
  }

  /**
   * Send distribution notifications
   */
  async sendDistributionNotifications(pool, distributionAmount) {
    // Notify creator
    await this.sendNotification(pool.creatorId, 'pool_funds_distributed', {
      poolId: pool.poolId,
      poolTitle: pool.title,
      amount: distributionAmount,
      currency: pool.currencyCode,
      totalRaised: pool.currentAmount
    });

    // Notify contributors
    if (pool.sendUpdates) {
      await this.notifyPoolUpdates(pool, 'funds_distributed', {
        amount: distributionAmount,
        totalRaised: pool.currentAmount
      });
    }
  }

  /**
   * Notify pool contributors about updates
   */
  async notifyPoolUpdates(pool, updateType, data) {
    const contributors = await PoolContribution.query()
      .where('pool_id', pool.id)
      .where('status', 'completed')
      .whereNotNull('contributor_id')
      .preload('contributor');

    const uniqueContributors = new Map();
    contributors.forEach(contribution => {
      if (contribution.contributor && !uniqueContributors.has(contribution.contributorId)) {
        uniqueContributors.set(contribution.contributorId, contribution.contributor);
      }
    });

    for (const contributor of uniqueContributors.values()) {
      await this.sendNotification(contributor.id, `pool_${updateType}`, {
        poolId: pool.poolId,
        poolTitle: pool.title,
        ...data
      });
    }
  }

  /**
   * Send notification
   */
  async sendNotification(userId, type, data) {
    try {
      await notification_service.sendNotification({
        userId,
        type: 'fundraising_pool',
        subType: type,
        title: this.getNotificationTitle(type),
        message: this.getNotificationMessage(type, data),
        data
      });
    } catch (error) {
      console.error('Failed to send fundraising pool notification:', error);
    }
  }

  /**
   * Get notification title based on type
   */
  getNotificationTitle(type) {
    const titles = {
      'pool_activated': 'Fundraising Pool Activated',
      'pool_contribution_received': 'New Contribution Received',
      'pool_contribution_confirmed': 'Contribution Confirmed',
      'pool_target_reached': 'Fundraising Target Reached',
      'pool_funds_distributed': 'Funds Distributed',
      'pool_new_contribution': 'New Contribution',
      'pool_funds_distributed_update': 'Funds Distributed'
    };

    return titles[type] || 'Fundraising Pool Notification';
  }

  /**
   * Get notification message based on type and data
   */
  getNotificationMessage(type, data) {
    const messages = {
      'pool_activated': `Your fundraising pool "${data.poolTitle}" is now active and accepting contributions.`,
      'pool_contribution_received': `${data.contributorName} contributed ${data.amount} ${data.currency} to your pool "${data.poolTitle}".`,
      'pool_contribution_confirmed': `Your contribution of ${data.amount} ${data.currency} to "${data.poolTitle}" has been confirmed.`,
      'pool_target_reached': `Congratulations! Your fundraising pool "${data.poolTitle}" has reached its target of ${data.targetAmount} ${data.currency}.`,
      'pool_funds_distributed': `${data.amount} ${data.currency} has been distributed to you from your fundraising pool "${data.poolTitle}".`,
      'pool_new_contribution': `${data.contributorName} made a new contribution to "${data.poolTitle}".`,
      'pool_funds_distributed_update': `Funds have been distributed from the fundraising pool "${data.poolTitle}".`
    };

    return messages[type] || 'You have a fundraising pool notification.';
  }

  /**
   * Handle expired pools
   */
  async handleExpiredPools() {
    const expiredPools = await FundraisingPool.query()
      .where('status', 'active')
      .where('has_deadline', true)
      .where('end_date', '<', DateTime.now().toSQL())
      .where('funds_distributed', false);

    const results = [];

    for (const pool of expiredPools) {
      try {
        if (pool.autoDistribute && pool.currentAmount > 0) {
          const result = await this.distributeFunds(pool.id);
          results.push({ poolId: pool.id, action: 'distributed', result });
        } else {
          await pool.expire();
          results.push({ poolId: pool.id, action: 'expired' });
        }
      } catch (error) {
        results.push({ poolId: pool.id, action: 'error', error: error.message });
      }
    }

    return results;
  }
}

export default new FundraisingPoolService();

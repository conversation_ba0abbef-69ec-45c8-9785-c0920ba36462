"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CreateRecurringTransferData, TransferFrequency } from "@/types/recurring-transfer";
import SelectRecipientCombo from "@/components/common/form/SelectRecipientCombo";
import { SelectCurrency } from "@/components/common/form/SelectCurrency";
import { DatePicker } from "@/components/common/form/DatePicker";
import { RotateCcw, DollarSign, Calendar, Settings, Bell, AlertTriangle } from "lucide-react";

const createRecurringTransferSchema = z.object({
  recipientId: z.number().min(1, "Please select a recipient"),
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  currencyCode: z.string().min(3, "Please select a currency"),
  frequency: z.enum(['daily', 'weekly', 'monthly', 'yearly']),
  intervalValue: z.number().min(1, "Interval must be at least 1").max(365, "Interval cannot exceed 365"),
  startDate: z.string().min(1, "Start date is required"),
  endDate: z.string().optional(),
  maxOccurrences: z.number().min(1, "Max occurrences must be at least 1").max(10000, "Max occurrences cannot exceed 10,000").optional(),
  description: z.string().optional(),
  notifySender: z.boolean(),
  notifyRecipient: z.boolean(),
  notifyOnFailure: z.boolean(),
});

type CreateRecurringTransferFormData = z.infer<typeof createRecurringTransferSchema>;

interface CreateRecurringTransferFormProps {
  onSubmit: (data: CreateRecurringTransferData) => Promise<void>;
  isLoading?: boolean;
  error?: string;
}

export function CreateRecurringTransferForm({ 
  onSubmit, 
  isLoading = false, 
  error 
}: CreateRecurringTransferFormProps) {
  const { t } = useTranslation();
  const [selectedRecipient, setSelectedRecipient] = useState<any>(null);
  const [hasEndDate, setHasEndDate] = useState(false);
  const [hasMaxOccurrences, setHasMaxOccurrences] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreateRecurringTransferFormData>({
    resolver: zodResolver(createRecurringTransferSchema),
    defaultValues: {
      frequency: 'monthly',
      intervalValue: 1,
      notifySender: true,
      notifyRecipient: true,
      notifyOnFailure: true,
    },
  });

  const watchedAmount = watch('amount');
  const watchedCurrency = watch('currencyCode');
  const watchedFrequency = watch('frequency');
  const watchedInterval = watch('intervalValue');

  const frequencyOptions: { value: TransferFrequency; label: string }[] = [
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'yearly', label: 'Yearly' },
  ];

  const getFrequencyLabel = () => {
    if (watchedInterval > 1) {
      return `Every ${watchedInterval} ${watchedFrequency === 'daily' ? 'days' : 
                                        watchedFrequency === 'weekly' ? 'weeks' :
                                        watchedFrequency === 'monthly' ? 'months' : 'years'}`;
    }
    return frequencyOptions.find(f => f.value === watchedFrequency)?.label || '';
  };

  const onFormSubmit = async (data: CreateRecurringTransferFormData) => {
    const submitData: CreateRecurringTransferData = {
      ...data,
      recipientId: selectedRecipient?.id || data.recipientId,
      endDate: hasEndDate ? data.endDate : undefined,
      maxOccurrences: hasMaxOccurrences ? data.maxOccurrences : undefined,
    };

    await onSubmit(submitData);
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Transfer Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>{t('Transfer Details')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Recipient Selection */}
          <div className="space-y-2">
            <Label htmlFor="recipient">{t('Recipient')} *</Label>
            <SelectRecipientCombo
              user={selectedRecipient}
              setUser={setSelectedRecipient}
              onChange={(value) => setValue('recipientId', parseInt(value))}
            />
            {errors.recipientId && (
              <p className="text-sm text-red-600">{errors.recipientId.message}</p>
            )}
          </div>

          {/* Amount and Currency */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="amount">{t('Amount')} *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                placeholder="0.00"
                {...register('amount', { valueAsNumber: true })}
              />
              {errors.amount && (
                <p className="text-sm text-red-600">{errors.amount.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">{t('Currency')} *</Label>
              <SelectCurrency
                value={watchedCurrency}
                onChange={(value) => setValue('currencyCode', value)}
              />
              {errors.currencyCode && (
                <p className="text-sm text-red-600">{errors.currencyCode.message}</p>
              )}
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">{t('Description')}</Label>
            <Textarea
              id="description"
              placeholder={t('Describe the purpose of this recurring transfer...')}
              {...register('description')}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Schedule Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>{t('Schedule Settings')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Frequency and Interval */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="frequency">{t('Frequency')} *</Label>
              <Select
                value={watchedFrequency}
                onValueChange={(value) => setValue('frequency', value as TransferFrequency)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {frequencyOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {t(option.label)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.frequency && (
                <p className="text-sm text-red-600">{errors.frequency.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="intervalValue">{t('Interval')} *</Label>
              <Input
                id="intervalValue"
                type="number"
                min="1"
                max="365"
                {...register('intervalValue', { valueAsNumber: true })}
              />
              {errors.intervalValue && (
                <p className="text-sm text-red-600">{errors.intervalValue.message}</p>
              )}
              <p className="text-xs text-muted-foreground">
                {t('Current setting')}: {getFrequencyLabel()}
              </p>
            </div>
          </div>

          {/* Start Date */}
          <div className="space-y-2">
            <Label>{t('Start Date')} *</Label>
            <DatePicker
              date={watch('startDate') ? new Date(watch('startDate')) : undefined}
              onDateChange={(date) => 
                setValue('startDate', date?.toISOString().split('T')[0] || '')
              }
              placeholder={t('Select start date')}
            />
            {errors.startDate && (
              <p className="text-sm text-red-600">{errors.startDate.message}</p>
            )}
          </div>

          {/* End Date Option */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Switch
                id="hasEndDate"
                checked={hasEndDate}
                onCheckedChange={setHasEndDate}
              />
              <Label htmlFor="hasEndDate">{t('Set End Date')}</Label>
            </div>
            {hasEndDate && (
              <DatePicker
                date={watch('endDate') ? new Date(watch('endDate')) : undefined}
                onDateChange={(date) => 
                  setValue('endDate', date?.toISOString().split('T')[0] || '')
                }
                placeholder={t('Select end date')}
              />
            )}
            {errors.endDate && (
              <p className="text-sm text-red-600">{errors.endDate.message}</p>
            )}
          </div>

          {/* Max Occurrences Option */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Switch
                id="hasMaxOccurrences"
                checked={hasMaxOccurrences}
                onCheckedChange={setHasMaxOccurrences}
              />
              <Label htmlFor="hasMaxOccurrences">{t('Limit Number of Transfers')}</Label>
            </div>
            {hasMaxOccurrences && (
              <Input
                type="number"
                min="1"
                max="10000"
                placeholder={t('Maximum number of transfers')}
                {...register('maxOccurrences', { valueAsNumber: true })}
              />
            )}
            {errors.maxOccurrences && (
              <p className="text-sm text-red-600">{errors.maxOccurrences.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bell className="h-5 w-5" />
            <span>{t('Notification Settings')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="notifySender">{t('Notify me when transfers execute')}</Label>
              <Switch
                id="notifySender"
                checked={watch('notifySender')}
                onCheckedChange={(checked) => setValue('notifySender', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="notifyRecipient">{t('Notify recipient when transfers execute')}</Label>
              <Switch
                id="notifyRecipient"
                checked={watch('notifyRecipient')}
                onCheckedChange={(checked) => setValue('notifyRecipient', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="notifyOnFailure">{t('Notify on transfer failures')}</Label>
              <Switch
                id="notifyOnFailure"
                checked={watch('notifyOnFailure')}
                onCheckedChange={(checked) => setValue('notifyOnFailure', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline">
          {t('Cancel')}
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? t('Creating...') : t('Create Recurring Transfer')}
        </Button>
      </div>
    </form>
  );
}

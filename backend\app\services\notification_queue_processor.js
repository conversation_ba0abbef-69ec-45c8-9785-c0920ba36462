import { DateTime } from 'luxon';
import EscrowNotification from '#models/escrow_notification';
import User from '#models/user';
import NotificationTemplateManager from '#services/notification_template_manager';
import notification_service from '#services/notification_service';

class NotificationQueueProcessor {
  constructor() {
    this.isProcessing = false;
    this.batchSize = 50;
    this.maxRetries = 3;
    this.retryDelay = 300; // 5 minutes in seconds
  }

  /**
   * Process all pending notifications
   */
  async processQueue() {
    if (this.isProcessing) {
      console.log('Notification queue is already being processed');
      return;
    }

    this.isProcessing = true;
    
    try {
      console.log('Starting notification queue processing...');
      
      // Process escrow notifications
      await this.processEscrowNotifications();
      
      // Process failed notifications for retry
      await this.retryFailedNotifications();
      
      console.log('Notification queue processing completed');
    } catch (error) {
      console.error('Error processing notification queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process pending escrow notifications
   */
  async processEscrowNotifications() {
    const pendingNotifications = await EscrowNotification.query()
      .where('status', 'pending')
      .where('scheduled_at', '<=', DateTime.now().toSQL())
      .preload('escrow', (query) => {
        query.preload('sender').preload('recipient');
      })
      .preload('user')
      .limit(this.batchSize);

    console.log(`Processing ${pendingNotifications.length} escrow notifications`);

    for (const notification of pendingNotifications) {
      try {
        await this.processEscrowNotification(notification);
      } catch (error) {
        console.error(`Failed to process notification ${notification.id}:`, error);
        await notification.markFailed(error.message);
      }
    }
  }

  /**
   * Process a single escrow notification
   */
  async processEscrowNotification(notification) {
    const user = notification.user;
    const escrow = notification.escrow;

    if (!user || !escrow) {
      throw new Error('Invalid notification: missing user or escrow data');
    }

    // Get template configuration
    const template = NotificationTemplateManager.getTemplate(notification.type);
    const emailData = NotificationTemplateManager.getEmailData(notification.type, notification.dataParsed);
    
    // Get user preferences (this would come from user settings)
    const userPreferences = await this.getUserNotificationPreferences(user.id);

    // Send notifications based on supported channels and user preferences
    const results = {
      email: false,
      push: false,
      sms: false,
      inApp: false
    };

    // Send email notification
    if (template.channels.includes('email') && 
        NotificationTemplateManager.shouldSendNotification(notification.type, 'email', userPreferences)) {
      try {
        await notification_service.sendEmail({
          to: user.email,
          subject: template.title,
          template: template.emailTemplate,
          data: emailData
        });
        results.email = true;
        await notification.updateDeliveryStatus('email', true);
      } catch (error) {
        console.error('Email notification failed:', error);
      }
    }

    // Send push notification
    if (template.channels.includes('push') && 
        NotificationTemplateManager.shouldSendNotification(notification.type, 'push', userPreferences)) {
      try {
        await notification_service.sendPushNotification(user.id, {
          title: template.title,
          message: NotificationTemplateManager.getMessage(notification.type, notification.dataParsed),
          data: notification.dataParsed
        });
        results.push = true;
        await notification.updateDeliveryStatus('push', true);
      } catch (error) {
        console.error('Push notification failed:', error);
      }
    }

    // Send in-app notification
    if (template.channels.includes('in_app')) {
      try {
        await notification_service.sendNotification({
          userId: user.id,
          type: 'escrow',
          subType: notification.type,
          title: template.title,
          message: NotificationTemplateManager.getMessage(notification.type, notification.dataParsed),
          navigate: NotificationTemplateManager.getNavigationUrl(notification.type, notification.dataParsed),
          data: notification.dataParsed
        });
        results.inApp = true;
      } catch (error) {
        console.error('In-app notification failed:', error);
      }
    }

    // Send SMS notification (for urgent notifications)
    if (template.channels.includes('sms') && 
        NotificationTemplateManager.shouldSendNotification(notification.type, 'sms', userPreferences)) {
      try {
        await this.sendSMSNotification(user, notification);
        results.sms = true;
        await notification.updateDeliveryStatus('sms', true);
      } catch (error) {
        console.error('SMS notification failed:', error);
      }
    }

    // Mark notification as sent if at least one channel succeeded
    if (Object.values(results).some(result => result)) {
      await notification.markSent();
    } else {
      throw new Error('All notification channels failed');
    }

    return results;
  }

  /**
   * Retry failed notifications
   */
  async retryFailedNotifications() {
    const failedNotifications = await EscrowNotification.query()
      .where('status', 'failed')
      .where('retry_count', '<', this.maxRetries)
      .where('updated_at', '<=', DateTime.now().minus({ seconds: this.retryDelay }).toSQL())
      .preload('escrow', (query) => {
        query.preload('sender').preload('recipient');
      })
      .preload('user')
      .limit(this.batchSize);

    console.log(`Retrying ${failedNotifications.length} failed notifications`);

    for (const notification of failedNotifications) {
      try {
        await this.processEscrowNotification(notification);
        console.log(`Successfully retried notification ${notification.id}`);
      } catch (error) {
        console.error(`Retry failed for notification ${notification.id}:`, error);
        await notification.markFailed(error.message);
      }
    }
  }

  /**
   * Send SMS notification (placeholder implementation)
   */
  async sendSMSNotification(user, notification) {
    // This would integrate with an SMS service like Twilio
    // For now, we'll just log the SMS
    console.log(`SMS to ${user.phone}: ${notification.title} - ${notification.message}`);
    
    // In a real implementation, you would:
    // 1. Check if user has a verified phone number
    // 2. Send SMS via SMS service provider
    // 3. Handle delivery confirmations
    // 4. Respect SMS rate limits and user preferences
  }

  /**
   * Get user notification preferences
   */
  async getUserNotificationPreferences(userId) {
    // This would fetch from user settings table
    // For now, return default preferences
    return {
      email: {
        escrow: true,
        milestones: true,
        recurring_transfers: false,
        fundraising: true,
        admin: true
      },
      push: {
        escrow: true,
        milestones: true,
        recurring_transfers: false,
        fundraising: true,
        admin: true
      },
      sms: {
        escrow: false,
        milestones: false,
        recurring_transfers: false,
        fundraising: false,
        admin: false
      }
    };
  }

  /**
   * Schedule bulk notifications
   */
  async scheduleBulkNotifications(notifications) {
    const results = [];
    
    for (const notificationData of notifications) {
      try {
        const notification = await EscrowNotification.create({
          escrowId: notificationData.escrowId,
          userId: notificationData.userId,
          type: notificationData.type,
          scheduledAt: notificationData.scheduledAt || DateTime.now().toJSDate(),
          title: NotificationTemplateManager.getTemplate(notificationData.type).title,
          message: NotificationTemplateManager.getMessage(notificationData.type, notificationData.data),
          data: JSON.stringify(notificationData.data || {})
        });
        
        results.push({ success: true, notificationId: notification.id });
      } catch (error) {
        results.push({ success: false, error: error.message });
      }
    }
    
    return results;
  }

  /**
   * Clean up old notifications
   */
  async cleanupOldNotifications(daysToKeep = 90) {
    const cutoffDate = DateTime.now().minus({ days: daysToKeep }).toSQL();
    
    const deletedCount = await EscrowNotification.query()
      .where('created_at', '<', cutoffDate)
      .where('status', 'sent')
      .delete();
    
    console.log(`Cleaned up ${deletedCount} old notifications`);
    return deletedCount;
  }

  /**
   * Get notification statistics
   */
  async getStatistics(dateRange = 7) {
    const startDate = DateTime.now().minus({ days: dateRange }).toSQL();
    
    const stats = await EscrowNotification.query()
      .where('created_at', '>=', startDate)
      .select('status', 'type')
      .groupBy('status', 'type')
      .count('* as count');
    
    const summary = {
      total: 0,
      sent: 0,
      pending: 0,
      failed: 0,
      cancelled: 0,
      byType: {},
      deliveryRate: 0
    };
    
    stats.forEach(stat => {
      summary.total += stat.count;
      summary[stat.status] = (summary[stat.status] || 0) + stat.count;
      
      if (!summary.byType[stat.type]) {
        summary.byType[stat.type] = { total: 0, sent: 0, pending: 0, failed: 0, cancelled: 0 };
      }
      summary.byType[stat.type].total += stat.count;
      summary.byType[stat.type][stat.status] = stat.count;
    });
    
    summary.deliveryRate = summary.total > 0 ? Math.round((summary.sent / summary.total) * 100) : 0;
    
    return summary;
  }

  /**
   * Cancel notifications for a specific escrow
   */
  async cancelEscrowNotifications(escrowId) {
    const cancelledCount = await EscrowNotification.query()
      .where('escrow_id', escrowId)
      .where('status', 'pending')
      .update({ status: 'cancelled' });
    
    console.log(`Cancelled ${cancelledCount} notifications for escrow ${escrowId}`);
    return cancelledCount;
  }

  /**
   * Get notification queue status
   */
  getQueueStatus() {
    return {
      isProcessing: this.isProcessing,
      batchSize: this.batchSize,
      maxRetries: this.maxRetries,
      retryDelay: this.retryDelay
    };
  }
}

export default new NotificationQueueProcessor();

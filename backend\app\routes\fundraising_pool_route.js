import router from '@adonisjs/core/services/router';
import { middleware } from '#start/kernel';

const FundraisingPoolsController = () => import('#controllers/fundraising_pools_controller');

// Fundraising pool routes - require authentication
router.group(() => {
  // User's own pools
  router.get('/fundraising-pools/my-pools', [FundraisingPoolsController, 'myPools']);
  router.get('/fundraising-pools/my-contributions', [FundraisingPoolsController, 'myContributions']);
  router.get('/fundraising-pools/export', [FundraisingPoolsController, 'exportCsv']);
  
  // Pool management
  router.post('/fundraising-pools', [FundraisingPoolsController, 'store']);
  router.put('/fundraising-pools/:id', [FundraisingPoolsController, 'update']);
  router.post('/fundraising-pools/:id/activate', [FundraisingPoolsController, 'activate']);
  router.post('/fundraising-pools/:id/distribute', [FundraisingPoolsController, 'distributeFunds']);
  
  // Pool contributions (authenticated)
  router.post('/fundraising-pools/:id/contribute', [FundraisingPoolsController, 'contribute']);
  
}).prefix('/api/v1').middleware([middleware.auth()]);

// Public fundraising pool routes (no authentication required)
router.group(() => {
  // Public pool browsing
  router.get('/fundraising-pools', [FundraisingPoolsController, 'index']);
  router.get('/fundraising-pools/search', [FundraisingPoolsController, 'search']);
  router.get('/fundraising-pools/categories', [FundraisingPoolsController, 'categories']);
  
  // Individual pool viewing
  router.get('/fundraising-pools/:id', [FundraisingPoolsController, 'show']);
  router.get('/fundraising-pools/:id/statistics', [FundraisingPoolsController, 'statistics']);
  router.get('/fundraising-pools/:id/contributions', [FundraisingPoolsController, 'contributions']);
  
  // Pool by public ID
  router.get('/fundraising-pools/by-id/:poolId', [FundraisingPoolsController, 'getByPoolId']);
  
  // Anonymous contributions
  router.post('/fundraising-pools/:id/contribute', [FundraisingPoolsController, 'contribute']);
  
}).prefix('/api/v1/public');

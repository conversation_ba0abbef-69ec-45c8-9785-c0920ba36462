"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { IMilestone } from "@/types/milestone";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  CheckCircle, 
  XCircle, 
  DollarSign, 
  AlertTriangle,
  FileText,
  Upload
} from "lucide-react";

interface MilestoneActionsProps {
  milestone: IMilestone;
  userRole: 'sender' | 'recipient';
  onAction?: (action: string, milestoneId: number, data?: any) => Promise<void>;
  isLoading?: boolean;
}

export function MilestoneActions({ 
  milestone, 
  userRole, 
  onAction,
  isLoading = false 
}: MilestoneActionsProps) {
  const { t } = useTranslation();
  const [completeDialogOpen, setCompleteDialogOpen] = useState(false);
  const [approveDialogOpen, setApproveDialogOpen] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [releaseDialogOpen, setReleaseDialogOpen] = useState(false);
  
  const [completionNotes, setCompletionNotes] = useState("");
  const [rejectionReason, setRejectionReason] = useState("");

  const canComplete = milestone.canBeCompleted && userRole === 'recipient';
  const canApprove = milestone.canBeApproved && userRole === 'sender';
  const canReject = milestone.status === 'completed' && userRole === 'sender';
  const canRelease = milestone.canBeReleased && userRole === 'sender';

  const handleComplete = async () => {
    await onAction?.('complete', milestone.id, { 
      completionNotes,
      evidence: [] // In real implementation, handle file uploads
    });
    setCompleteDialogOpen(false);
    setCompletionNotes("");
  };

  const handleApprove = async () => {
    await onAction?.('approve', milestone.id, { userType: userRole });
    setApproveDialogOpen(false);
  };

  const handleReject = async () => {
    await onAction?.('reject', milestone.id, { rejectionReason });
    setRejectDialogOpen(false);
    setRejectionReason("");
  };

  const handleRelease = async () => {
    await onAction?.('release', milestone.id);
    setReleaseDialogOpen(false);
  };

  // Don't show actions if no actions are available
  if (!canComplete && !canApprove && !canReject && !canRelease) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-2 pt-2 border-t">
      {/* Complete Milestone */}
      {canComplete && (
        <Dialog open={completeDialogOpen} onOpenChange={setCompleteDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" variant="default">
              <CheckCircle className="h-3 w-3 mr-1" />
              {t('Mark Complete')}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('Complete Milestone')}</DialogTitle>
              <DialogDescription>
                {t('Mark this milestone as completed. You can add notes and evidence.')}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="completionNotes">{t('Completion Notes')}</Label>
                <Textarea
                  id="completionNotes"
                  value={completionNotes}
                  onChange={(e) => setCompletionNotes(e.target.value)}
                  placeholder={t('Describe how you completed this milestone...')}
                  rows={3}
                />
              </div>

              {/* Evidence Upload Placeholder */}
              <div className="space-y-2">
                <Label>{t('Evidence (Optional)')}</Label>
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    {t('Upload files, images, or documents as evidence')}
                  </p>
                  <Button variant="outline" size="sm" className="mt-2" disabled>
                    <FileText className="h-3 w-3 mr-1" />
                    {t('Choose Files')}
                  </Button>
                </div>
              </div>

              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  {t('Once marked complete, the milestone will be sent for approval.')}
                </AlertDescription>
              </Alert>
            </div>

            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => {
                  setCompleteDialogOpen(false);
                  setCompletionNotes("");
                }}
                disabled={isLoading}
              >
                {t('Cancel')}
              </Button>
              <Button 
                onClick={handleComplete}
                disabled={isLoading}
              >
                {isLoading ? t('Completing...') : t('Mark Complete')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Approve Milestone */}
      {canApprove && (
        <Dialog open={approveDialogOpen} onOpenChange={setApproveDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" variant="default">
              <CheckCircle className="h-3 w-3 mr-1" />
              {t('Approve')}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('Approve Milestone')}</DialogTitle>
              <DialogDescription>
                {t('Are you satisfied with the completion of this milestone?')}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  {t('Approving this milestone will make it eligible for fund release.')}
                </AlertDescription>
              </Alert>

              {/* Show milestone details */}
              <div className="bg-muted p-3 rounded">
                <h4 className="font-medium mb-2">{milestone.title}</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  {milestone.amount.toLocaleString()} • {milestone.percentage}%
                </p>
                {milestone.completionNotes && (
                  <div>
                    <p className="text-xs font-medium mb-1">{t('Completion Notes')}:</p>
                    <p className="text-xs text-muted-foreground">{milestone.completionNotes}</p>
                  </div>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setApproveDialogOpen(false)}
                disabled={isLoading}
              >
                {t('Cancel')}
              </Button>
              <Button 
                onClick={handleApprove}
                disabled={isLoading}
              >
                {isLoading ? t('Approving...') : t('Approve')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Reject Milestone */}
      {canReject && (
        <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" variant="destructive">
              <XCircle className="h-3 w-3 mr-1" />
              {t('Reject')}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('Reject Milestone')}</DialogTitle>
              <DialogDescription>
                {t('Provide a reason for rejecting this milestone completion.')}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  {t('Rejecting will send the milestone back to the recipient for revision.')}
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label htmlFor="rejectionReason">{t('Rejection Reason')} *</Label>
                <Textarea
                  id="rejectionReason"
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder={t('Explain what needs to be improved or corrected...')}
                  rows={3}
                  required
                />
              </div>
            </div>

            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => {
                  setRejectDialogOpen(false);
                  setRejectionReason("");
                }}
                disabled={isLoading}
              >
                {t('Cancel')}
              </Button>
              <Button 
                variant="destructive"
                onClick={handleReject}
                disabled={isLoading || !rejectionReason.trim()}
              >
                {isLoading ? t('Rejecting...') : t('Reject')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Release Funds */}
      {canRelease && (
        <Dialog open={releaseDialogOpen} onOpenChange={setReleaseDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" variant="default">
              <DollarSign className="h-3 w-3 mr-1" />
              {t('Release Funds')}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('Release Milestone Funds')}</DialogTitle>
              <DialogDescription>
                {t('Release the funds for this completed and approved milestone.')}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <Alert>
                <DollarSign className="h-4 w-4" />
                <AlertDescription>
                  {t('This will release {{amount}} to the recipient. This action cannot be undone.', {
                    amount: `${milestone.amount.toLocaleString()}`
                  })}
                </AlertDescription>
              </Alert>

              <div className="bg-muted p-3 rounded">
                <h4 className="font-medium mb-2">{milestone.title}</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-muted-foreground">{t('Amount')}:</span>
                    <span className="ml-2 font-medium">{milestone.amount.toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">{t('Percentage')}:</span>
                    <span className="ml-2 font-medium">{milestone.percentage}%</span>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setReleaseDialogOpen(false)}
                disabled={isLoading}
              >
                {t('Cancel')}
              </Button>
              <Button 
                onClick={handleRelease}
                disabled={isLoading}
              >
                {isLoading ? t('Releasing...') : t('Release Funds')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

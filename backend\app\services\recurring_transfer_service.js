import { DateTime } from 'luxon';
import RecurringTransfer from '#models/recurring_transfer';
import Transaction from '#models/transaction';
import User from '#models/user';
import Wallet from '#models/wallet';
import Currency from '#models/currency';
import formatPrecision from '#utils/format_precision';
import notification_service from '#services/notification_service';

class RecurringTransferService {
  /**
   * Create a new recurring transfer
   */
  async createRecurringTransfer(data) {
    const {
      senderId,
      recipientId,
      amount,
      currencyCode,
      frequency,
      intervalValue = 1,
      startDate,
      endDate = null,
      maxOccurrences = null,
      description = null,
      scheduleConfig = {},
      notifySender = true,
      notifyRecipient = true,
      notifyOnFailure = true
    } = data;

    // Validate users exist
    const sender = await User.find(senderId);
    const recipient = await User.find(recipientId);
    
    if (!sender || !recipient) {
      throw new Error('Invalid sender or recipient');
    }

    if (sender.id === recipient.id) {
      throw new Error('Sender and recipient cannot be the same');
    }

    // Validate currency
    const currency = await Currency.query().where('code', currencyCode).first();
    if (!currency) {
      throw new Error('Invalid currency');
    }

    // Calculate fee
    const feePercentage = currency.transferFee || 0;
    const fee = formatPrecision((amount * feePercentage) / 100);

    // Validate and parse start date
    const startDateTime = DateTime.fromISO(startDate);
    if (!startDateTime.isValid) {
      throw new Error('Invalid start date');
    }

    if (startDateTime <= DateTime.now()) {
      throw new Error('Start date must be in the future');
    }

    // Validate end date if provided
    let endDateTime = null;
    if (endDate) {
      endDateTime = DateTime.fromISO(endDate);
      if (!endDateTime.isValid) {
        throw new Error('Invalid end date');
      }
      
      if (endDateTime <= startDateTime) {
        throw new Error('End date must be after start date');
      }
    }

    // Validate frequency and interval
    if (!['daily', 'weekly', 'monthly', 'yearly'].includes(frequency)) {
      throw new Error('Invalid frequency. Must be daily, weekly, monthly, or yearly');
    }

    if (intervalValue < 1 || intervalValue > 365) {
      throw new Error('Interval value must be between 1 and 365');
    }

    // Calculate next execution time
    const nextExecution = this.calculateNextExecution(startDateTime, frequency, intervalValue);

    // Create recurring transfer
    const recurringTransfer = await RecurringTransfer.create({
      senderId,
      recipientId,
      amount: formatPrecision(amount),
      fee,
      currencyCode: currencyCode.toUpperCase(),
      frequency,
      intervalValue,
      scheduleConfig: JSON.stringify(scheduleConfig),
      startDate: startDateTime.toJSDate(),
      endDate: endDateTime ? endDateTime.toJSDate() : null,
      nextExecution: nextExecution.toJSDate(),
      maxOccurrences,
      description,
      notifySender,
      notifyRecipient,
      notifyOnFailure,
      status: 'active'
    });

    // Send creation notifications
    if (notifySender) {
      await this.sendNotification(senderId, 'recurring_transfer_created', {
        recurringId: recurringTransfer.recurringId,
        amount,
        currency: currencyCode,
        frequency,
        recipientName: recipient.name || recipient.email
      });
    }

    if (notifyRecipient) {
      await this.sendNotification(recipientId, 'recurring_transfer_setup', {
        recurringId: recurringTransfer.recurringId,
        amount,
        currency: currencyCode,
        frequency,
        senderName: sender.name || sender.email
      });
    }

    return recurringTransfer;
  }

  /**
   * Execute pending recurring transfers
   */
  async executePendingTransfers() {
    const pendingTransfers = await RecurringTransfer.query()
      .where('status', 'active')
      .where('next_execution', '<=', DateTime.now().toSQL())
      .preload('sender', (query) => query.preload('customer'))
      .preload('recipient', (query) => query.preload('customer'));

    const results = [];

    for (const transfer of pendingTransfers) {
      try {
        const result = await this.executeTransfer(transfer);
        results.push({
          recurringId: transfer.recurringId,
          status: 'success',
          transactionId: result.transaction.id
        });
      } catch (error) {
        await transfer.markFailed(error.message);
        results.push({
          recurringId: transfer.recurringId,
          status: 'failed',
          error: error.message
        });

        // Send failure notification if enabled
        if (transfer.notifyOnFailure) {
          await this.sendFailureNotification(transfer, error.message);
        }
      }
    }

    return results;
  }

  /**
   * Execute a single recurring transfer
   */
  async executeTransfer(recurringTransfer) {
    // Check if transfer should still be executed
    if (!recurringTransfer.shouldExecute) {
      throw new Error('Transfer should not be executed at this time');
    }

    // Get sender's wallet
    const currency = await Currency.query().where('code', recurringTransfer.currencyCode).first();
    const senderWallet = await Wallet.query()
      .where('user_id', recurringTransfer.senderId)
      .where('currency_id', currency.id)
      .where('default', true)
      .first();

    if (!senderWallet) {
      throw new Error('Sender wallet not found');
    }

    // Check sufficient balance
    const totalAmount = recurringTransfer.amount + recurringTransfer.fee;
    if (senderWallet.balance < totalAmount) {
      throw new Error('Insufficient balance');
    }

    // Get recipient's wallet
    const recipientWallet = await Wallet.query()
      .where('user_id', recurringTransfer.recipientId)
      .where('currency_id', currency.id)
      .where('default', true)
      .first();

    if (!recipientWallet) {
      throw new Error('Recipient wallet not found');
    }

    // Prepare transaction data
    const fromData = {
      image: recurringTransfer.sender.customer?.profileImage ?? '',
      label: recurringTransfer.sender.customer?.name ?? '',
      email: recurringTransfer.sender.email ?? '',
      currency: recurringTransfer.currencyCode
    };

    const toData = {
      image: recurringTransfer.recipient.customer?.profileImage ?? '',
      label: recurringTransfer.recipient.customer?.name ?? '',
      email: recurringTransfer.recipient.email ?? '',
      currency: recurringTransfer.currencyCode
    };

    // Create sender transaction
    const senderTransaction = await Transaction.create({
      type: 'recurring_transfer',
      from: fromData,
      to: toData,
      amount: totalAmount,
      fee: recurringTransfer.fee,
      total: recurringTransfer.amount,
      status: 'completed',
      recurringTransferId: recurringTransfer.id,
      userId: recurringTransfer.senderId,
      metaData: {
        currency: recurringTransfer.currencyCode,
        trxAction: 'send',
        recurringId: recurringTransfer.recurringId,
        executionCount: recurringTransfer.executedCount + 1
      }
    });

    // Create recipient transaction
    const recipientTransaction = await Transaction.create({
      type: 'recurring_transfer',
      from: fromData,
      to: toData,
      amount: recurringTransfer.amount,
      fee: 0,
      total: recurringTransfer.amount,
      status: 'completed',
      recurringTransferId: recurringTransfer.id,
      userId: recurringTransfer.recipientId,
      metaData: {
        currency: recurringTransfer.currencyCode,
        trxAction: 'receive',
        recurringId: recurringTransfer.recurringId,
        executionCount: recurringTransfer.executedCount + 1
      }
    });

    // Update wallets
    senderWallet.balance = formatPrecision(senderWallet.balance - totalAmount);
    await senderWallet.save();

    recipientWallet.balance = formatPrecision(recipientWallet.balance + recurringTransfer.amount);
    await recipientWallet.save();

    // Mark transfer as executed
    await recurringTransfer.markExecuted(senderTransaction.id, 'success');

    // Send notifications
    if (recurringTransfer.notifySender) {
      await this.sendNotification(recurringTransfer.senderId, 'recurring_transfer_executed', {
        recurringId: recurringTransfer.recurringId,
        amount: recurringTransfer.amount,
        currency: recurringTransfer.currencyCode,
        recipientName: recurringTransfer.recipient.name || recurringTransfer.recipient.email,
        executionCount: recurringTransfer.executedCount
      });
    }

    if (recurringTransfer.notifyRecipient) {
      await this.sendNotification(recurringTransfer.recipientId, 'recurring_transfer_received', {
        recurringId: recurringTransfer.recurringId,
        amount: recurringTransfer.amount,
        currency: recurringTransfer.currencyCode,
        senderName: recurringTransfer.sender.name || recurringTransfer.sender.email,
        executionCount: recurringTransfer.executedCount
      });
    }

    return {
      recurringTransfer,
      transaction: senderTransaction,
      recipientTransaction
    };
  }

  /**
   * Pause a recurring transfer
   */
  async pauseRecurringTransfer(recurringId, userId) {
    const transfer = await RecurringTransfer.query()
      .where('recurring_id', recurringId)
      .where('sender_id', userId)
      .first();

    if (!transfer) {
      throw new Error('Recurring transfer not found or unauthorized');
    }

    if (transfer.status !== 'active') {
      throw new Error('Can only pause active recurring transfers');
    }

    await transfer.pause();

    // Send notification
    await this.sendNotification(userId, 'recurring_transfer_paused', {
      recurringId: transfer.recurringId,
      amount: transfer.amount,
      currency: transfer.currencyCode
    });

    return transfer;
  }

  /**
   * Resume a paused recurring transfer
   */
  async resumeRecurringTransfer(recurringId, userId) {
    const transfer = await RecurringTransfer.query()
      .where('recurring_id', recurringId)
      .where('sender_id', userId)
      .first();

    if (!transfer) {
      throw new Error('Recurring transfer not found or unauthorized');
    }

    if (transfer.status !== 'paused') {
      throw new Error('Can only resume paused recurring transfers');
    }

    await transfer.resume();

    // Send notification
    await this.sendNotification(userId, 'recurring_transfer_resumed', {
      recurringId: transfer.recurringId,
      amount: transfer.amount,
      currency: transfer.currencyCode,
      nextExecution: transfer.nextExecution
    });

    return transfer;
  }

  /**
   * Cancel a recurring transfer
   */
  async cancelRecurringTransfer(recurringId, userId) {
    const transfer = await RecurringTransfer.query()
      .where('recurring_id', recurringId)
      .where('sender_id', userId)
      .first();

    if (!transfer) {
      throw new Error('Recurring transfer not found or unauthorized');
    }

    if (transfer.status === 'cancelled' || transfer.status === 'completed') {
      throw new Error('Recurring transfer is already cancelled or completed');
    }

    await transfer.cancel();

    // Send notifications to both parties
    await this.sendNotification(transfer.senderId, 'recurring_transfer_cancelled', {
      recurringId: transfer.recurringId,
      amount: transfer.amount,
      currency: transfer.currencyCode
    });

    if (transfer.notifyRecipient) {
      await this.sendNotification(transfer.recipientId, 'recurring_transfer_cancelled', {
        recurringId: transfer.recurringId,
        amount: transfer.amount,
        currency: transfer.currencyCode,
        senderName: (await User.find(transfer.senderId)).name
      });
    }

    return transfer;
  }

  /**
   * Get recurring transfer history
   */
  async getTransferHistory(recurringId, userId, options = {}) {
    const transfer = await RecurringTransfer.query()
      .where('recurring_id', recurringId)
      .where((query) => {
        query.where('sender_id', userId).orWhere('recipient_id', userId);
      })
      .first();

    if (!transfer) {
      throw new Error('Recurring transfer not found or unauthorized');
    }

    const { page = 1, limit = 20, status = null } = options;

    let query = Transaction.query()
      .where('recurring_transfer_id', transfer.id)
      .orderBy('created_at', 'desc');

    if (status) {
      query = query.where('status', status);
    }

    const transactions = await query.paginate(page, limit);

    return {
      recurringTransfer: transfer,
      transactions: transactions.all(),
      pagination: {
        page: transactions.currentPage,
        perPage: transactions.perPage,
        total: transactions.total,
        lastPage: transactions.lastPage
      }
    };
  }

  /**
   * Calculate next execution time
   */
  calculateNextExecution(currentTime, frequency, intervalValue) {
    const current = DateTime.isDateTime(currentTime) ? currentTime : DateTime.fromJSDate(currentTime);

    switch (frequency) {
      case 'daily':
        return current.plus({ days: intervalValue });
      case 'weekly':
        return current.plus({ weeks: intervalValue });
      case 'monthly':
        return current.plus({ months: intervalValue });
      case 'yearly':
        return current.plus({ years: intervalValue });
      default:
        throw new Error(`Invalid frequency: ${frequency}`);
    }
  }

  /**
   * Send notification
   */
  async sendNotification(userId, type, data) {
    try {
      await notification_service.sendNotification({
        userId,
        type: 'recurring_transfer',
        subType: type,
        title: this.getNotificationTitle(type),
        message: this.getNotificationMessage(type, data),
        data
      });
    } catch (error) {
      console.error('Failed to send recurring transfer notification:', error);
    }
  }

  /**
   * Send failure notification
   */
  async sendFailureNotification(transfer, errorMessage) {
    await this.sendNotification(transfer.senderId, 'recurring_transfer_failed', {
      recurringId: transfer.recurringId,
      amount: transfer.amount,
      currency: transfer.currencyCode,
      error: errorMessage,
      retryCount: transfer.retryCount
    });
  }

  /**
   * Get notification title based on type
   */
  getNotificationTitle(type) {
    const titles = {
      'recurring_transfer_created': 'Recurring Transfer Created',
      'recurring_transfer_setup': 'Recurring Transfer Setup',
      'recurring_transfer_executed': 'Recurring Transfer Executed',
      'recurring_transfer_received': 'Recurring Transfer Received',
      'recurring_transfer_paused': 'Recurring Transfer Paused',
      'recurring_transfer_resumed': 'Recurring Transfer Resumed',
      'recurring_transfer_cancelled': 'Recurring Transfer Cancelled',
      'recurring_transfer_failed': 'Recurring Transfer Failed',
      'recurring_transfer_completed': 'Recurring Transfer Completed'
    };

    return titles[type] || 'Recurring Transfer Notification';
  }

  /**
   * Get notification message based on type and data
   */
  getNotificationMessage(type, data) {
    const messages = {
      'recurring_transfer_created': `Recurring transfer of ${data.amount} ${data.currency} to ${data.recipientName} has been created.`,
      'recurring_transfer_setup': `${data.senderName} has set up a recurring transfer of ${data.amount} ${data.currency} to you.`,
      'recurring_transfer_executed': `Recurring transfer #${data.executionCount} of ${data.amount} ${data.currency} to ${data.recipientName} has been executed.`,
      'recurring_transfer_received': `You received ${data.amount} ${data.currency} from ${data.senderName} (recurring transfer #${data.executionCount}).`,
      'recurring_transfer_paused': `Recurring transfer of ${data.amount} ${data.currency} has been paused.`,
      'recurring_transfer_resumed': `Recurring transfer of ${data.amount} ${data.currency} has been resumed.`,
      'recurring_transfer_cancelled': `Recurring transfer of ${data.amount} ${data.currency} has been cancelled.`,
      'recurring_transfer_failed': `Recurring transfer failed: ${data.error}. Retry attempt ${data.retryCount}.`,
      'recurring_transfer_completed': `Recurring transfer of ${data.amount} ${data.currency} has completed all scheduled executions.`
    };

    return messages[type] || 'You have a recurring transfer notification.';
  }

  /**
   * Get upcoming transfers for a user
   */
  async getUpcomingTransfers(userId, daysAhead = 7) {
    const futureDate = DateTime.now().plus({ days: daysAhead });

    const transfers = await RecurringTransfer.query()
      .where((query) => {
        query.where('sender_id', userId).orWhere('recipient_id', userId);
      })
      .where('status', 'active')
      .where('next_execution', '<=', futureDate.toSQL())
      .where('next_execution', '>', DateTime.now().toSQL())
      .orderBy('next_execution')
      .preload('sender')
      .preload('recipient');

    return transfers.map(transfer => ({
      recurringId: transfer.recurringId,
      amount: transfer.amount,
      currency: transfer.currencyCode,
      frequency: transfer.frequency,
      nextExecution: transfer.nextExecution,
      daysUntilExecution: Math.ceil(DateTime.fromJSDate(transfer.nextExecution).diff(DateTime.now(), 'days').days),
      isOutgoing: transfer.senderId === userId,
      counterparty: transfer.senderId === userId ? transfer.recipient : transfer.sender
    }));
  }

  /**
   * Retry failed transfers
   */
  async retryFailedTransfers() {
    const failedTransfers = await RecurringTransfer.query()
      .where('status', 'failed')
      .where('retry_count', '<', 'max_retries');

    const results = [];

    for (const transfer of failedTransfers) {
      try {
        // Reset retry count and status
        await transfer.resetRetryCount();
        
        // Try to execute
        const result = await this.executeTransfer(transfer);
        results.push({
          recurringId: transfer.recurringId,
          status: 'retry_success',
          transactionId: result.transaction.id
        });
      } catch (error) {
        await transfer.markFailed(error.message);
        results.push({
          recurringId: transfer.recurringId,
          status: 'retry_failed',
          error: error.message
        });
      }
    }

    return results;
  }
}

export default new RecurringTransferService();

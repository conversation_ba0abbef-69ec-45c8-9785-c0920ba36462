@layout('emails/generic_email')

@section('content')
<h1>Hello {{ userName }},</h1>

<p>Great news! Your fundraising pool has received a new contribution!</p>

<div style="background-color: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
    <h3 style="margin-top: 0; color: #155724;">Contribution Details:</h3>
    <ul style="list-style: none; padding: 0;">
        <li><strong>Pool:</strong> {{ poolTitle }}</li>
        <li><strong>Contribution:</strong> {{ amount }} {{ currency }}</li>
        <li><strong>From:</strong> {{ contributorName }}</li>
        <li><strong>Current Total:</strong> {{ currentAmount }} {{ currency }}</li>
        <li><strong>Target:</strong> {{ targetAmount }} {{ currency }}</li>
    </ul>
</div>

<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h4 style="margin-top: 0;">Progress Update:</h4>
    <div style="background-color: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0;">
        <div style="background-color: #28a745; height: 20px; width: {{ progressPercentage }}%; transition: width 0.3s ease;"></div>
    </div>
    <p style="margin: 5px 0; text-align: center; font-weight: bold;">{{ progressPercentage }}% of target reached</p>
</div>

<p>You're getting closer to your fundraising goal! Keep sharing your pool to reach more potential contributors.</p>

@if(progressPercentage >= 100)
<p style="background-color: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; color: #155724;">
    <strong>🎉 Congratulations!</strong> You've reached your fundraising target! The funds will be distributed according to your pool settings.
</p>
@endif

<p>Click the button below to view your fundraising pool:</p>
@endsection

@section('action')
<a href="{{ actionUrl }}" style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
    View Pool
</a>
@endsection

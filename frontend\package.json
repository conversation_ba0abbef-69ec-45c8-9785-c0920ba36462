{"name": "pay-snap", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --check --ignore-path .gitignore .", "format:fix": "prettier --write --ignore-path .gitignore .", "prepare": "husky"}, "dependencies": {"@adonisjs/transmit-client": "^1.0.0", "@bprogress/next": "^3.2.12", "@fingerprintjs/fingerprintjs": "^4.5.0", "@hookform/resolvers": "^3.4.2", "@next/third-parties": "^15.1.7", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.1", "@tanstack/react-query": "^5.8.4", "@tanstack/react-table": "^8.17.3", "@tawk.to/tawk-messenger-react": "^2.0.2", "apexcharts": "^4.5.0", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "formik": "^2.4.6", "html2canvas": "^1.4.1", "i18next": "^23.15.1", "i18next-http-backend": "^2.6.1", "iconsax-react": "^0.0.8", "input-otp": "^1.2.4", "jose": "^5.9.3", "libphonenumber-js": "^1.11.7", "lucide-react": "^0.379.0", "next": "^14.2.24", "next-themes": "^0.3.0", "qrcode.react": "^4.0.1", "rc-pagination": "^4.2.0", "react": "^18", "react-apexcharts": "^1.7.0", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-hook-form": "^7.51.5", "react-i18next": "^15.0.2", "react-infinite-scroll-component": "^6.1.0", "react-jss": "^10.10.0", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9", "sass": "^1.77.2", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "ts-pattern": "^5.2.0", "vaul": "^0.9.1", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-window": "^1.8.8", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-next": "14.2.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.33.2", "husky": "^9.0.11", "postcss": "^8", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.1", "typescript": "^5"}}
"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { AdminDashboardOverview } from "@/types/admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Shield, 
  AlertTriangle,
  Activity,
  RefreshCw,
  Download,
  Settings
} from "lucide-react";
import { SystemHealthCard } from "./SystemHealthCard";
import { PerformanceMetricsCard } from "./PerformanceMetricsCard";
import { RecentActivitiesCard } from "./RecentActivitiesCard";
import { AdminAlertsCard } from "./AdminAlertsCard";

interface AdminDashboardProps {
  overview: AdminDashboardOverview;
  isLoading?: boolean;
  onRefresh?: () => void;
  onExport?: (type: string) => void;
}

export function AdminDashboard({ 
  overview, 
  isLoading = false, 
  onRefresh,
  onExport 
}: AdminDashboardProps) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("overview");

  const getHealthColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHealthBadgeColor = (score: number) => {
    if (score >= 90) return 'bg-green-100 text-green-800 border-green-200';
    if (score >= 70) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('Admin Dashboard')}</h1>
          <p className="text-muted-foreground">
            {t('Monitor and manage your PaySnap platform')}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {t('Refresh')}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onExport?.('dashboard')}
          >
            <Download className="h-4 w-4 mr-2" />
            {t('Export')}
          </Button>
        </div>
      </div>

      {/* System Health Alert */}
      {overview.health.healthScore < 70 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {t('System health is below optimal levels. Please review the issues requiring attention.')}
          </AlertDescription>
        </Alert>
      )}

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('Total Users')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.system.users.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +{overview.period.newUsers} {t('this period')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('Total Volume')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${overview.financial.totalVolume.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('Across all services')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('Active Escrows')}</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.activity.activeEscrows}</div>
            <p className="text-xs text-muted-foreground">
              {overview.activity.successfulTransactions} {t('successful transactions')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('System Health')}</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getHealthColor(overview.health.healthScore)}`}>
              {overview.health.healthScore}%
            </div>
            <Badge 
              variant="outline" 
              className={`text-xs ${getHealthBadgeColor(overview.health.healthScore)}`}
            >
              {overview.health.healthScore >= 90 ? t('Excellent') :
               overview.health.healthScore >= 70 ? t('Good') : t('Needs Attention')}
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">{t('Overview')}</TabsTrigger>
          <TabsTrigger value="health">{t('System Health')}</TabsTrigger>
          <TabsTrigger value="performance">{t('Performance')}</TabsTrigger>
          <TabsTrigger value="alerts">{t('Alerts')}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Financial Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5" />
                  <span>{t('Financial Overview')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">{t('Escrow Volume')}</p>
                    <p className="text-lg font-semibold">
                      ${overview.financial.escrowVolume.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t('Recurring Volume')}</p>
                    <p className="text-lg font-semibold">
                      ${overview.financial.recurringTransferVolume.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t('Fundraising Volume')}</p>
                    <p className="text-lg font-semibold">
                      ${overview.financial.fundraisingVolume.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t('Total Fees')}</p>
                    <p className="text-lg font-semibold">
                      ${overview.financial.totalFees.toLocaleString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Activity Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="h-5 w-5" />
                  <span>{t('Activity Overview')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">{t('Active Escrows')}</p>
                    <p className="text-lg font-semibold">{overview.activity.activeEscrows}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t('Completed Escrows')}</p>
                    <p className="text-lg font-semibold">{overview.activity.completedEscrows}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t('Active Transfers')}</p>
                    <p className="text-lg font-semibold">{overview.activity.activeRecurringTransfers}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t('Active Pools')}</p>
                    <p className="text-lg font-semibold">{overview.activity.activeFundraisingPools}</p>
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">{t('Transaction Success Rate')}</span>
                    <Badge variant="outline" className="bg-green-100 text-green-800">
                      {overview.activity.transactionSuccessRate}%
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activities */}
          <RecentActivitiesCard activities={overview.recentActivities} />
        </TabsContent>

        <TabsContent value="health">
          <SystemHealthCard health={overview.health} />
        </TabsContent>

        <TabsContent value="performance">
          <PerformanceMetricsCard />
        </TabsContent>

        <TabsContent value="alerts">
          <AdminAlertsCard />
        </TabsContent>
      </Tabs>

      {/* Period Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>{t('Period Statistics')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{overview.period.newUsers}</p>
              <p className="text-sm text-muted-foreground">{t('New Users')}</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{overview.period.newEscrows}</p>
              <p className="text-sm text-muted-foreground">{t('New Escrows')}</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">{overview.period.newRecurringTransfers}</p>
              <p className="text-sm text-muted-foreground">{t('New Transfers')}</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">{overview.period.newFundraisingPools}</p>
              <p className="text-sm text-muted-foreground">{t('New Pools')}</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-emerald-600">{overview.period.newTransactions}</p>
              <p className="text-sm text-muted-foreground">{t('New Transactions')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

{"version": 3, "file": "kycs_controller.js", "sourceRoot": "", "sources": ["../../../app/controllers/kycs_controller.ts"], "names": [], "mappings": "AACA,OAAO,GAAG,MAAM,aAAa,CAAC;AAC9B,OAAO,YAAY,MAAM,2BAA2B,CAAC;AACrD,OAAO,EAAE,kBAAkB,EAAE,MAAM,iBAAiB,CAAC;AACrD,OAAO,GAAG,MAAM,6BAA6B,CAAC;AAC9C,OAAO,EAAE,IAAI,EAAE,MAAM,wBAAwB,CAAC;AAC9C,OAAO,oBAAoB,MAAM,gCAAgC,CAAC;AAClE,OAAO,eAAe,MAAM,4BAA4B,CAAC;AAEzD,MAAM,CAAC,OAAO,OAAO,cAAc;IACjC,KAAK,CAAC,KAAK,CAAC,GAAgB;QAC1B,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QACzB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;YACvB,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,GAAgB;QAC9B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QAC/B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,KAAK,EAAE;iBAC3B,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAK,CAAC,EAAE,CAAC;iBAC9B,OAAO,CAAC,MAAM,EAAE,CAAC,SAAS,EAAE,EAAE;gBAC7B,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAChC,CAAC,CAAC;iBACD,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAC1E,CAAC;YACD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,GAAgB;QAC5B,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QAClC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,KAAK,EAAE;iBAC3B,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACxC,OAAO,CAAC,MAAM,EAAE,CAAC,SAAS,EAAE,EAAE;gBAC7B,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAChC,CAAC,CAAC;iBACD,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAC1E,CAAC;YACD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,iBAAiB,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,GAAgB;QACnC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QACxC,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;gBACzB,OAAO,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YAC7E,CAAC;YACD,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;YAE9F,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC;gBAC3B,MAAM,EAAE,IAAI,CAAC,IAAK,CAAC,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,OAAO,GAAwB,EAAE,YAAY,EAAE,CAAC;YAEpD,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;oBAChD,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM,CAAC,OAAO,EAAE;iBACpC,CAAC,CAAC;gBACH,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,QAAS,CAAC;YACpC,CAAC;YACD,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;oBAC/C,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;iBACnC,CAAC,CAAC;gBACH,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,QAAS,CAAC;YAClC,CAAC;YACD,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;oBAC9C,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE;iBAClC,CAAC,CAAC;gBACH,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,QAAS,CAAC;YAChC,CAAC;YAED,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,MAAM,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;gBAC1E,MAAM,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC;gBACjE,OAAO,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;YAClF,CAAC;YAED,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAC1D,MAAM,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC;YAEjE,OAAO,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;QAClF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,wBAAwB,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,GAAgB;QAC9B,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QAClC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,CAAC;YAC1F,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAC1E,CAAC;YACD,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAC/C,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YACjD,MAAM,oBAAoB,CAAC,2BAA2B,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEpE,MAAM,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACvC,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,GAAgB;QAC/B,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QAClC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,CAAC;YAC1F,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAC1E,CAAC;YACD,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAC7C,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAClD,MAAM,oBAAoB,CAAC,2BAA2B,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAKpE,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,mBAAmB,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;CACF"}
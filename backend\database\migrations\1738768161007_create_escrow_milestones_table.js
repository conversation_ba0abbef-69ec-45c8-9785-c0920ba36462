import { BaseSchema } from '@adonisjs/lucid/schema';

export default class extends BaseSchema {
  tableName = 'escrow_milestones';

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id');
      
      // Relationship to escrow
      table
        .integer('escrow_id')
        .unsigned()
        .references('id')
        .inTable('escrows')
        .onDelete('CASCADE')
        .notNullable();
      
      // Milestone details
      table.string('title').notNullable();
      table.text('description').nullable();
      table.integer('order_index').notNullable();
      table.double('amount').notNullable();
      table.double('percentage').notNullable();
      
      // Status and approval
      table.enum('status', [
        'pending',
        'in_progress',
        'completed',
        'approved',
        'rejected',
        'released'
      ]).defaultTo('pending');
      
      // Approval tracking
      table.boolean('sender_approved').defaultTo(false);
      table.boolean('recipient_approved').defaultTo(false);
      table.timestamp('sender_approved_at').nullable();
      table.timestamp('recipient_approved_at').nullable();
      
      // Release information
      table.timestamp('released_at').nullable();
      table.integer('released_by_user_id').unsigned().nullable();
      
      // Deliverables and evidence
      table.json('deliverables').nullable(); // Expected deliverables
      table.json('evidence').nullable(); // Submitted evidence/proof
      table.text('completion_notes').nullable();
      table.text('rejection_reason').nullable();
      
      // Timing
      table.timestamp('due_date').nullable();
      table.timestamp('completed_at').nullable();
      
      table.timestamp('created_at');
      table.timestamp('updated_at');
      
      // Indexes
      table.index(['escrow_id', 'order_index']);
      table.index(['escrow_id', 'status']);
      table.index(['status']);
    });
  }

  async down() {
    this.schema.dropTable(this.tableName);
  }
}

"use client";

import { useTranslation } from "react-i18next";
import { Escrow } from "@/types/escrow";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  CheckCircle, 
  Clock, 
  DollarSign, 
  Shield, 
  XCircle, 
  AlertTriangle,
  Plus,
  Target
} from "lucide-react";
import { format } from "date-fns";

interface EscrowTimelineProps {
  escrow: Escrow;
}

interface TimelineEvent {
  id: string;
  type: 'created' | 'confirmed' | 'funded' | 'milestone' | 'released' | 'cancelled' | 'expired';
  title: string;
  description: string;
  timestamp: Date;
  icon: React.ReactNode;
  status: 'completed' | 'pending' | 'failed';
}

export function EscrowTimeline({ escrow }: EscrowTimelineProps) {
  const { t } = useTranslation();

  const generateTimelineEvents = (): TimelineEvent[] => {
    const events: TimelineEvent[] = [];

    // Escrow Created
    events.push({
      id: 'created',
      type: 'created',
      title: t('Escrow Created'),
      description: t('Escrow was created with amount {{amount}} {{currency}}', {
        amount: escrow.amount.toLocaleString(),
        currency: escrow.currencyCode
      }),
      timestamp: escrow.createdAt,
      icon: <Plus className="h-4 w-4" />,
      status: 'completed'
    });

    // Sender Confirmation
    if (escrow.senderConfirmed) {
      events.push({
        id: 'sender-confirmed',
        type: 'confirmed',
        title: t('Sender Confirmed'),
        description: t('Sender has confirmed the escrow terms'),
        timestamp: escrow.createdAt, // In real implementation, this would be the actual confirmation timestamp
        icon: <CheckCircle className="h-4 w-4" />,
        status: 'completed'
      });
    } else if (escrow.status !== 'cancelled') {
      events.push({
        id: 'sender-confirmation-pending',
        type: 'confirmed',
        title: t('Sender Confirmation'),
        description: t('Waiting for sender to confirm escrow terms'),
        timestamp: escrow.createdAt,
        icon: <Clock className="h-4 w-4" />,
        status: 'pending'
      });
    }

    // Recipient Confirmation
    if (escrow.recipientConfirmed) {
      events.push({
        id: 'recipient-confirmed',
        type: 'confirmed',
        title: t('Recipient Confirmed'),
        description: t('Recipient has confirmed the escrow terms'),
        timestamp: escrow.createdAt, // In real implementation, this would be the actual confirmation timestamp
        icon: <CheckCircle className="h-4 w-4" />,
        status: 'completed'
      });
    } else if (escrow.status !== 'cancelled') {
      events.push({
        id: 'recipient-confirmation-pending',
        type: 'confirmed',
        title: t('Recipient Confirmation'),
        description: t('Waiting for recipient to confirm escrow terms'),
        timestamp: escrow.createdAt,
        icon: <Clock className="h-4 w-4" />,
        status: 'pending'
      });
    }

    // Escrow Funded (when both parties confirm)
    if (escrow.status === 'active' || escrow.status === 'completed') {
      events.push({
        id: 'funded',
        type: 'funded',
        title: t('Escrow Funded'),
        description: t('Funds have been secured in escrow'),
        timestamp: escrow.createdAt, // In real implementation, this would be the funding timestamp
        icon: <Shield className="h-4 w-4" />,
        status: 'completed'
      });
    }

    // Milestones
    if (escrow.hasMilestones && escrow.milestones) {
      escrow.milestones.forEach((milestone, index) => {
        if (milestone.completedAt) {
          events.push({
            id: `milestone-${milestone.id}-completed`,
            type: 'milestone',
            title: t('Milestone {{number}} Completed', { number: index + 1 }),
            description: milestone.title,
            timestamp: milestone.completedAt,
            icon: <Target className="h-4 w-4" />,
            status: 'completed'
          });
        }

        if (milestone.releasedAt) {
          events.push({
            id: `milestone-${milestone.id}-released`,
            type: 'released',
            title: t('Milestone {{number}} Released', { number: index + 1 }),
            description: t('Funds for milestone "{{title}}" have been released', { title: milestone.title }),
            timestamp: milestone.releasedAt,
            icon: <DollarSign className="h-4 w-4" />,
            status: 'completed'
          });
        }
      });
    }

    // Final Release
    if (escrow.status === 'completed' && escrow.releasedAt) {
      events.push({
        id: 'released',
        type: 'released',
        title: t('Funds Released'),
        description: t('All funds have been released to the recipient'),
        timestamp: escrow.releasedAt,
        icon: <DollarSign className="h-4 w-4" />,
        status: 'completed'
      });
    } else if (escrow.status === 'active' && !escrow.hasMilestones) {
      events.push({
        id: 'release-pending',
        type: 'released',
        title: t('Fund Release'),
        description: t('Waiting for sender to release funds'),
        timestamp: escrow.deadline,
        icon: <Clock className="h-4 w-4" />,
        status: 'pending'
      });
    }

    // Cancellation
    if (escrow.status === 'cancelled') {
      events.push({
        id: 'cancelled',
        type: 'cancelled',
        title: t('Escrow Cancelled'),
        description: t('Escrow has been cancelled'),
        timestamp: escrow.updatedAt,
        icon: <XCircle className="h-4 w-4" />,
        status: 'failed'
      });
    }

    // Expiration
    if (escrow.status === 'expired') {
      events.push({
        id: 'expired',
        type: 'expired',
        title: t('Escrow Expired'),
        description: t('Escrow has expired past the deadline'),
        timestamp: escrow.deadline,
        icon: <AlertTriangle className="h-4 w-4" />,
        status: 'failed'
      });
    }

    // Sort events by timestamp
    return events.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  };

  const events = generateTimelineEvents();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getConnectorColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'pending':
        return 'bg-yellow-500';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-300';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('Escrow Timeline')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          {events.map((event, index) => (
            <div key={event.id} className="relative flex items-start space-x-4 pb-8">
              {/* Timeline Connector */}
              {index < events.length - 1 && (
                <div 
                  className={`absolute left-4 top-8 w-0.5 h-full ${getConnectorColor(event.status)}`}
                  style={{ transform: 'translateX(-50%)' }}
                />
              )}

              {/* Event Icon */}
              <div className={`relative z-10 flex items-center justify-center w-8 h-8 rounded-full border-2 ${getStatusColor(event.status)}`}>
                {event.icon}
              </div>

              {/* Event Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-900">
                    {event.title}
                  </h4>
                  <Badge 
                    variant="outline" 
                    className={`${getStatusColor(event.status)} text-xs`}
                  >
                    {t(event.status)}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {event.description}
                </p>
                <p className="text-xs text-gray-500 mt-2">
                  {format(event.timestamp, 'MMM dd, yyyy HH:mm')}
                </p>
              </div>
            </div>
          ))}
        </div>

        {events.length === 0 && (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('No Timeline Events')}
            </h3>
            <p className="text-gray-600">
              {t('Timeline events will appear here as the escrow progresses.')}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

import { DateTime } from 'luxon';
import { BaseModel, column, belongsTo, computed } from '@adonisjs/lucid/orm';
import Escrow from './escrow.js';
import User from './user.js';

export default class EscrowNotification extends BaseModel {
  @column({ isPrimary: true })
  id;

  @column()
  escrowId;

  @column()
  type;

  @column()
  userId;

  @column()
  status;

  @column.dateTime()
  scheduledAt;

  @column.dateTime()
  sentAt;

  @column()
  retryCount;

  @column()
  errorMessage;

  @column()
  title;

  @column()
  message;

  @column()
  data;

  @column()
  emailSent;

  @column()
  pushSent;

  @column()
  smsSent;

  @column.dateTime({ autoCreate: true })
  createdAt;

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  updatedAt;

  // Relationships
  @belongsTo(() => Escrow)
  escrow;

  @belongsTo(() => User)
  user;

  // Computed properties
  @computed()
  get dataParsed() {
    return typeof this.data !== 'object' 
      ? JSON.parse(this?.data || '{}') 
      : this?.data;
  }

  @computed()
  get isPending() {
    return this.status === 'pending';
  }

  @computed()
  get isSent() {
    return this.status === 'sent';
  }

  @computed()
  get isFailed() {
    return this.status === 'failed';
  }

  @computed()
  get shouldSend() {
    return this.isPending && DateTime.now() >= DateTime.fromJSDate(this.scheduledAt);
  }

  @computed()
  get isOverdue() {
    return this.isPending && DateTime.now() > DateTime.fromJSDate(this.scheduledAt);
  }

  // Instance methods
  async markSent() {
    this.status = 'sent';
    this.sentAt = DateTime.now();
    await this.save();
  }

  async markFailed(errorMessage) {
    this.status = 'failed';
    this.errorMessage = errorMessage;
    this.retryCount += 1;
    await this.save();
  }

  async cancel() {
    this.status = 'cancelled';
    await this.save();
  }

  async retry() {
    this.status = 'pending';
    this.errorMessage = null;
    await this.save();
  }

  async updateDeliveryStatus(channel, success) {
    switch (channel) {
      case 'email':
        this.emailSent = success;
        break;
      case 'push':
        this.pushSent = success;
        break;
      case 'sms':
        this.smsSent = success;
        break;
    }
    await this.save();
  }

  // Static methods
  static async createReminderNotification(escrowId, userId, type, scheduledAt) {
    const escrow = await Escrow.find(escrowId);
    if (!escrow) throw new Error('Escrow not found');

    const titles = {
      'deadline_reminder_24h': 'Escrow Deadline Reminder - 24 Hours',
      'deadline_reminder_6h': 'Escrow Deadline Reminder - 6 Hours',
      'deadline_reminder_1h': 'Escrow Deadline Reminder - 1 Hour'
    };

    const messages = {
      'deadline_reminder_24h': `Your escrow ${escrow.escrowId} will expire in 24 hours. Please take action to avoid automatic release.`,
      'deadline_reminder_6h': `Your escrow ${escrow.escrowId} will expire in 6 hours. Please take action to avoid automatic release.`,
      'deadline_reminder_1h': `Your escrow ${escrow.escrowId} will expire in 1 hour. Please take action to avoid automatic release.`
    };

    return await this.create({
      escrowId,
      userId,
      type,
      scheduledAt,
      title: titles[type] || 'Escrow Notification',
      message: messages[type] || 'You have an escrow notification.',
      data: JSON.stringify({
        escrowId: escrow.escrowId,
        amount: escrow.amount,
        currency: escrow.currencyCode,
        deadline: escrow.deadline
      })
    });
  }

  static async createEscrowNotification(escrowId, userId, type, data = {}) {
    const escrow = await Escrow.find(escrowId);
    if (!escrow) throw new Error('Escrow not found');

    const titles = {
      'escrow_created': 'New Escrow Created',
      'escrow_funded': 'Escrow Funded',
      'escrow_confirmed': 'Escrow Confirmed',
      'escrow_released': 'Escrow Released',
      'escrow_expired': 'Escrow Expired',
      'milestone_completed': 'Milestone Completed',
      'milestone_approved': 'Milestone Approved',
      'milestone_rejected': 'Milestone Rejected'
    };

    const messages = {
      'escrow_created': `A new escrow ${escrow.escrowId} has been created.`,
      'escrow_funded': `Escrow ${escrow.escrowId} has been funded.`,
      'escrow_confirmed': `Escrow ${escrow.escrowId} has been confirmed by both parties.`,
      'escrow_released': `Escrow ${escrow.escrowId} has been released.`,
      'escrow_expired': `Escrow ${escrow.escrowId} has expired.`,
      'milestone_completed': `A milestone in escrow ${escrow.escrowId} has been completed.`,
      'milestone_approved': `A milestone in escrow ${escrow.escrowId} has been approved.`,
      'milestone_rejected': `A milestone in escrow ${escrow.escrowId} has been rejected.`
    };

    return await this.create({
      escrowId,
      userId,
      type,
      scheduledAt: DateTime.now(),
      title: titles[type] || 'Escrow Notification',
      message: messages[type] || 'You have an escrow notification.',
      data: JSON.stringify({
        escrowId: escrow.escrowId,
        amount: escrow.amount,
        currency: escrow.currencyCode,
        ...data
      })
    });
  }

  static async getPendingNotifications() {
    return await this.query()
      .where('status', 'pending')
      .where('scheduled_at', '<=', DateTime.now().toSQL())
      .preload('escrow')
      .preload('user');
  }

  static async getFailedNotifications(maxRetries = 3) {
    return await this.query()
      .where('status', 'failed')
      .where('retry_count', '<', maxRetries)
      .preload('escrow')
      .preload('user');
  }
}

{"version": 3, "file": "pdf_service.js", "sourceRoot": "", "sources": ["../../../app/services/pdf_service.ts"], "names": [], "mappings": "AAAA,OAAO,WAAW,MAAM,QAAQ,CAAC;AACjC,OAAO,WAAW,MAAM,qBAAqB,CAAC;AAE9C,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAC;AACtC,OAAO,eAAe,MAAM,uBAAuB,CAAC;AAEpD,MAAM,mBAAmB;IACf,aAAa,CAAS;IACtB,IAAI,CAAO;IACX,GAAG,CAAM;IACT,OAAO,CAAW;IAE1B,YAAY,aAAqB,EAAE,IAAU;QAC3C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,GAAG,GAAG,IAAI,WAAW,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAElB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAClF,CAAC;IAEO,gBAAgB;QACtB,MAAM,QAAQ,GAAG,GAAG,CAAC;QACrB,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,OAAO,GAAG,GAAG,CAAC;QACpB,MAAM,UAAU,GAAG,GAAG,CAAC;QACvB,MAAM,SAAS,GAAG,EAAE,CAAC;QAErB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC;QAEvE,MAAM,YAAY,GAAG;YACnB,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YACjD,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,KAAK,EAAE;SACpE,CAAC;QAEF,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACnC,MAAM,IAAI,GAAG,QAAQ,GAAG,KAAK,GAAG,SAAS,CAAC;YAC1C,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC;YAE7D,IAAI,CAAC,GAAG;iBACL,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC;iBACrB,MAAM,CAAC,OAAO,EAAE,IAAI,GAAG,SAAS,CAAC;iBACjC,MAAM,EAAE,CAAC;YAEZ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,OAAO,GAAG,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;YACxD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,EAAE,OAAO,GAAG,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB,CAAC,WAAwB;QAClD,MAAM,QAAQ,GAAG,GAAG,CAAC;QACrB,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,OAAO,GAAG,GAAG,CAAC;QACpB,MAAM,UAAU,GAAG,GAAG,CAAC;QACvB,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,MAAM,eAAe,GAAG,QAAQ,GAAG,EAAE,GAAG,EAAE,CAAC;QAC3C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,yBAAyB,EAAE,OAAO,EAAE,eAAe,GAAG,EAAE,CAAC,CAAC;QACrF,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAEvD,eAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACvC,MAAM,IAAI,GAAG,eAAe,GAAG,KAAK,GAAG,SAAS,CAAC;YACjD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC;YAE7D,IAAI,CAAC,GAAG;iBACL,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC;iBACrB,MAAM,CAAC,OAAO,EAAE,IAAI,GAAG,SAAS,CAAC;iBACjC,MAAM,EAAE,CAAC;YAEZ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,OAAO,GAAG,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;YACxD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,EAAE,OAAO,GAAG,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,MAAM,QAAQ,GAAG,GAAG,CAAC;QACrB,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,UAAU,GAAG,GAAG,CAAC;QACvB,MAAM,iBAAiB,GAAG,CAAC,CAAC;QAC5B,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,MAAM,QAAQ,GAAG,MAAM,eAAe,EAAE,CAAC;QACzC,MAAM,eAAe,GAAG,QAAQ,GAAG,iBAAiB,GAAG,SAAS,GAAG,GAAG,CAAC;QACvE,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,OAAO;QACT,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,KAAK,CACZ,iBAAiB,GAAG,QAAQ,CAAC,IAAI,EACjC,OAAO,GAAG,UAAU,GAAG,CAAC,GAAG,EAAE,EAC7B,eAAe,EACf;YACE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;SACd,CACF,CAAC;IACJ,CAAC;IAEO,cAAc;QACpB,MAAM,QAAQ,GAAG,GAAG,CAAC;QACrB,MAAM,iBAAiB,GAAG,CAAC,CAAC;QAC5B,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,SAAS,GAAG,QAAQ,GAAG,iBAAiB,GAAG,SAAS,GAAG,UAAU,GAAG,EAAE,CAAC;QAE7E,IAAI,CAAC,GAAG;aACL,QAAQ,CAAC,EAAE,CAAC;aACZ,IAAI,CAAC,iDAAiD,EAAE,OAAO,EAAE,SAAS,EAAE;YAC3E,KAAK,EAAE,QAAQ;SAChB,CAAC,CAAC;IACP,CAAC;IAEO,YAAY,CAAC,WAAwB;QAC3C,MAAM,QAAQ,GAAwB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAA6B,CAAC,CAAC;QAC5F,MAAM,IAAI,GAAwB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAyB,CAAC,CAAC;QACpF,MAAM,EAAE,GAAwB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAuB,CAAC,CAAC;QAChF,QAAQ,WAAW,CAAC,IAAI,EAAE,CAAC;YACzB,KAAK,SAAS,CAAC;YACf,KAAK,UAAU,CAAC;YAChB,KAAK,gBAAgB;gBACnB,OAAO;oBACL,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,WAAW,CAAC,IAAI,EAAE;oBACtD,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE;oBACrD,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACtE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE;oBAC9C,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,IAAI,KAAK,EAAE;oBAChE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,MAAM,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;oBACxE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;oBAClE;wBACE,KAAK,EAAE,WAAW,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;wBACrE,KAAK,EAAE,GAAG,WAAW,CAAC,KAAK,IAAI,QAAQ,CAAC,QAAQ,EAAE;qBACnD;iBACF,CAAC;YACJ,KAAK,UAAU,CAAC;YAChB,KAAK,SAAS;gBACZ,OAAO;oBACL,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,WAAW,CAAC,IAAI,EAAE;oBACtD,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE;oBACrD,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACtE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE;oBAC9C;wBACE,KAAK,EAAE,WAAW,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU;wBACjE,KAAK,EAAE,EAAE,CAAC,KAAK,IAAI,KAAK;qBACzB;oBACD,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,MAAM,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;oBACxE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;oBAClE,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,KAAK,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;iBAClF,CAAC;YACJ,KAAK,UAAU;gBACb,OAAO;oBACL,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,WAAW,CAAC,IAAI,EAAE;oBACtD,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE;oBACrD,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACtE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE;oBAC9C,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,QAAQ,CAAC,YAAY,EAAE;oBACxD,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;oBACzE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;oBAClE,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,KAAK,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;iBAC1E,CAAC;YACJ,KAAK,UAAU;gBACb,OAAO;oBACL,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,WAAW,CAAC,IAAI,GAAG,KAAK,QAAQ,CAAC,IAAI,IAAI,EAAE,GAAG,EAAE;oBACpF,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE;oBACrD,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACtE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE;oBAC9C,QAAQ,CAAC,IAAI,KAAK,aAAa;wBAC7B,CAAC,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,KAAK,QAAQ,CAAC,WAAW,IAAI,EAAE,GAAG,EAAE;wBACjF,CAAC,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE;oBAC9C,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,MAAM,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;oBACxE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;oBAClE,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,KAAK,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;iBAClF,CAAC;YACJ;gBACE,OAAO;oBACL,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,WAAW,CAAC,IAAI,EAAE;oBACtD,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE;oBACrD,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACtE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE;oBAC9C,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,IAAI,KAAK,EAAE;oBAChE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,MAAM,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;oBACxE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;oBAClE,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,KAAK,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE;iBAC1E,CAAC;QACN,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW;QACf,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/F,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACtC,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QAEf,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAC9F,CAAC;CACF;AAED,eAAe,mBAAmB,CAAC"}
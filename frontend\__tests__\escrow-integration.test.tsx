import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import { EscrowList } from '@/components/escrow/EscrowList';
import { CreateEscrowForm } from '@/components/escrow/CreateEscrowForm';
import { MilestoneList } from '@/components/milestone/MilestoneList';
import { RecurringTransferList } from '@/components/recurring-transfer/RecurringTransferList';
import { FundraisingPoolList } from '@/components/fundraising-pool/FundraisingPoolList';
import { AdminDashboard } from '@/components/admin/AdminDashboard';
import { Escrow } from '@/types/escrow';
import { Milestone } from '@/types/milestone';
import { RecurringTransfer } from '@/types/recurring-transfer';
import { FundraisingPool } from '@/types/fundraising-pool';

// Mock data
const mockEscrow = new Escrow({
  id: 1,
  escrowId: 'ESC-001',
  senderId: 1,
  recipientId: 2,
  amount: 1000,
  currencyCode: 'USD',
  status: 'active',
  description: 'Test escrow',
  deadline: new Date(Date.now() + 86400000), // 24 hours from now
  createdAt: new Date(),
  updatedAt: new Date(),
  senderConfirmed: true,
  recipientConfirmed: true,
  fee: 25,
  feePayer: 'sender',
  hasMilestones: true,
  totalMilestones: 2,
  completedMilestones: 1,
});

const mockMilestone = new Milestone({
  id: 1,
  escrowId: 1,
  title: 'First milestone',
  description: 'Complete initial work',
  percentage: 50,
  amount: 500,
  orderIndex: 1,
  status: 'completed',
  createdAt: new Date(),
  updatedAt: new Date(),
});

const mockRecurringTransfer = new RecurringTransfer({
  id: 1,
  recurringId: 'RT-001',
  senderId: 1,
  recipientId: 2,
  amount: 100,
  currencyCode: 'USD',
  frequency: 'monthly',
  intervalValue: 1,
  status: 'active',
  startDate: new Date(),
  executedCount: 3,
  maxOccurrences: 12,
  createdAt: new Date(),
  updatedAt: new Date(),
});

const mockFundraisingPool = new FundraisingPool({
  id: 1,
  poolId: 'POOL-001',
  creatorId: 1,
  title: 'Help Local Community',
  description: 'Raising funds for community center',
  targetAmount: 10000,
  currentAmount: 3500,
  currencyCode: 'USD',
  status: 'active',
  contributorsCount: 15,
  startDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
});

// Mock API functions
const mockEscrowApi = {
  getEscrows: jest.fn(),
  createEscrow: jest.fn(),
  confirmEscrow: jest.fn(),
  releaseEscrow: jest.fn(),
  cancelEscrow: jest.fn(),
};

// Test suites
describe('Escrow Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('EscrowList Component', () => {
    it('should render escrow list with filters', async () => {
      mockEscrowApi.getEscrows.mockResolvedValue({
        success: true,
        data: [mockEscrow]
      });

      render(
        <EscrowList
          escrows={[mockEscrow]}
          currentUserId={1}
          onFiltersChange={jest.fn()}
          onAction={jest.fn()}
        />
      );

      expect(screen.getByText('Escrows')).toBeInTheDocument();
      expect(screen.getByText('ESC-001')).toBeInTheDocument();
      expect(screen.getByText('$1,000 USD')).toBeInTheDocument();
    });

    it('should handle escrow actions', async () => {
      const mockOnAction = jest.fn();
      
      render(
        <EscrowList
          escrows={[mockEscrow]}
          currentUserId={1}
          onAction={mockOnAction}
        />
      );

      // Test release action
      const releaseButton = screen.getByText('Release Funds');
      fireEvent.click(releaseButton);
      
      expect(mockOnAction).toHaveBeenCalledWith('release', 1);
    });

    it('should filter escrows by status', async () => {
      const mockOnFiltersChange = jest.fn();
      
      render(
        <EscrowList
          escrows={[mockEscrow]}
          currentUserId={1}
          onFiltersChange={mockOnFiltersChange}
        />
      );

      // Open filters
      const filtersButton = screen.getByText('Filters');
      fireEvent.click(filtersButton);

      // Select status filter
      const statusSelect = screen.getByRole('combobox', { name: /status/i });
      fireEvent.click(statusSelect);
      fireEvent.click(screen.getByText('Active'));

      expect(mockOnFiltersChange).toHaveBeenCalledWith({
        status: 'active'
      });
    });
  });

  describe('CreateEscrowForm Component', () => {
    it('should submit escrow creation form', async () => {
      const mockOnSubmit = jest.fn().mockResolvedValue(undefined);
      
      render(
        <CreateEscrowForm
          onSubmit={mockOnSubmit}
        />
      );

      // Fill form fields
      fireEvent.change(screen.getByLabelText(/amount/i), {
        target: { value: '1000' }
      });
      
      fireEvent.change(screen.getByLabelText(/description/i), {
        target: { value: 'Test escrow description' }
      });

      // Submit form
      const submitButton = screen.getByText('Create Escrow');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            amount: 1000,
            description: 'Test escrow description'
          })
        );
      });
    });

    it('should validate required fields', async () => {
      render(
        <CreateEscrowForm
          onSubmit={jest.fn()}
        />
      );

      // Try to submit without filling required fields
      const submitButton = screen.getByText('Create Escrow');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Amount is required')).toBeInTheDocument();
        expect(screen.getByText('Please select a recipient')).toBeInTheDocument();
      });
    });
  });

  describe('MilestoneList Component', () => {
    it('should render milestone progress', () => {
      render(
        <MilestoneList
          escrowId={1}
          milestones={[mockMilestone]}
          userRole="recipient"
          onAction={jest.fn()}
        />
      );

      expect(screen.getByText('Milestones')).toBeInTheDocument();
      expect(screen.getByText('First milestone')).toBeInTheDocument();
      expect(screen.getByText('50%')).toBeInTheDocument();
    });

    it('should handle milestone completion', async () => {
      const mockOnAction = jest.fn();
      
      render(
        <MilestoneList
          escrowId={1}
          milestones={[{
            ...mockMilestone,
            status: 'in_progress',
            canBeCompleted: true
          }]}
          userRole="recipient"
          onAction={mockOnAction}
        />
      );

      const completeButton = screen.getByText('Mark Complete');
      fireEvent.click(completeButton);

      expect(mockOnAction).toHaveBeenCalledWith('complete', 1);
    });
  });

  describe('RecurringTransferList Component', () => {
    it('should render recurring transfers', () => {
      render(
        <RecurringTransferList
          transfers={[mockRecurringTransfer]}
          currentUserId={1}
          onAction={jest.fn()}
        />
      );

      expect(screen.getByText('Recurring Transfers')).toBeInTheDocument();
      expect(screen.getByText('RT-001')).toBeInTheDocument();
      expect(screen.getByText('$100 USD')).toBeInTheDocument();
    });

    it('should handle transfer actions', () => {
      const mockOnAction = jest.fn();
      
      render(
        <RecurringTransferList
          transfers={[mockRecurringTransfer]}
          currentUserId={1}
          onAction={mockOnAction}
        />
      );

      // Test pause action
      const pauseButton = screen.getByText('Pause');
      fireEvent.click(pauseButton);

      expect(mockOnAction).toHaveBeenCalledWith('pause', 1);
    });
  });

  describe('FundraisingPoolList Component', () => {
    it('should render fundraising pools', () => {
      render(
        <FundraisingPoolList
          pools={[mockFundraisingPool]}
          currentUserId={1}
          onAction={jest.fn()}
        />
      );

      expect(screen.getByText('Fundraising Pools')).toBeInTheDocument();
      expect(screen.getByText('Help Local Community')).toBeInTheDocument();
      expect(screen.getByText('35%')).toBeInTheDocument(); // Progress percentage
    });

    it('should handle pool contributions', () => {
      const mockOnAction = jest.fn();
      
      render(
        <FundraisingPoolList
          pools={[mockFundraisingPool]}
          currentUserId={1}
          onAction={mockOnAction}
        />
      );

      const contributeButton = screen.getByText('Contribute');
      fireEvent.click(contributeButton);

      expect(mockOnAction).toHaveBeenCalledWith('contribute', 1);
    });
  });

  describe('AdminDashboard Component', () => {
    const mockOverview = {
      system: {
        users: 1250,
        escrows: 450,
        recurringTransfers: 120,
        fundraisingPools: 35,
        transactions: 2800
      },
      period: {
        newUsers: 45,
        newEscrows: 12,
        newRecurringTransfers: 8,
        newFundraisingPools: 3,
        newTransactions: 156
      },
      activity: {
        activeEscrows: 89,
        completedEscrows: 340,
        activeRecurringTransfers: 95,
        activeFundraisingPools: 28,
        successfulTransactions: 2650,
        failedTransactions: 150,
        transactionSuccessRate: 94.6
      },
      financial: {
        escrowVolume: 2500000,
        recurringTransferVolume: 850000,
        fundraisingVolume: 320000,
        totalFees: 45000,
        totalVolume: 3670000
      },
      health: {
        pendingNotifications: 25,
        failedNotifications: 3,
        expiredEscrows: 2,
        failedRecurringTransfers: 5,
        expiredFundraisingPools: 1,
        totalIssues: 36,
        healthScore: 87
      },
      recentActivities: {
        recentEscrows: [],
        recentHighValueTransactions: [],
        recentFundraisingPools: []
      }
    };

    it('should render admin dashboard overview', () => {
      render(
        <AdminDashboard
          overview={mockOverview}
          onRefresh={jest.fn()}
          onExport={jest.fn()}
        />
      );

      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
      expect(screen.getByText('1,250')).toBeInTheDocument(); // Total users
      expect(screen.getByText('87%')).toBeInTheDocument(); // Health score
    });

    it('should handle dashboard refresh', () => {
      const mockOnRefresh = jest.fn();
      
      render(
        <AdminDashboard
          overview={mockOverview}
          onRefresh={mockOnRefresh}
          onExport={jest.fn()}
        />
      );

      const refreshButton = screen.getByText('Refresh');
      fireEvent.click(refreshButton);

      expect(mockOnRefresh).toHaveBeenCalled();
    });
  });
});

// Integration workflow tests
describe('End-to-End Workflow Tests', () => {
  it('should complete full escrow workflow', async () => {
    // This would test the complete escrow lifecycle:
    // 1. Create escrow
    // 2. Both parties confirm
    // 3. Complete milestones
    // 4. Release funds
    
    // Mock the API calls for each step
    const createResponse = { success: true, data: mockEscrow };
    const confirmResponse = { success: true, data: { ...mockEscrow, status: 'active' } };
    const releaseResponse = { success: true, data: { ...mockEscrow, status: 'completed' } };

    mockEscrowApi.createEscrow.mockResolvedValue(createResponse);
    mockEscrowApi.confirmEscrow.mockResolvedValue(confirmResponse);
    mockEscrowApi.releaseEscrow.mockResolvedValue(releaseResponse);

    // Test each step of the workflow
    expect(createResponse.success).toBe(true);
    expect(confirmResponse.data.status).toBe('active');
    expect(releaseResponse.data.status).toBe('completed');
  });

  it('should handle error scenarios gracefully', async () => {
    // Test error handling throughout the application
    const errorResponse = {
      success: false,
      message: 'Insufficient funds',
      statusCode: 400
    };

    mockEscrowApi.createEscrow.mockRejectedValue(errorResponse);

    try {
      await mockEscrowApi.createEscrow({});
    } catch (error) {
      expect(error.message).toBe('Insufficient funds');
    }
  });
});

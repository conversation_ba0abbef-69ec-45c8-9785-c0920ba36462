import { DateTime } from 'luxon';
import EscrowNotification from '#models/escrow_notification';
import Escrow from '#models/escrow';
import User from '#models/user';
import notification_service from '#services/notification_service';

class EscrowNotificationService {
  /**
   * Process pending escrow notifications
   */
  async processPendingNotifications() {
    const pendingNotifications = await EscrowNotification.getPendingNotifications();
    const results = [];

    for (const notification of pendingNotifications) {
      try {
        await this.sendNotification(notification);
        results.push({ 
          id: notification.id, 
          status: 'sent', 
          type: notification.type 
        });
      } catch (error) {
        await notification.markFailed(error.message);
        results.push({ 
          id: notification.id, 
          status: 'failed', 
          error: error.message,
          type: notification.type 
        });
      }
    }

    return results;
  }

  /**
   * Send a specific notification
   */
  async sendNotification(notification) {
    const user = notification.user;
    const escrow = notification.escrow;

    if (!user || !escrow) {
      throw new Error('Invalid notification: missing user or escrow data');
    }

    // Prepare notification data
    const notificationData = {
      title: notification.title,
      message: notification.message,
      data: notification.dataParsed,
      userId: user.id,
      type: 'escrow',
      subType: notification.type
    };

    // Send email notification
    if (user.email && !notification.emailSent) {
      try {
        await this.sendEmailNotification(user, escrow, notification);
        await notification.updateDeliveryStatus('email', true);
      } catch (error) {
        console.error('Email notification failed:', error);
      }
    }

    // Send push notification
    if (!notification.pushSent) {
      try {
        await this.sendPushNotification(user, notificationData);
        await notification.updateDeliveryStatus('push', true);
      } catch (error) {
        console.error('Push notification failed:', error);
      }
    }

    // Mark notification as sent
    await notification.markSent();
  }

  /**
   * Send email notification
   */
  async sendEmailNotification(user, escrow, notification) {
    const emailTemplates = {
      'deadline_reminder_24h': {
        subject: 'Escrow Deadline Reminder - 24 Hours',
        template: 'escrow_deadline_reminder',
        data: {
          userName: user.name || user.email,
          escrowId: escrow.escrowId,
          amount: escrow.amount,
          currency: escrow.currencyCode,
          deadline: escrow.deadline,
          hoursRemaining: 24,
          actionUrl: `${process.env.FRONTEND_URL}/escrow/${escrow.escrowId}`
        }
      },
      'deadline_reminder_6h': {
        subject: 'Escrow Deadline Reminder - 6 Hours',
        template: 'escrow_deadline_reminder',
        data: {
          userName: user.name || user.email,
          escrowId: escrow.escrowId,
          amount: escrow.amount,
          currency: escrow.currencyCode,
          deadline: escrow.deadline,
          hoursRemaining: 6,
          actionUrl: `${process.env.FRONTEND_URL}/escrow/${escrow.escrowId}`
        }
      },
      'deadline_reminder_1h': {
        subject: 'Escrow Deadline Reminder - 1 Hour',
        template: 'escrow_deadline_reminder',
        data: {
          userName: user.name || user.email,
          escrowId: escrow.escrowId,
          amount: escrow.amount,
          currency: escrow.currencyCode,
          deadline: escrow.deadline,
          hoursRemaining: 1,
          actionUrl: `${process.env.FRONTEND_URL}/escrow/${escrow.escrowId}`
        }
      },
      'escrow_created': {
        subject: 'New Escrow Created',
        template: 'escrow_created',
        data: {
          userName: user.name || user.email,
          escrowId: escrow.escrowId,
          amount: escrow.amount,
          currency: escrow.currencyCode,
          senderName: escrow.sender?.name || escrow.sender?.email,
          actionUrl: `${process.env.FRONTEND_URL}/escrow/${escrow.escrowId}`
        }
      },
      'escrow_funded': {
        subject: 'Escrow Funded',
        template: 'escrow_funded',
        data: {
          userName: user.name || user.email,
          escrowId: escrow.escrowId,
          amount: escrow.amount,
          currency: escrow.currencyCode,
          actionUrl: `${process.env.FRONTEND_URL}/escrow/${escrow.escrowId}`
        }
      },
      'escrow_confirmed': {
        subject: 'Escrow Confirmed',
        template: 'escrow_confirmed',
        data: {
          userName: user.name || user.email,
          escrowId: escrow.escrowId,
          amount: escrow.amount,
          currency: escrow.currencyCode,
          actionUrl: `${process.env.FRONTEND_URL}/escrow/${escrow.escrowId}`
        }
      },
      'escrow_released': {
        subject: 'Escrow Released',
        template: 'escrow_released',
        data: {
          userName: user.name || user.email,
          escrowId: escrow.escrowId,
          amount: escrow.amount,
          currency: escrow.currencyCode,
          actionUrl: `${process.env.FRONTEND_URL}/escrow/${escrow.escrowId}`
        }
      },
      'escrow_expired': {
        subject: 'Escrow Expired',
        template: 'escrow_expired',
        data: {
          userName: user.name || user.email,
          escrowId: escrow.escrowId,
          amount: escrow.amount,
          currency: escrow.currencyCode,
          actionUrl: `${process.env.FRONTEND_URL}/escrow/${escrow.escrowId}`
        }
      },
      'milestone_completed': {
        subject: 'Milestone Completed',
        template: 'milestone_completed',
        data: {
          userName: user.name || user.email,
          escrowId: escrow.escrowId,
          milestoneTitle: notification.dataParsed.milestoneTitle,
          orderIndex: notification.dataParsed.orderIndex,
          actionUrl: `${process.env.FRONTEND_URL}/escrow/${escrow.escrowId}/milestones`
        }
      },
      'milestone_approved': {
        subject: 'Milestone Approved',
        template: 'milestone_approved',
        data: {
          userName: user.name || user.email,
          escrowId: escrow.escrowId,
          milestoneTitle: notification.dataParsed.milestoneTitle,
          orderIndex: notification.dataParsed.orderIndex,
          actionUrl: `${process.env.FRONTEND_URL}/escrow/${escrow.escrowId}/milestones`
        }
      },
      'milestone_rejected': {
        subject: 'Milestone Rejected',
        template: 'milestone_rejected',
        data: {
          userName: user.name || user.email,
          escrowId: escrow.escrowId,
          milestoneTitle: notification.dataParsed.milestoneTitle,
          orderIndex: notification.dataParsed.orderIndex,
          rejectionReason: notification.dataParsed.rejectionReason,
          actionUrl: `${process.env.FRONTEND_URL}/escrow/${escrow.escrowId}/milestones`
        }
      }
    };

    const emailConfig = emailTemplates[notification.type];
    if (!emailConfig) {
      throw new Error(`No email template found for notification type: ${notification.type}`);
    }

    // Use existing notification service to send email
    // This assumes the existing notification service has email capabilities
    await notification_service.sendEmail({
      to: user.email,
      subject: emailConfig.subject,
      template: emailConfig.template,
      data: emailConfig.data
    });
  }

  /**
   * Send push notification
   */
  async sendPushNotification(user, notificationData) {
    // Use existing notification service for push notifications
    await notification_service.sendPushNotification(user.id, notificationData);
  }

  /**
   * Check and send deadline reminders
   */
  async checkDeadlineReminders() {
    const activeEscrows = await Escrow.query()
      .where('status', 'active')
      .where('deadline', '>', DateTime.now().toSQL());

    const results = [];

    for (const escrow of activeEscrows) {
      const reminders = escrow.needsReminder;

      // Send 24h reminder
      if (reminders.reminder24h) {
        await this.scheduleReminderNotification(escrow, 'deadline_reminder_24h');
        escrow.reminder24hSent = true;
        await escrow.save();
        results.push({ escrowId: escrow.id, type: '24h_reminder' });
      }

      // Send 6h reminder
      if (reminders.reminder6h) {
        await this.scheduleReminderNotification(escrow, 'deadline_reminder_6h');
        escrow.reminder6hSent = true;
        await escrow.save();
        results.push({ escrowId: escrow.id, type: '6h_reminder' });
      }

      // Send 1h reminder
      if (reminders.reminder1h) {
        await this.scheduleReminderNotification(escrow, 'deadline_reminder_1h');
        escrow.reminder1hSent = true;
        await escrow.save();
        results.push({ escrowId: escrow.id, type: '1h_reminder' });
      }
    }

    return results;
  }

  /**
   * Schedule reminder notification for both parties
   */
  async scheduleReminderNotification(escrow, type) {
    // Create notification for sender
    await EscrowNotification.createReminderNotification(
      escrow.id,
      escrow.senderId,
      type,
      DateTime.now().toJSDate()
    );

    // Create notification for recipient
    await EscrowNotification.createReminderNotification(
      escrow.id,
      escrow.recipientId,
      type,
      DateTime.now().toJSDate()
    );
  }

  /**
   * Retry failed notifications
   */
  async retryFailedNotifications(maxRetries = 3) {
    const failedNotifications = await EscrowNotification.getFailedNotifications(maxRetries);
    const results = [];

    for (const notification of failedNotifications) {
      try {
        await this.sendNotification(notification);
        results.push({ 
          id: notification.id, 
          status: 'retried_success',
          type: notification.type 
        });
      } catch (error) {
        await notification.markFailed(error.message);
        results.push({ 
          id: notification.id, 
          status: 'retried_failed', 
          error: error.message,
          type: notification.type 
        });
      }
    }

    return results;
  }

  /**
   * Cancel scheduled notifications for an escrow
   */
  async cancelEscrowNotifications(escrowId) {
    const pendingNotifications = await EscrowNotification.query()
      .where('escrow_id', escrowId)
      .where('status', 'pending');

    for (const notification of pendingNotifications) {
      await notification.cancel();
    }

    return pendingNotifications.length;
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats(dateRange = 7) {
    const startDate = DateTime.now().minus({ days: dateRange }).toSQL();

    const stats = await EscrowNotification.query()
      .where('created_at', '>=', startDate)
      .select('status', 'type')
      .groupBy('status', 'type')
      .count('* as count');

    const summary = {
      total: 0,
      sent: 0,
      pending: 0,
      failed: 0,
      cancelled: 0,
      byType: {}
    };

    stats.forEach(stat => {
      summary.total += stat.count;
      summary[stat.status] = (summary[stat.status] || 0) + stat.count;
      
      if (!summary.byType[stat.type]) {
        summary.byType[stat.type] = { total: 0, sent: 0, pending: 0, failed: 0, cancelled: 0 };
      }
      summary.byType[stat.type].total += stat.count;
      summary.byType[stat.type][stat.status] = stat.count;
    });

    return summary;
  }
}

export default new EscrowNotificationService();

import { DateTime } from 'luxon';

class RecurringTransferValidator {
  /**
   * Validate recurring transfer creation data
   */
  static validateCreationData(data) {
    const errors = [];

    // Required fields
    if (!data.senderId || typeof data.senderId !== 'number') {
      errors.push('Sender ID is required and must be a number');
    }

    if (!data.recipientId || typeof data.recipientId !== 'number') {
      errors.push('Recipient ID is required and must be a number');
    }

    if (data.senderId === data.recipientId) {
      errors.push('Sender and recipient cannot be the same');
    }

    if (!data.amount || typeof data.amount !== 'number' || data.amount <= 0) {
      errors.push('Amount is required and must be a positive number');
    }

    if (!data.currencyCode || typeof data.currencyCode !== 'string') {
      errors.push('Currency code is required and must be a string');
    }

    if (!data.frequency || !['daily', 'weekly', 'monthly', 'yearly'].includes(data.frequency)) {
      errors.push('Frequency is required and must be daily, weekly, monthly, or yearly');
    }

    if (!data.startDate) {
      errors.push('Start date is required');
    }

    // Validate dates
    if (data.startDate) {
      const startDate = DateTime.fromISO(data.startDate);
      if (!startDate.isValid) {
        errors.push('Invalid start date format');
      } else if (startDate <= DateTime.now()) {
        errors.push('Start date must be in the future');
      }
    }

    if (data.endDate) {
      const endDate = DateTime.fromISO(data.endDate);
      if (!endDate.isValid) {
        errors.push('Invalid end date format');
      } else if (data.startDate) {
        const startDate = DateTime.fromISO(data.startDate);
        if (endDate <= startDate) {
          errors.push('End date must be after start date');
        }
      }
    }

    // Validate interval value
    if (data.intervalValue !== undefined) {
      if (typeof data.intervalValue !== 'number' || data.intervalValue < 1 || data.intervalValue > 365) {
        errors.push('Interval value must be a number between 1 and 365');
      }
    }

    // Validate max occurrences
    if (data.maxOccurrences !== undefined && data.maxOccurrences !== null) {
      if (typeof data.maxOccurrences !== 'number' || data.maxOccurrences < 1 || data.maxOccurrences > 10000) {
        errors.push('Max occurrences must be a number between 1 and 10000');
      }
    }

    // Validate description length
    if (data.description && data.description.length > 500) {
      errors.push('Description must be less than 500 characters');
    }

    // Validate boolean fields
    const booleanFields = ['notifySender', 'notifyRecipient', 'notifyOnFailure'];
    booleanFields.forEach(field => {
      if (data[field] !== undefined && typeof data[field] !== 'boolean') {
        errors.push(`${field} must be a boolean value`);
      }
    });

    // Validate schedule config
    if (data.scheduleConfig && typeof data.scheduleConfig !== 'object') {
      errors.push('Schedule config must be an object');
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Validate recurring transfer schedule
   */
  static validateSchedule(frequency, intervalValue, startDate, endDate = null, maxOccurrences = null) {
    const errors = [];
    const start = DateTime.fromISO(startDate);

    // Validate frequency-specific constraints
    switch (frequency) {
      case 'daily':
        if (intervalValue > 365) {
          errors.push('Daily interval cannot exceed 365 days');
        }
        break;
      case 'weekly':
        if (intervalValue > 52) {
          errors.push('Weekly interval cannot exceed 52 weeks');
        }
        break;
      case 'monthly':
        if (intervalValue > 12) {
          errors.push('Monthly interval cannot exceed 12 months');
        }
        break;
      case 'yearly':
        if (intervalValue > 10) {
          errors.push('Yearly interval cannot exceed 10 years');
        }
        break;
    }

    // Calculate total duration and validate
    if (endDate && maxOccurrences) {
      const end = DateTime.fromISO(endDate);
      const calculatedOccurrences = this.calculateMaxOccurrences(start, end, frequency, intervalValue);
      
      if (maxOccurrences > calculatedOccurrences) {
        errors.push(`Max occurrences (${maxOccurrences}) exceeds what's possible in the given time period (${calculatedOccurrences})`);
      }
    }

    // Validate reasonable limits
    const maxDuration = start.plus({ years: 10 });
    if (endDate) {
      const end = DateTime.fromISO(endDate);
      if (end > maxDuration) {
        errors.push('Recurring transfer duration cannot exceed 10 years');
      }
    }

    if (maxOccurrences && maxOccurrences > 3650) { // ~10 years of daily transfers
      errors.push('Max occurrences cannot exceed 3650');
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Calculate maximum possible occurrences in a time period
   */
  static calculateMaxOccurrences(startDate, endDate, frequency, intervalValue) {
    const start = DateTime.isDateTime(startDate) ? startDate : DateTime.fromISO(startDate);
    const end = DateTime.isDateTime(endDate) ? endDate : DateTime.fromISO(endDate);

    let occurrences = 0;
    let current = start;

    while (current <= end && occurrences < 10000) { // Safety limit
      occurrences++;
      
      switch (frequency) {
        case 'daily':
          current = current.plus({ days: intervalValue });
          break;
        case 'weekly':
          current = current.plus({ weeks: intervalValue });
          break;
        case 'monthly':
          current = current.plus({ months: intervalValue });
          break;
        case 'yearly':
          current = current.plus({ years: intervalValue });
          break;
      }
    }

    return occurrences - 1; // Subtract 1 because we count the start date
  }

  /**
   * Validate transfer execution conditions
   */
  static validateExecutionConditions(recurringTransfer, senderWallet) {
    const errors = [];

    // Check if transfer is active
    if (recurringTransfer.status !== 'active') {
      errors.push('Recurring transfer is not active');
    }

    // Check if it's time to execute
    if (!recurringTransfer.shouldExecute) {
      errors.push('Transfer is not scheduled for execution at this time');
    }

    // Check if transfer is completed
    if (recurringTransfer.isCompleted) {
      errors.push('Recurring transfer has already completed all scheduled executions');
    }

    // Check wallet balance
    const totalAmount = recurringTransfer.amount + recurringTransfer.fee;
    if (senderWallet.balance < totalAmount) {
      errors.push(`Insufficient balance. Required: ${totalAmount}, Available: ${senderWallet.balance}`);
    }

    // Check retry limits
    if (recurringTransfer.retryCount >= recurringTransfer.maxRetries) {
      errors.push('Maximum retry attempts exceeded');
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Validate schedule modification
   */
  static validateScheduleModification(recurringTransfer, newData) {
    const errors = [];

    // Can't modify completed or cancelled transfers
    if (['completed', 'cancelled'].includes(recurringTransfer.status)) {
      errors.push('Cannot modify completed or cancelled recurring transfers');
    }

    // Validate new frequency if provided
    if (newData.frequency && !['daily', 'weekly', 'monthly', 'yearly'].includes(newData.frequency)) {
      errors.push('Invalid frequency');
    }

    // Validate new amount
    if (newData.amount !== undefined) {
      if (typeof newData.amount !== 'number' || newData.amount <= 0) {
        errors.push('Amount must be a positive number');
      }
    }

    // Validate new end date
    if (newData.endDate) {
      const endDate = DateTime.fromISO(newData.endDate);
      if (!endDate.isValid) {
        errors.push('Invalid end date format');
      } else if (endDate <= DateTime.now()) {
        errors.push('End date must be in the future');
      }
    }

    // Validate new max occurrences
    if (newData.maxOccurrences !== undefined) {
      if (newData.maxOccurrences !== null && 
          (typeof newData.maxOccurrences !== 'number' || 
           newData.maxOccurrences < recurringTransfer.executedCount)) {
        errors.push('Max occurrences cannot be less than already executed count');
      }
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Validate user permissions for recurring transfer operations
   */
  static validateUserPermissions(recurringTransfer, userId, action) {
    const isSender = recurringTransfer.senderId === userId;
    const isRecipient = recurringTransfer.recipientId === userId;

    const permissions = {
      'view': isSender || isRecipient,
      'pause': isSender,
      'resume': isSender,
      'cancel': isSender,
      'modify': isSender,
      'view_history': isSender || isRecipient
    };

    if (!permissions[action]) {
      throw new Error(`Invalid action: ${action}`);
    }

    if (!permissions[action]) {
      throw new Error(`Unauthorized: Cannot perform action '${action}' on this recurring transfer`);
    }

    return true;
  }

  /**
   * Validate recurring transfer limits for a user
   */
  static validateUserLimits(userId, existingTransfersCount, newTransferData) {
    const errors = [];

    // Maximum active recurring transfers per user
    const maxActiveTransfers = 50;
    if (existingTransfersCount >= maxActiveTransfers) {
      errors.push(`Maximum active recurring transfers limit reached (${maxActiveTransfers})`);
    }

    // Daily transfer amount limits (this would typically come from user settings or currency limits)
    const maxDailyAmount = 10000; // This should be configurable
    if (newTransferData.frequency === 'daily' && newTransferData.amount > maxDailyAmount) {
      errors.push(`Daily recurring transfer amount cannot exceed ${maxDailyAmount}`);
    }

    // Minimum transfer amount
    const minAmount = 0.01;
    if (newTransferData.amount < minAmount) {
      errors.push(`Transfer amount cannot be less than ${minAmount}`);
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Generate schedule preview
   */
  static generateSchedulePreview(startDate, frequency, intervalValue, maxOccurrences = null, endDate = null, previewCount = 10) {
    const start = DateTime.fromISO(startDate);
    const end = endDate ? DateTime.fromISO(endDate) : null;
    const preview = [];

    let current = start;
    let count = 0;

    while (count < previewCount && (!maxOccurrences || count < maxOccurrences) && (!end || current <= end)) {
      preview.push({
        executionNumber: count + 1,
        scheduledDate: current.toISODate(),
        scheduledTime: current.toISOTime(),
        dayOfWeek: current.toFormat('cccc'),
        isWeekend: current.weekday > 5
      });

      count++;

      switch (frequency) {
        case 'daily':
          current = current.plus({ days: intervalValue });
          break;
        case 'weekly':
          current = current.plus({ weeks: intervalValue });
          break;
        case 'monthly':
          current = current.plus({ months: intervalValue });
          break;
        case 'yearly':
          current = current.plus({ years: intervalValue });
          break;
      }
    }

    return {
      preview,
      totalScheduledExecutions: maxOccurrences || (end ? this.calculateMaxOccurrences(start, end, frequency, intervalValue) : 'Unlimited'),
      estimatedCompletionDate: maxOccurrences ? preview[Math.min(maxOccurrences - 1, preview.length - 1)]?.scheduledDate : (end ? end.toISODate() : 'No end date')
    };
  }
}

export default RecurringTransferValidator;

import { DateTime } from 'luxon';
import Escrow from '#models/escrow';
import EscrowMilestone from '#models/escrow_milestone';
import EscrowNotification from '#models/escrow_notification';
import Transaction from '#models/transaction';
import User from '#models/user';
import Wallet from '#models/wallet';
import Currency from '#models/currency';
import formatPrecision from '#utils/format_precision';
import notification_service from '#services/notification_service';

class EscrowService {
  /**
   * Create a new escrow
   */
  async createEscrow(data) {
    const {
      senderId,
      recipientId,
      amount,
      currencyCode,
      description,
      termsConditions,
      deadlineHours = 72,
      feePayer = 'sender',
      milestones = null
    } = data;

    // Validate users exist
    const sender = await User.find(senderId);
    const recipient = await User.find(recipientId);
    
    if (!sender || !recipient) {
      throw new Error('Invalid sender or recipient');
    }

    if (sender.id === recipient.id) {
      throw new Error('Sender and recipient cannot be the same');
    }

    // Validate currency and get fee information
    const currency = await Currency.query().where('code', currencyCode).first();
    if (!currency) {
      throw new Error('Invalid currency');
    }

    // Calculate fees
    const feeCalculation = await this.calculateFees(amount, currency, feePayer);
    
    // Calculate deadline
    const deadline = DateTime.now().plus({ hours: deadlineHours });

    // Create escrow
    const escrow = await Escrow.create({
      senderId,
      recipientId,
      amount: formatPrecision(amount),
      fee: feeCalculation.totalFee,
      total: feeCalculation.totalAmount,
      currencyCode: currencyCode.toUpperCase(),
      feePayer,
      senderFeeAmount: feeCalculation.senderFee,
      recipientFeeAmount: feeCalculation.recipientFee,
      deadline: deadline.toJSDate(),
      deadlineHours,
      description,
      termsConditions,
      hasMilestones: milestones && milestones.length > 0,
      totalMilestones: milestones ? milestones.length : 0,
      status: 'pending'
    });

    // Create milestones if provided
    if (milestones && milestones.length > 0) {
      await this.createMilestones(escrow.id, milestones);
    }

    // Schedule notifications
    await this.scheduleNotifications(escrow);

    // Create notification for recipient
    await EscrowNotification.createEscrowNotification(
      escrow.id,
      recipientId,
      'escrow_created'
    );

    return escrow;
  }

  /**
   * Fund an escrow (lock funds from sender)
   */
  async fundEscrow(escrowId, senderId) {
    const escrow = await Escrow.query()
      .where('id', escrowId)
      .where('sender_id', senderId)
      .first();

    if (!escrow) {
      throw new Error('Escrow not found or unauthorized');
    }

    if (escrow.status !== 'pending') {
      throw new Error('Escrow cannot be funded in current status');
    }

    // Get sender's wallet
    const senderWallet = await Wallet.query()
      .where('user_id', senderId)
      .where('currency_id', (await Currency.query().where('code', escrow.currencyCode).first()).id)
      .where('default', true)
      .first();

    if (!senderWallet) {
      throw new Error('Sender wallet not found');
    }

    // Check sufficient balance
    if (senderWallet.balance < escrow.total) {
      throw new Error('Insufficient balance');
    }

    // Lock funds by deducting from sender's wallet
    senderWallet.balance = formatPrecision(senderWallet.balance - escrow.total);
    await senderWallet.save();

    // Create transaction record
    const fromData = {
      image: (await escrow.related('sender').query().preload('customer').first()).customer?.profileImage ?? '',
      label: (await escrow.related('sender').query().preload('customer').first()).customer?.name ?? '',
      email: escrow.sender?.email ?? '',
      currency: escrow.currencyCode
    };

    const toData = {
      image: (await escrow.related('recipient').query().preload('customer').first()).customer?.profileImage ?? '',
      label: (await escrow.related('recipient').query().preload('customer').first()).customer?.name ?? '',
      email: escrow.recipient?.email ?? '',
      currency: escrow.currencyCode
    };

    const transaction = await Transaction.create({
      type: 'escrow_fund',
      from: fromData,
      to: toData,
      amount: escrow.amount,
      fee: escrow.fee,
      total: escrow.total,
      status: 'completed',
      escrowId: escrow.id,
      userId: senderId,
      metaData: {
        currency: escrow.currencyCode,
        trxAction: 'escrow_fund',
        escrowId: escrow.escrowId
      }
    });

    // Update escrow status
    escrow.status = 'active';
    await escrow.save();

    // Notify recipient
    await EscrowNotification.createEscrowNotification(
      escrow.id,
      escrow.recipientId,
      'escrow_funded'
    );

    return { escrow, transaction };
  }

  /**
   * Confirm escrow by sender or recipient
   */
  async confirmEscrow(escrowId, userId, userType) {
    const escrow = await Escrow.find(escrowId);
    
    if (!escrow) {
      throw new Error('Escrow not found');
    }

    if (escrow.status !== 'active') {
      throw new Error('Escrow cannot be confirmed in current status');
    }

    // Validate user authorization
    if (userType === 'sender' && escrow.senderId !== userId) {
      throw new Error('Unauthorized: Not the sender');
    }
    
    if (userType === 'recipient' && escrow.recipientId !== userId) {
      throw new Error('Unauthorized: Not the recipient');
    }

    // Confirm based on user type
    if (userType === 'sender') {
      await escrow.confirmBySender();
    } else {
      await escrow.confirmByRecipient();
    }

    // Check if both parties have confirmed
    if (escrow.senderConfirmed && escrow.recipientConfirmed) {
      await EscrowNotification.createEscrowNotification(
        escrow.id,
        escrow.senderId,
        'escrow_confirmed'
      );
      await EscrowNotification.createEscrowNotification(
        escrow.id,
        escrow.recipientId,
        'escrow_confirmed'
      );

      // Auto-release if no milestones
      if (!escrow.hasMilestones && escrow.autoRelease) {
        await this.releaseEscrow(escrow.id, null, 'automatic');
      }
    }

    return escrow;
  }

  /**
   * Release escrow funds to recipient
   */
  async releaseEscrow(escrowId, releasedByUserId = null, releaseType = 'manual') {
    const escrow = await Escrow.find(escrowId);
    
    if (!escrow) {
      throw new Error('Escrow not found');
    }

    if (escrow.status !== 'active') {
      throw new Error('Escrow cannot be released in current status');
    }

    // Validate release conditions
    if (!escrow.canBeReleased) {
      throw new Error('Escrow conditions not met for release');
    }

    // Get recipient's wallet
    const currency = await Currency.query().where('code', escrow.currencyCode).first();
    const recipientWallet = await Wallet.query()
      .where('user_id', escrow.recipientId)
      .where('currency_id', currency.id)
      .where('default', true)
      .first();

    if (!recipientWallet) {
      throw new Error('Recipient wallet not found');
    }

    // Calculate amount to release (amount minus recipient fee if applicable)
    const releaseAmount = escrow.amount - escrow.recipientFeeAmount;

    // Add funds to recipient's wallet
    recipientWallet.balance = formatPrecision(recipientWallet.balance + releaseAmount);
    await recipientWallet.save();

    // Create release transaction
    const fromData = {
      image: (await escrow.related('sender').query().preload('customer').first()).customer?.profileImage ?? '',
      label: (await escrow.related('sender').query().preload('customer').first()).customer?.name ?? '',
      email: escrow.sender?.email ?? '',
      currency: escrow.currencyCode
    };

    const toData = {
      image: (await escrow.related('recipient').query().preload('customer').first()).customer?.profileImage ?? '',
      label: (await escrow.related('recipient').query().preload('customer').first()).customer?.name ?? '',
      email: escrow.recipient?.email ?? '',
      currency: escrow.currencyCode
    };

    const transaction = await Transaction.create({
      type: 'escrow_release',
      from: fromData,
      to: toData,
      amount: releaseAmount,
      fee: 0,
      total: releaseAmount,
      status: 'completed',
      escrowId: escrow.id,
      userId: escrow.recipientId,
      metaData: {
        currency: escrow.currencyCode,
        trxAction: 'escrow_receive',
        escrowId: escrow.escrowId,
        releaseType
      }
    });

    // Update escrow status
    await escrow.release(releaseType, releasedByUserId);

    // Notify both parties
    await EscrowNotification.createEscrowNotification(
      escrow.id,
      escrow.senderId,
      'escrow_released'
    );
    await EscrowNotification.createEscrowNotification(
      escrow.id,
      escrow.recipientId,
      'escrow_released'
    );

    return { escrow, transaction };
  }

  /**
   * Cancel escrow and refund sender
   */
  async cancelEscrow(escrowId, userId, reason = null) {
    const escrow = await Escrow.find(escrowId);
    
    if (!escrow) {
      throw new Error('Escrow not found');
    }

    // Only sender or admin can cancel
    if (escrow.senderId !== userId) {
      // TODO: Add admin role check
      throw new Error('Unauthorized to cancel escrow');
    }

    if (escrow.status !== 'active' && escrow.status !== 'pending') {
      throw new Error('Escrow cannot be cancelled in current status');
    }

    // If escrow was funded, refund the sender
    if (escrow.status === 'active') {
      await this.refundEscrow(escrow);
    }

    // Update escrow status
    await escrow.cancel();

    return escrow;
  }

  /**
   * Handle expired escrows
   */
  async handleExpiredEscrows() {
    const expiredEscrows = await Escrow.query()
      .where('status', 'active')
      .where('deadline', '<', DateTime.now().toSQL())
      .where('auto_release', true);

    const results = [];

    for (const escrow of expiredEscrows) {
      try {
        if (escrow.canBeReleased) {
          // Auto-release if conditions are met
          const result = await this.releaseEscrow(escrow.id, null, 'automatic');
          results.push({ escrowId: escrow.id, action: 'released', result });
        } else {
          // Mark as expired and refund sender
          await this.refundEscrow(escrow);
          await escrow.expire();
          
          await EscrowNotification.createEscrowNotification(
            escrow.id,
            escrow.senderId,
            'escrow_expired'
          );
          await EscrowNotification.createEscrowNotification(
            escrow.id,
            escrow.recipientId,
            'escrow_expired'
          );
          
          results.push({ escrowId: escrow.id, action: 'expired' });
        }
      } catch (error) {
        results.push({ escrowId: escrow.id, action: 'error', error: error.message });
      }
    }

    return results;
  }

  /**
   * Calculate fees based on currency and fee payer
   */
  async calculateFees(amount, currency, feePayer) {
    // Get fee percentage from currency settings
    const feePercentage = currency.transferFee || 0;
    const totalFee = formatPrecision((amount * feePercentage) / 100);

    let senderFee = 0;
    let recipientFee = 0;
    let totalAmount = amount;

    switch (feePayer) {
      case 'sender':
        senderFee = totalFee;
        totalAmount = formatPrecision(amount + totalFee);
        break;
      case 'recipient':
        recipientFee = totalFee;
        totalAmount = amount; // Sender pays only the amount
        break;
      case 'split':
        senderFee = formatPrecision(totalFee / 2);
        recipientFee = formatPrecision(totalFee / 2);
        totalAmount = formatPrecision(amount + senderFee);
        break;
    }

    return {
      totalFee,
      senderFee,
      recipientFee,
      totalAmount
    };
  }

  /**
   * Create milestones for an escrow
   */
  async createMilestones(escrowId, milestonesData) {
    const milestones = [];
    let totalPercentage = 0;

    for (let i = 0; i < milestonesData.length; i++) {
      const milestoneData = milestonesData[i];
      totalPercentage += milestoneData.percentage;

      const milestone = await EscrowMilestone.create({
        escrowId,
        title: milestoneData.title,
        description: milestoneData.description,
        orderIndex: i + 1,
        amount: formatPrecision((milestoneData.percentage / 100) * (await Escrow.find(escrowId)).amount),
        percentage: milestoneData.percentage,
        deliverables: JSON.stringify(milestoneData.deliverables || []),
        dueDate: milestoneData.dueDate ? new Date(milestoneData.dueDate) : null
      });

      milestones.push(milestone);
    }

    // Validate total percentage
    if (Math.abs(totalPercentage - 100) > 0.01) {
      throw new Error('Milestone percentages must total 100%');
    }

    return milestones;
  }

  /**
   * Schedule reminder notifications
   */
  async scheduleNotifications(escrow) {
    const deadline = DateTime.fromJSDate(escrow.deadline);
    
    // Schedule 24h reminder
    const reminder24h = deadline.minus({ hours: 24 });
    if (reminder24h > DateTime.now()) {
      await EscrowNotification.createReminderNotification(
        escrow.id,
        escrow.senderId,
        'deadline_reminder_24h',
        reminder24h.toJSDate()
      );
      await EscrowNotification.createReminderNotification(
        escrow.id,
        escrow.recipientId,
        'deadline_reminder_24h',
        reminder24h.toJSDate()
      );
    }

    // Schedule 6h reminder
    const reminder6h = deadline.minus({ hours: 6 });
    if (reminder6h > DateTime.now()) {
      await EscrowNotification.createReminderNotification(
        escrow.id,
        escrow.senderId,
        'deadline_reminder_6h',
        reminder6h.toJSDate()
      );
      await EscrowNotification.createReminderNotification(
        escrow.id,
        escrow.recipientId,
        'deadline_reminder_6h',
        reminder6h.toJSDate()
      );
    }

    // Schedule 1h reminder
    const reminder1h = deadline.minus({ hours: 1 });
    if (reminder1h > DateTime.now()) {
      await EscrowNotification.createReminderNotification(
        escrow.id,
        escrow.senderId,
        'deadline_reminder_1h',
        reminder1h.toJSDate()
      );
      await EscrowNotification.createReminderNotification(
        escrow.id,
        escrow.recipientId,
        'deadline_reminder_1h',
        reminder1h.toJSDate()
      );
    }
  }

  /**
   * Refund escrow to sender
   */
  async refundEscrow(escrow) {
    const currency = await Currency.query().where('code', escrow.currencyCode).first();
    const senderWallet = await Wallet.query()
      .where('user_id', escrow.senderId)
      .where('currency_id', currency.id)
      .where('default', true)
      .first();

    if (!senderWallet) {
      throw new Error('Sender wallet not found for refund');
    }

    // Refund the total amount (including fees)
    senderWallet.balance = formatPrecision(senderWallet.balance + escrow.total);
    await senderWallet.save();

    // Create refund transaction
    const transaction = await Transaction.create({
      type: 'escrow_refund',
      from: { label: 'Escrow System', currency: escrow.currencyCode },
      to: {
        image: (await escrow.related('sender').query().preload('customer').first()).customer?.profileImage ?? '',
        label: (await escrow.related('sender').query().preload('customer').first()).customer?.name ?? '',
        email: escrow.sender?.email ?? '',
        currency: escrow.currencyCode
      },
      amount: escrow.total,
      fee: 0,
      total: escrow.total,
      status: 'completed',
      escrowId: escrow.id,
      userId: escrow.senderId,
      metaData: {
        currency: escrow.currencyCode,
        trxAction: 'escrow_refund',
        escrowId: escrow.escrowId
      }
    });

    return transaction;
  }
}

export default new EscrowService();

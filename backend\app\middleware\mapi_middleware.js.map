{"version": 3, "file": "mapi_middleware.js", "sourceRoot": "", "sources": ["../../../app/middleware/mapi_middleware.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,kBAAkB,CAAC;AAIxC,MAAM,CAAC,OAAO,OAAO,cAAc;IACjC,KAAK,CAAC,MAAM,CAAC,GAAgB,EAAE,IAAY;QAIzC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;YACzC,OAAO,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnC,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,+EAA+E;aACzF,CAAC,CAAC;QACL,CAAC;QACD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC;QACnE,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,CAAC;QACxF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnC,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,2BAA2B;aACrC,CAAC,CAAC;QACL,CAAC;QACD,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,SAAS,EAAE,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAIlF,MAAM,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;QAC5B,OAAO,MAAM,CAAC;IAChB,CAAC;CACF"}
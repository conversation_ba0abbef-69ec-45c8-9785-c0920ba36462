import { 
  IContact,
  ContactFilters,
  ContactListResponse,
  ContactSearchResponse,
  ContactDetailResponse,
  CreateContactData,
  ContactStatistics,
  ContactValidationResult
} from '@/types/contact';

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';

class ContactApiClient {
  private hasEnhancedEndpoints: boolean | null = null;
  private endpointCache: Map<string, boolean> = new Map();

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('Contact API request error:', error);
      throw error;
    }
  }

  private async checkEndpointExists(endpoint: string): Promise<boolean> {
    if (this.endpointCache.has(endpoint)) {
      return this.endpointCache.get(endpoint)!;
    }

    try {
      // Use HEAD request to check if endpoint exists
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'HEAD',
      });
      const exists = response.status !== 404;
      this.endpointCache.set(endpoint, exists);
      return exists;
    } catch {
      this.endpointCache.set(endpoint, false);
      return false;
    }
  }

  private async checkEnhancedEndpoints(): Promise<boolean> {
    if (this.hasEnhancedEndpoints !== null) {
      return this.hasEnhancedEndpoints;
    }

    try {
      // Test if enhanced search endpoint exists
      const hasSearch = await this.checkEndpointExists('/contacts/search');
      this.hasEnhancedEndpoints = hasSearch;
    } catch {
      this.hasEnhancedEndpoints = false;
    }

    return this.hasEnhancedEndpoints;
  }

  // Get contacts with filtering and pagination
  async getContacts(filters?: ContactFilters): Promise<ContactListResponse> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(key, String(v)));
          } else {
            params.append(key, String(value));
          }
        }
      });
    }
    
    return this.request<ContactListResponse>(
      `/contacts?${params.toString()}`
    );
  }

  // Search contacts with enhanced results and fallback
  async searchContacts(filters?: ContactFilters): Promise<ContactSearchResponse> {
    const hasEnhanced = await this.checkEnhancedEndpoints();

    if (hasEnhanced) {
      try {
        return await this.searchContactsEnhanced(filters);
      } catch (error) {
        console.warn('Enhanced search endpoint failed, falling back to legacy:', error);
        return await this.searchContactsLegacy(filters);
      }
    } else {
      return await this.searchContactsLegacy(filters);
    }
  }

  private async searchContactsEnhanced(filters?: ContactFilters): Promise<ContactSearchResponse> {
    const params = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(key, String(v)));
          } else {
            params.append(key, String(value));
          }
        }
      });
    }

    return this.request<ContactSearchResponse>(
      `/contacts/search?${params.toString()}`
    );
  }

  private async searchContactsLegacy(filters?: ContactFilters): Promise<ContactSearchResponse> {
    // Convert enhanced filters to legacy format
    const legacyParams = new URLSearchParams();
    if (filters?.search) {
      legacyParams.append('search', filters.search);
    }
    if (filters?.limit) {
      legacyParams.append('limit', filters.limit.toString());
    }

    const response = await this.request<any>(`/contacts?${legacyParams.toString()}`);

    // Convert legacy response to enhanced format
    const legacyContacts = response.data || response || [];
    const convertedContacts = legacyContacts.map((contact: any) => {
      // Handle different legacy formats
      if (contact.contact?.customer) {
        return {
          id: contact.contact.customer.id || contact.id,
          name: contact.contact.customer.name || contact.contact.customer.email,
          email: contact.contact.email || contact.contact.customer.email,
          avatar: contact.contact.customer.profileImage,
          status: 'active',
          isVerified: false,
          verificationLevel: 'basic',
          lastTransactionAt: contact.updatedAt ? new Date(contact.updatedAt) : undefined,
          totalTransactions: 0,
          totalVolume: 0,
          riskScore: 0,
          tags: [],
          capabilities: {
            canReceiveEscrow: true,
            canReceiveRecurringTransfer: true,
            canReceiveFundraisingContribution: true,
          },
          relationshipType: 'contact',
          createdAt: contact.createdAt ? new Date(contact.createdAt) : new Date(),
          updatedAt: contact.updatedAt ? new Date(contact.updatedAt) : new Date(),
        };
      }

      // Handle simple contact format
      return {
        id: contact.id,
        name: contact.name || contact.email,
        email: contact.email,
        avatar: contact.avatar,
        status: contact.status || 'active',
        isVerified: contact.isVerified || false,
        verificationLevel: contact.verificationLevel || 'basic',
        lastTransactionAt: contact.lastTransactionAt ? new Date(contact.lastTransactionAt) : undefined,
        totalTransactions: contact.totalTransactions || 0,
        totalVolume: contact.totalVolume || 0,
        riskScore: contact.riskScore || 0,
        tags: contact.tags || [],
        capabilities: contact.capabilities || {
          canReceiveEscrow: true,
          canReceiveRecurringTransfer: true,
          canReceiveFundraisingContribution: true,
        },
        relationshipType: contact.relationshipType || 'contact',
        createdAt: contact.createdAt ? new Date(contact.createdAt) : new Date(),
        updatedAt: contact.updatedAt ? new Date(contact.updatedAt) : new Date(),
      };
    });

    return {
      success: true,
      data: {
        contacts: convertedContacts,
        suggestions: [],
        recentContacts: [],
        frequentContacts: [],
      },
    };
  }

  // Get contact by ID
  async getContact(id: number): Promise<ContactDetailResponse> {
    return this.request<ContactDetailResponse>(`/contacts/${id}`);
  }

  // Create new contact
  async createContact(data: CreateContactData): Promise<ContactDetailResponse> {
    return this.request<ContactDetailResponse>('/contacts', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Update contact
  async updateContact(id: number, data: Partial<CreateContactData>): Promise<ContactDetailResponse> {
    return this.request<ContactDetailResponse>(`/contacts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Delete contact
  async deleteContact(id: number): Promise<{ success: boolean; message: string }> {
    return this.request(`/contacts/${id}`, {
      method: 'DELETE',
    });
  }

  // Block/unblock contact
  async blockContact(id: number, reason?: string): Promise<ContactDetailResponse> {
    return this.request<ContactDetailResponse>(`/contacts/${id}/block`, {
      method: 'POST',
      body: JSON.stringify({ reason }),
    });
  }

  async unblockContact(id: number): Promise<ContactDetailResponse> {
    return this.request<ContactDetailResponse>(`/contacts/${id}/unblock`, {
      method: 'POST',
    });
  }

  // Verify contact
  async verifyContact(id: number, level: 'basic' | 'enhanced' | 'premium'): Promise<ContactDetailResponse> {
    return this.request<ContactDetailResponse>(`/contacts/${id}/verify`, {
      method: 'POST',
      body: JSON.stringify({ level }),
    });
  }

  // Update contact tags
  async updateContactTags(id: number, tags: string[]): Promise<ContactDetailResponse> {
    return this.request<ContactDetailResponse>(`/contacts/${id}/tags`, {
      method: 'PUT',
      body: JSON.stringify({ tags }),
    });
  }

  // Add contact note
  async addContactNote(id: number, note: string): Promise<ContactDetailResponse> {
    return this.request<ContactDetailResponse>(`/contacts/${id}/notes`, {
      method: 'POST',
      body: JSON.stringify({ note }),
    });
  }

  // Get contact statistics
  async getContactStatistics(): Promise<{ success: boolean; data: ContactStatistics }> {
    return this.request('/contacts/statistics');
  }

  // Validate contact data
  async validateContact(data: CreateContactData): Promise<{ success: boolean; data: ContactValidationResult }> {
    return this.request('/contacts/validate', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Get recent contacts
  async getRecentContacts(limit: number = 10): Promise<ContactListResponse> {
    return this.request<ContactListResponse>(`/contacts/recent?limit=${limit}`);
  }

  // Get frequent contacts
  async getFrequentContacts(limit: number = 10): Promise<ContactListResponse> {
    return this.request<ContactListResponse>(`/contacts/frequent?limit=${limit}`);
  }

  // Get contact suggestions
  async getContactSuggestions(context?: string): Promise<ContactListResponse> {
    const params = context ? `?context=${context}` : '';
    return this.request<ContactListResponse>(`/contacts/suggestions${params}`);
  }

  // Import contacts
  async importContacts(contacts: CreateContactData[]): Promise<{
    success: boolean;
    data: {
      imported: number;
      failed: number;
      errors: Array<{ index: number; error: string }>;
    };
  }> {
    return this.request('/contacts/import', {
      method: 'POST',
      body: JSON.stringify({ contacts }),
    });
  }

  // Export contacts
  async exportContacts(filters?: ContactFilters): Promise<{
    success: boolean;
    data: {
      downloadUrl: string;
      expiresAt: string;
    };
  }> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, String(value));
        }
      });
    }
    
    return this.request(`/contacts/export?${params.toString()}`, {
      method: 'POST',
    });
  }

  // Bulk operations
  async bulkUpdateContacts(
    contactIds: number[], 
    updates: {
      status?: 'active' | 'blocked' | 'pending';
      tags?: string[];
      verificationLevel?: 'basic' | 'enhanced' | 'premium';
    }
  ): Promise<{
    success: boolean;
    data: {
      updated: number;
      failed: number;
      errors: Array<{ contactId: number; error: string }>;
    };
  }> {
    return this.request('/contacts/bulk-update', {
      method: 'POST',
      body: JSON.stringify({ contactIds, updates }),
    });
  }

  async bulkDeleteContacts(contactIds: number[]): Promise<{
    success: boolean;
    data: {
      deleted: number;
      failed: number;
      errors: Array<{ contactId: number; error: string }>;
    };
  }> {
    return this.request('/contacts/bulk-delete', {
      method: 'POST',
      body: JSON.stringify({ contactIds }),
    });
  }

  // Contact relationship management
  async getContactRelationships(contactId: number): Promise<{
    success: boolean;
    data: {
      escrows: number;
      recurringTransfers: number;
      fundraisingContributions: number;
      totalVolume: number;
      lastInteraction: string;
    };
  }> {
    return this.request(`/contacts/${contactId}/relationships`);
  }

  // Contact risk assessment
  async updateContactRiskScore(
    contactId: number, 
    riskScore: number, 
    reason: string
  ): Promise<ContactDetailResponse> {
    return this.request<ContactDetailResponse>(`/contacts/${contactId}/risk-score`, {
      method: 'PUT',
      body: JSON.stringify({ riskScore, reason }),
    });
  }

  // Contact capabilities management
  async updateContactCapabilities(
    contactId: number,
    capabilities: {
      canReceiveEscrow?: boolean;
      canReceiveRecurringTransfer?: boolean;
      canReceiveFundraisingContribution?: boolean;
    }
  ): Promise<ContactDetailResponse> {
    return this.request<ContactDetailResponse>(`/contacts/${contactId}/capabilities`, {
      method: 'PUT',
      body: JSON.stringify({ capabilities }),
    });
  }
}

// Create singleton instance
export const contactApi = new ContactApiClient();

// Error handling utilities
export function isContactApiError(error: any): boolean {
  return error && typeof error.success === 'boolean' && error.success === false;
}

export function getContactErrorMessage(error: any): string {
  if (isContactApiError(error)) {
    return error.message;
  }
  if (error instanceof Error) {
    return error.message;
  }
  return 'An unexpected error occurred';
}

// Response utilities
export function extractContactData<T>(response: { success: boolean; data?: T }): T | null {
  return response.success ? response.data || null : null;
}

// Hook utilities for React components
export const useContactApi = () => {
  return {
    api: contactApi,
    isContactApiError,
    getContactErrorMessage,
    extractContactData,
  };
};

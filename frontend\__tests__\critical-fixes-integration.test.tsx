import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { jest } from '@jest/globals';
import SelectRecipientCombo from '@/components/common/form/SelectRecipientCombo';
import { ErrorBoundary, ContactSelectionErrorBoundary } from '@/components/common/ErrorBoundary';
import { useUnifiedLoading, useContactLoadingStates } from '@/hooks/useUnifiedLoading';
import { useWebSocket } from '@/hooks/useWebSocket';
import { normalizeContact, convertLegacyContact } from '@/types/contact-unified';

// Mock data for testing
const mockEnhancedContact = {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  avatar: 'https://example.com/avatar.jpg',
  status: 'active',
  isVerified: true,
  verificationLevel: 'enhanced',
  lastTransactionAt: new Date(),
  totalTransactions: 5,
  totalVolume: 1000,
  riskScore: 25,
  tags: ['trusted', 'frequent'],
  capabilities: {
    canReceiveEscrow: true,
    canReceiveRecurringTransfer: true,
    canReceiveFundraisingContribution: true,
  },
  relationshipType: 'frequent',
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockLegacyContact = {
  id: 1,
  contactId: 1,
  userId: 2,
  contact: {
    customer: {
      id: 1,
      name: 'Jane Smith',
      email: '<EMAIL>',
      profileImage: 'https://example.com/jane.jpg',
    },
    email: '<EMAIL>',
  },
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Mock APIs with progressive enhancement
const mockContactApi = {
  searchContacts: jest.fn(),
  getContacts: jest.fn(),
};

// Mock WebSocket with circuit breaker
const mockWebSocket = {
  subscribe: jest.fn().mockReturnValue(() => {}),
  unsubscribe: jest.fn(),
  isConnected: jest.fn().mockReturnValue(true),
  getConnectionState: jest.fn().mockReturnValue('connected'),
  getCircuitBreakerStatus: jest.fn().mockReturnValue({
    state: 'closed',
    failureCount: 0,
    lastFailureTime: 0,
    timeUntilHalfOpen: 0,
  }),
  getReconnectionStatus: jest.fn().mockReturnValue({
    attempts: 0,
    maxAttempts: 5,
    isReconnecting: false,
    nextAttemptIn: 0,
  }),
  disconnect: jest.fn(),
};

// Mock hooks
jest.mock('@/lib/contact-api', () => ({
  contactApi: mockContactApi,
}));

jest.mock('@/hooks/useWebSocket', () => ({
  useWebSocket: () => mockWebSocket,
}));

jest.mock('@/hooks/useSWR', () => ({
  useSWR: (url: string) => {
    if (url.includes('/contacts')) {
      return {
        data: { data: [mockLegacyContact] },
        isLoading: false,
        error: null,
      };
    }
    return { data: null, isLoading: false, error: null };
  },
}));

describe('Critical Fixes Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.error = jest.fn(); // Suppress error logs in tests
    console.warn = jest.fn(); // Suppress warning logs in tests
  });

  describe('Fix #1: Type Definition Conflicts Resolution', () => {
    it('should handle both enhanced and legacy contact formats', () => {
      // Test enhanced contact normalization
      const normalizedEnhanced = normalizeContact(mockEnhancedContact);
      expect(normalizedEnhanced.id).toBe(1);
      expect(normalizedEnhanced.name).toBe('John Doe');
      expect(normalizedEnhanced.status).toBe('active');

      // Test legacy contact conversion
      const convertedLegacy = convertLegacyContact(mockLegacyContact as any);
      expect(convertedLegacy.id).toBe(1);
      expect(convertedLegacy.name).toBe('Jane Smith');
      expect(convertedLegacy.status).toBe('active');
    });

    it('should work with unified contact types in SelectRecipientCombo', async () => {
      const mockOnChange = jest.fn();
      const mockSetUser = jest.fn();

      render(
        <SelectRecipientCombo
          user={mockEnhancedContact}
          onChange={mockOnChange}
          setUser={mockSetUser}
        />
      );

      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
  });

  describe('Fix #2: Unified Loading State Management', () => {
    it('should coordinate multiple loading states', () => {
      const TestComponent = () => {
        const loadingState = useContactLoadingStates({
          enhancedApi: true,
          legacyApi: false,
          websocketConnection: false,
          search: true,
        });

        return (
          <div>
            <div data-testid="loading">{loadingState.isLoading.toString()}</div>
            <div data-testid="message">{loadingState.contactLoadingMessage}</div>
            <div data-testid="searching">{loadingState.isSearching.toString()}</div>
          </div>
        );
      };

      render(<TestComponent />);

      expect(screen.getByTestId('loading')).toHaveTextContent('true');
      expect(screen.getByTestId('searching')).toHaveTextContent('true');
      expect(screen.getByTestId('message')).toHaveTextContent('Searching contacts...');
    });

    it('should show appropriate loading messages for different states', () => {
      const TestComponent = ({ states }: { states: any }) => {
        const loadingState = useContactLoadingStates(states);
        return <div data-testid="message">{loadingState.contactLoadingMessage}</div>;
      };

      const { rerender } = render(<TestComponent states={{ enhancedApi: true }} />);
      expect(screen.getByTestId('message')).toHaveTextContent('Loading enhanced contacts...');

      rerender(<TestComponent states={{ legacyApi: true }} />);
      expect(screen.getByTestId('message')).toHaveTextContent('Loading contacts...');

      rerender(<TestComponent states={{ search: true }} />);
      expect(screen.getByTestId('message')).toHaveTextContent('Searching contacts...');
    });
  });

  describe('Fix #3: Error Boundary Implementation', () => {
    it('should catch and display component errors', () => {
      const ThrowError = () => {
        throw new Error('Test error');
      };

      render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.getByText('Test error')).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });

    it('should provide contact-specific error boundary', () => {
      const ThrowError = () => {
        throw new Error('Contact selection error');
      };

      render(
        <ContactSelectionErrorBoundary>
          <ThrowError />
        </ContactSelectionErrorBoundary>
      );

      expect(screen.getByText('Contact selection failed')).toBeInTheDocument();
      expect(screen.getByText('Refresh')).toBeInTheDocument();
    });

    it('should allow retry after error', async () => {
      let shouldThrow = true;
      const ConditionalError = () => {
        if (shouldThrow) {
          throw new Error('Conditional error');
        }
        return <div>Success</div>;
      };

      render(
        <ErrorBoundary>
          <ConditionalError />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Click retry
      shouldThrow = false;
      fireEvent.click(screen.getByText('Try Again'));

      await waitFor(() => {
        expect(screen.getByText('Success')).toBeInTheDocument();
      });
    });
  });

  describe('Fix #4: WebSocket Memory Leak Prevention', () => {
    it('should clean up subscriptions on unmount', () => {
      const unsubscribeFn = jest.fn();
      mockWebSocket.subscribe.mockReturnValue(unsubscribeFn);

      const { unmount } = render(
        <SelectRecipientCombo
          user={null}
          onChange={jest.fn()}
          setUser={jest.fn()}
          enableRealTimeUpdates={true}
        />
      );

      expect(mockWebSocket.subscribe).toHaveBeenCalled();

      unmount();

      // Should call unsubscribe functions
      expect(unsubscribeFn).toHaveBeenCalled();
    });

    it('should handle subscription errors gracefully', () => {
      const errorSubscribe = jest.fn().mockImplementation(() => {
        throw new Error('Subscription error');
      });
      mockWebSocket.subscribe.mockImplementation(errorSubscribe);

      // Should not crash the component
      render(
        <SelectRecipientCombo
          user={null}
          onChange={jest.fn()}
          setUser={jest.fn()}
          enableRealTimeUpdates={true}
        />
      );

      expect(screen.getByText('Enter recipient account')).toBeInTheDocument();
    });
  });

  describe('Fix #5: Progressive API Enhancement', () => {
    it('should fallback to legacy API when enhanced API fails', async () => {
      // Mock enhanced API failure
      mockContactApi.searchContacts.mockRejectedValueOnce(new Error('Enhanced API unavailable'));

      render(
        <SelectRecipientCombo
          user={null}
          onChange={jest.fn()}
          setUser={jest.fn()}
        />
      );

      // Should still show legacy contacts
      fireEvent.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });
    });

    it('should use enhanced API when available', async () => {
      mockContactApi.searchContacts.mockResolvedValueOnce({
        success: true,
        data: {
          contacts: [mockEnhancedContact],
          recentContacts: [],
          frequentContacts: [],
          suggestions: [],
        },
      });

      render(
        <SelectRecipientCombo
          user={null}
          onChange={jest.fn()}
          setUser={jest.fn()}
          context="escrow"
        />
      );

      await waitFor(() => {
        expect(mockContactApi.searchContacts).toHaveBeenCalledWith(
          expect.objectContaining({
            canReceiveEscrow: true,
          })
        );
      });
    });
  });

  describe('Fix #6: Enhanced WebSocket Reconnection', () => {
    it('should provide circuit breaker status', () => {
      render(
        <SelectRecipientCombo
          user={null}
          onChange={jest.fn()}
          setUser={jest.fn()}
          enableRealTimeUpdates={true}
        />
      );

      expect(mockWebSocket.getCircuitBreakerStatus).toHaveBeenCalled();
      expect(mockWebSocket.getReconnectionStatus).toHaveBeenCalled();
    });

    it('should handle circuit breaker open state', () => {
      mockWebSocket.getCircuitBreakerStatus.mockReturnValue({
        state: 'open',
        failureCount: 5,
        lastFailureTime: Date.now(),
        timeUntilHalfOpen: 30000,
      });

      render(
        <SelectRecipientCombo
          user={null}
          onChange={jest.fn()}
          setUser={jest.fn()}
          enableRealTimeUpdates={true}
        />
      );

      // Component should still render despite circuit breaker being open
      expect(screen.getByText('Enter recipient account')).toBeInTheDocument();
    });
  });

  describe('Integration: All Fixes Working Together', () => {
    it('should handle complete workflow with all fixes active', async () => {
      const mockOnChange = jest.fn();
      const mockSetUser = jest.fn();

      // Mock successful enhanced API
      mockContactApi.searchContacts.mockResolvedValue({
        success: true,
        data: {
          contacts: [mockEnhancedContact],
          recentContacts: [mockEnhancedContact],
          frequentContacts: [],
          suggestions: [],
        },
      });

      render(
        <ContactSelectionErrorBoundary>
          <SelectRecipientCombo
            user={null}
            onChange={mockOnChange}
            setUser={mockSetUser}
            context="escrow"
            enableRealTimeUpdates={true}
            showVerificationBadge={true}
            showContactStatus={true}
          />
        </ContactSelectionErrorBoundary>
      );

      // Open dropdown
      fireEvent.click(screen.getByRole('button'));

      // Should load enhanced contacts
      await waitFor(() => {
        expect(mockContactApi.searchContacts).toHaveBeenCalledWith(
          expect.objectContaining({
            canReceiveEscrow: true,
          })
        );
      });

      // Should show enhanced contact with verification
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Select contact
      fireEvent.click(screen.getByText('John Doe'));

      // Should call handlers with normalized contact
      expect(mockSetUser).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>',
          status: 'active',
          verificationLevel: 'enhanced',
        })
      );
      expect(mockOnChange).toHaveBeenCalledWith('1');
    });

    it('should gracefully degrade when multiple systems fail', async () => {
      // Mock all enhanced features failing
      mockContactApi.searchContacts.mockRejectedValue(new Error('API down'));
      mockWebSocket.getConnectionState.mockReturnValue('disconnected');
      mockWebSocket.getCircuitBreakerStatus.mockReturnValue({
        state: 'open',
        failureCount: 10,
        lastFailureTime: Date.now(),
        timeUntilHalfOpen: 60000,
      });

      render(
        <ContactSelectionErrorBoundary>
          <SelectRecipientCombo
            user={null}
            onChange={jest.fn()}
            setUser={jest.fn()}
            enableRealTimeUpdates={true}
          />
        </ContactSelectionErrorBoundary>
      );

      // Should still work with legacy data
      fireEvent.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });
    });
  });
});

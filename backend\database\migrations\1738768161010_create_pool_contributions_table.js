import { BaseSchema } from '@adonisjs/lucid/schema';

export default class extends BaseSchema {
  tableName = 'pool_contributions';

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id');
      table.string('contribution_id').notNullable().unique();
      
      // Relationships
      table
        .integer('pool_id')
        .unsigned()
        .references('id')
        .inTable('fundraising_pools')
        .onDelete('CASCADE')
        .notNullable();
      
      table
        .integer('contributor_id')
        .unsigned()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .nullable(); // Nullable for anonymous contributions
      
      // Contribution details
      table.double('amount').notNullable();
      table.double('fee').defaultTo(0);
      table.double('net_amount').notNullable(); // Amount after fees
      table.string('currency_code').notNullable();
      
      // Contributor information (for anonymous contributions)
      table.string('contributor_name').nullable();
      table.string('contributor_email').nullable();
      table.boolean('is_anonymous').defaultTo(false);
      table.boolean('show_amount').defaultTo(true);
      
      // Status and processing
      table.enum('status', [
        'pending',
        'processing',
        'completed',
        'failed',
        'refunded'
      ]).defaultTo('pending');
      
      // Payment information
      table.string('payment_method').nullable();
      table.string('transaction_reference').nullable();
      table
        .integer('transaction_id')
        .unsigned()
        .references('id')
        .inTable('transactions')
        .onDelete('SET NULL')
        .nullable();
      
      // Message and social features
      table.text('message').nullable();
      table.boolean('allow_contact').defaultTo(false);
      table.json('social_links').nullable();
      
      // Refund information
      table.timestamp('refunded_at').nullable();
      table.text('refund_reason').nullable();
      table.double('refund_amount').nullable();
      
      // Metadata
      table.json('metadata').nullable();
      table.string('ip_address').nullable();
      table.string('user_agent').nullable();
      
      table.timestamp('created_at');
      table.timestamp('updated_at');
      
      // Indexes
      table.index(['pool_id', 'status']);
      table.index(['contributor_id', 'status']);
      table.index(['status']);
      table.index(['created_at']);
      table.index(['transaction_id']);
    });
  }

  async down() {
    this.schema.dropTable(this.tableName);
  }
}

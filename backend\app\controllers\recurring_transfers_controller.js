import errorHandler from '#exceptions/error_handler';
import RecurringTransfer from '#models/recurring_transfer';
import recurringTransferService from '#services/recurring_transfer_service';
import RecurringTransferValidator from '#utils/recurring_transfer_validator';
import { 
  createRecurringTransferSchema, 
  updateRecurringTransferSchema 
} from '#validators/recurring_transfer';
import exportService from '#services/export_service';

export default class RecurringTransfersController {
  /**
   * Get user's recurring transfers
   */
  async index(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const { page, limit, status, type, ...filters } = request.qs();
      
      let query = RecurringTransfer.query()
        .where((builder) => {
          builder.where('sender_id', auth.user.id).orWhere('recipient_id', auth.user.id);
        })
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .orderBy('created_at', 'desc');

      // Apply filters
      if (status) {
        query = query.where('status', status);
      }

      if (type === 'sent') {
        query = query.where('sender_id', auth.user.id);
      } else if (type === 'received') {
        query = query.where('recipient_id', auth.user.id);
      }

      // Apply date filters
      if (filters.startDate) {
        query = query.where('created_at', '>=', filters.startDate);
      }
      
      if (filters.endDate) {
        query = query.where('created_at', '<=', filters.endDate);
      }

      if (filters.frequency) {
        query = query.where('frequency', filters.frequency);
      }

      const data = page && limit ? await query.paginate(page, limit) : await query.exec();
      
      return response.json(data);
    } catch (error) {
      errorHandler(error, ctx, 'Recurring transfers index error');
    }
  }

  /**
   * Get recurring transfer by ID
   */
  async show(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const transferId = request.param('id');
      
      const transfer = await RecurringTransfer.query()
        .where('id', transferId)
        .where((builder) => {
          builder.where('sender_id', auth.user.id).orWhere('recipient_id', auth.user.id);
        })
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .preload('transactions', (query) => query.orderBy('created_at', 'desc').limit(10))
        .first();

      if (!transfer) {
        return response.notFound({ success: false, message: 'Recurring transfer not found' });
      }

      return response.json(transfer);
    } catch (error) {
      errorHandler(error, ctx, 'Recurring transfer show error');
    }
  }

  /**
   * Create new recurring transfer
   */
  async store(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const data = await request.validateUsing(createRecurringTransferSchema);
      
      // Add sender ID from authenticated user
      data.senderId = auth.user.id;
      
      // Validate creation data
      RecurringTransferValidator.validateCreationData(data);
      
      // Check user limits
      const existingCount = await RecurringTransfer.query()
        .where('sender_id', auth.user.id)
        .whereIn('status', ['active', 'paused'])
        .count('* as total');
      
      RecurringTransferValidator.validateUserLimits(
        auth.user.id, 
        existingCount[0]?.total || 0, 
        data
      );
      
      const transfer = await recurringTransferService.createRecurringTransfer(data);
      
      return response.created({
        success: true,
        message: 'Recurring transfer created successfully',
        data: transfer
      });
    } catch (error) {
      errorHandler(error, ctx, 'Recurring transfer creation error');
    }
  }

  /**
   * Update recurring transfer
   */
  async update(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const transferId = request.param('id');
      const updateData = await request.validateUsing(updateRecurringTransferSchema);
      
      const transfer = await RecurringTransfer.query()
        .where('id', transferId)
        .where('sender_id', auth.user.id)
        .first();

      if (!transfer) {
        return response.notFound({ success: false, message: 'Recurring transfer not found' });
      }

      // Validate schedule modification
      RecurringTransferValidator.validateScheduleModification(transfer, updateData);
      
      // Update transfer
      transfer.merge(updateData);
      await transfer.save();
      
      return response.json({
        success: true,
        message: 'Recurring transfer updated successfully',
        data: transfer
      });
    } catch (error) {
      errorHandler(error, ctx, 'Recurring transfer update error');
    }
  }

  /**
   * Pause recurring transfer
   */
  async pause(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const transferId = request.param('id');
      
      const transfer = await recurringTransferService.pauseRecurringTransfer(
        transferId, 
        auth.user.id
      );
      
      return response.json({
        success: true,
        message: 'Recurring transfer paused successfully',
        data: transfer
      });
    } catch (error) {
      errorHandler(error, ctx, 'Recurring transfer pause error');
    }
  }

  /**
   * Resume recurring transfer
   */
  async resume(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const transferId = request.param('id');
      
      const transfer = await recurringTransferService.resumeRecurringTransfer(
        transferId, 
        auth.user.id
      );
      
      return response.json({
        success: true,
        message: 'Recurring transfer resumed successfully',
        data: transfer
      });
    } catch (error) {
      errorHandler(error, ctx, 'Recurring transfer resume error');
    }
  }

  /**
   * Cancel recurring transfer
   */
  async cancel(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const transferId = request.param('id');
      
      const transfer = await recurringTransferService.cancelRecurringTransfer(
        transferId, 
        auth.user.id
      );
      
      return response.json({
        success: true,
        message: 'Recurring transfer cancelled successfully',
        data: transfer
      });
    } catch (error) {
      errorHandler(error, ctx, 'Recurring transfer cancellation error');
    }
  }

  /**
   * Get transfer history
   */
  async history(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const transferId = request.param('id');
      const { page = 1, limit = 20, status } = request.qs();
      
      const result = await recurringTransferService.getTransferHistory(
        transferId, 
        auth.user.id, 
        { page, limit, status }
      );
      
      return response.json(result);
    } catch (error) {
      errorHandler(error, ctx, 'Recurring transfer history error');
    }
  }

  /**
   * Get upcoming transfers
   */
  async upcoming(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const { daysAhead = 7 } = request.qs();
      
      const upcomingTransfers = await recurringTransferService.getUpcomingTransfers(
        auth.user.id, 
        parseInt(daysAhead)
      );
      
      return response.json(upcomingTransfers);
    } catch (error) {
      errorHandler(error, ctx, 'Upcoming transfers error');
    }
  }

  /**
   * Get recurring transfer statistics
   */
  async statistics(ctx) {
    const { auth, response } = ctx;
    
    try {
      const userId = auth.user.id;
      
      const stats = await RecurringTransfer.query()
        .where((builder) => {
          builder.where('sender_id', userId).orWhere('recipient_id', userId);
        })
        .select('status', 'frequency')
        .groupBy('status', 'frequency')
        .count('* as count');

      const sentStats = await RecurringTransfer.query()
        .where('sender_id', userId)
        .select('status')
        .groupBy('status')
        .count('* as count');

      const receivedStats = await RecurringTransfer.query()
        .where('recipient_id', userId)
        .select('status')
        .groupBy('status')
        .count('* as count');

      const totalExecutions = await RecurringTransfer.query()
        .where((builder) => {
          builder.where('sender_id', userId).orWhere('recipient_id', userId);
        })
        .sum('executed_count as total');

      return response.json({
        overall: stats,
        sent: sentStats,
        received: receivedStats,
        totalExecutions: totalExecutions[0]?.total || 0
      });
    } catch (error) {
      errorHandler(error, ctx, 'Recurring transfer statistics error');
    }
  }

  /**
   * Preview recurring transfer schedule
   */
  async previewSchedule(ctx) {
    const { request, response } = ctx;
    
    try {
      const { 
        startDate, 
        frequency, 
        intervalValue = 1, 
        maxOccurrences, 
        endDate,
        previewCount = 10 
      } = request.qs();
      
      // Validate schedule parameters
      RecurringTransferValidator.validateSchedule(
        frequency, 
        parseInt(intervalValue), 
        startDate, 
        endDate, 
        maxOccurrences ? parseInt(maxOccurrences) : null
      );
      
      const preview = RecurringTransferValidator.generateSchedulePreview(
        startDate,
        frequency,
        parseInt(intervalValue),
        maxOccurrences ? parseInt(maxOccurrences) : null,
        endDate,
        parseInt(previewCount)
      );
      
      return response.json(preview);
    } catch (error) {
      errorHandler(error, ctx, 'Schedule preview error');
    }
  }

  /**
   * Export recurring transfers to CSV
   */
  async exportCsv(ctx) {
    try {
      await exportService.exportData(ctx, 'recurring_transfers');
    } catch (error) {
      errorHandler(error, ctx, 'Recurring transfers export error');
    }
  }

  /**
   * Get recurring transfer by recurring_id
   */
  async getByRecurringId(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const recurringId = request.param('recurringId');
      
      const transfer = await RecurringTransfer.query()
        .where('recurring_id', recurringId)
        .where((builder) => {
          builder.where('sender_id', auth.user.id).orWhere('recipient_id', auth.user.id);
        })
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .first();

      if (!transfer) {
        return response.notFound({ success: false, message: 'Recurring transfer not found' });
      }

      return response.json(transfer);
    } catch (error) {
      errorHandler(error, ctx, 'Recurring transfer getByRecurringId error');
    }
  }
}

import { BaseSchema } from '@adonisjs/lucid/schema';

export default class extends BaseSchema {
  tableName = 'transactions';

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add escrow-related fields
      table
        .integer('escrow_id')
        .unsigned()
        .references('id')
        .inTable('escrows')
        .onDelete('SET NULL')
        .nullable();
      
      table
        .integer('recurring_transfer_id')
        .unsigned()
        .references('id')
        .inTable('recurring_transfers')
        .onDelete('SET NULL')
        .nullable();
      
      table
        .integer('pool_contribution_id')
        .unsigned()
        .references('id')
        .inTable('pool_contributions')
        .onDelete('SET NULL')
        .nullable();
      
      // Add new transaction types to support escrow operations
      // Note: This assumes the type field can accommodate new values
      // If it's an enum, we might need to modify the enum definition
      
      // Add indexes for the new foreign keys
      table.index(['escrow_id']);
      table.index(['recurring_transfer_id']);
      table.index(['pool_contribution_id']);
    });
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropIndex(['escrow_id']);
      table.dropIndex(['recurring_transfer_id']);
      table.dropIndex(['pool_contribution_id']);
      
      table.dropColumn('escrow_id');
      table.dropColumn('recurring_transfer_id');
      table.dropColumn('pool_contribution_id');
    });
  }
}

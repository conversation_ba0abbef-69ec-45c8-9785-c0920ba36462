import vine from '@vinejs/vine';

/**
 * Validator for creating recurring transfer
 */
export const createRecurringTransferSchema = vine.compile(
  vine.object({
    recipientId: vine.number().positive(),
    amount: vine.number().positive().decimal([0, 2]),
    currencyCode: vine.string().minLength(3).maxLength(3),
    frequency: vine.enum(['daily', 'weekly', 'monthly', 'yearly']),
    intervalValue: vine.number().positive().max(365).optional(),
    startDate: vine.date().afterField('today'),
    endDate: vine.date().optional().afterField('startDate'),
    maxOccurrences: vine.number().positive().max(10000).optional(),
    description: vine.string().optional().maxLength(500),
    scheduleConfig: vine.object({}).optional(),
    notifySender: vine.boolean().optional(),
    notifyRecipient: vine.boolean().optional(),
    notifyOnFailure: vine.boolean().optional()
  })
);

/**
 * Validator for updating recurring transfer
 */
export const updateRecurringTransferSchema = vine.compile(
  vine.object({
    amount: vine.number().positive().decimal([0, 2]).optional(),
    frequency: vine.enum(['daily', 'weekly', 'monthly', 'yearly']).optional(),
    intervalValue: vine.number().positive().max(365).optional(),
    endDate: vine.date().optional(),
    maxOccurrences: vine.number().positive().max(10000).optional(),
    description: vine.string().optional().maxLength(500),
    notifySender: vine.boolean().optional(),
    notifyRecipient: vine.boolean().optional(),
    notifyOnFailure: vine.boolean().optional()
  })
);

/**
 * Validator for recurring transfer filters
 */
export const recurringTransferFiltersSchema = vine.compile(
  vine.object({
    status: vine.enum(['active', 'paused', 'completed', 'cancelled', 'failed']).optional(),
    type: vine.enum(['sent', 'received']).optional(),
    frequency: vine.enum(['daily', 'weekly', 'monthly', 'yearly']).optional(),
    startDate: vine.date().optional(),
    endDate: vine.date().optional(),
    page: vine.number().positive().optional(),
    limit: vine.number().positive().max(100).optional()
  })
);

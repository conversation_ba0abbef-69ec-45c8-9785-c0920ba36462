"use client";

import { Loader } from "@/components/common/Loader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useSWR } from "@/hooks/useSWR";
import { useWebSocket } from "@/hooks/useWebSocket";
import { contactApi } from "@/lib/contact-api";
import {
  IContact,
  ContactContext,
  ContactFilters,
  ContactStatusUpdate,
  ContactVerificationUpdate
} from "@/types/contact";
import cn, { imageURL } from "@/lib/utils";
import { TickCircle } from "iconsax-react";
import {
  Shield,
  AlertTriangle,
  Star,
  Clock,
  Plus,
  ChevronsUpDown,
  Check
} from "lucide-react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { useEffect, useState, useCallback } from "react";
import { useTranslation } from "react-i18next";

// Enhanced interface that maintains backward compatibility
interface SelectRecipientComboProps {
  // Legacy props for backward compatibility
  user: IContact | any | null;
  onChange: (value: string) => void;
  setUser: (user: IContact | any | null) => void;

  // Enhanced props (optional for backward compatibility)
  context?: ContactContext;
  filterBy?: {
    canReceiveEscrow?: boolean;
    canReceiveRecurringTransfer?: boolean;
    canReceiveFundraisingContribution?: boolean;
    verificationLevel?: 'basic' | 'enhanced' | 'premium';
    maxRiskScore?: number;
  };

  // UI customization
  placeholder?: string;
  disabled?: boolean;
  className?: string;

  // Advanced features (optional)
  enableRealTimeUpdates?: boolean;
  showContactStatus?: boolean;
  showVerificationBadge?: boolean;
  showRiskIndicator?: boolean;
  showRecentContacts?: boolean;
  showFrequentContacts?: boolean;
  allowNewContact?: boolean;

  // Callbacks
  onCreateNewContact?: (contactData: { name: string; email: string }) => void;
  onContactSelect?: (contact: IContact) => void;
}

export default function SelectRecipientCombo({
  user,
  onChange,
  setUser,
  context = 'general',
  filterBy,
  placeholder = "Enter recipient account",
  disabled = false,
  className,
  enableRealTimeUpdates = true,
  showContactStatus = true,
  showVerificationBadge = true,
  showRiskIndicator = false,
  showRecentContacts = true,
  showFrequentContacts = true,
  allowNewContact = false,
  onCreateNewContact,
  onContactSelect,
}: SelectRecipientComboProps) {
  const { t } = useTranslation();
  const [selected, setSelected] = useState<string>("");
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [contacts, setContacts] = useState<IContact[]>([]);
  const [recentContacts, setRecentContacts] = useState<IContact[]>([]);
  const [frequentContacts, setFrequentContacts] = useState<IContact[]>([]);
  const [isLoadingEnhanced, setIsLoadingEnhanced] = useState(false);
  const searchParams = useSearchParams();

  // Legacy SWR for backward compatibility
  const { data: legacyData, isLoading: legacyLoading } = useSWR("/contacts?limit=500");
  const spContact = searchParams.get("contact");

  // WebSocket for real-time updates
  const { subscribe, unsubscribe } = useWebSocket({
    enabled: enableRealTimeUpdates,
  });

  // Build filters based on context and props
  const buildFilters = useCallback((): ContactFilters => {
    const filters: ContactFilters = {
      search: searchValue,
      limit: 50,
      ...filterBy,
    };

    // Context-specific filtering
    switch (context) {
      case 'escrow':
        filters.canReceiveEscrow = true;
        break;
      case 'recurring-transfer':
        filters.canReceiveRecurringTransfer = true;
        break;
      case 'fundraising-pool':
        filters.canReceiveFundraisingContribution = true;
        break;
    }

    return filters;
  }, [searchValue, filterBy, context]);

  // Fetch enhanced contacts
  const fetchContacts = useCallback(async () => {
    if (!contactApi) return; // Fallback to legacy if enhanced API not available

    setIsLoadingEnhanced(true);
    try {
      const filters = buildFilters();
      const response = await contactApi.searchContacts(filters);

      if (response.success) {
        setContacts(response.data.contacts);
        setRecentContacts(response.data.recentContacts || []);
        setFrequentContacts(response.data.frequentContacts || []);
      }
    } catch (error) {
      console.error('Failed to fetch enhanced contacts:', error);
      // Fallback to legacy data
    } finally {
      setIsLoadingEnhanced(false);
    }
  }, [buildFilters]);

  // Legacy user effect for backward compatibility
  useEffect(() => {
    if (user) {
      setSelected(user?.name);
    }
  }, [user]);

  // Legacy search params effect for backward compatibility
  useEffect(() => {
    if (spContact && legacyData?.data) {
      const u = legacyData.data.find(
        (c: Record<string, any>) => c.id?.toString() === spContact,
      );

      if (u) {
        setSelected(u.contact?.customer?.name);
        onChange(u.contactId?.toString());
        setUser({
          id: u.contact?.customer?.id,
          avatar: u.contact?.customer?.profileImage,
          name: u.contact?.customer?.name,
          email: u.contact?.email,
        });
      }
    }
  }, [spContact, legacyData, onChange, setUser]);

  // Debounced search for enhanced contacts
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchValue || open) {
        fetchContacts();
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchValue, open, fetchContacts]);

  // Real-time updates
  useEffect(() => {
    if (enableRealTimeUpdates) {
      const handleContactStatusUpdate = (update: ContactStatusUpdate) => {
        setContacts(prev => prev.map(contact =>
          contact.id === update.contactId
            ? { ...contact, status: update.status }
            : contact
        ));
      };

      const handleContactVerificationUpdate = (update: ContactVerificationUpdate) => {
        setContacts(prev => prev.map(contact =>
          contact.id === update.contactId
            ? {
                ...contact,
                verificationLevel: update.verificationLevel,
                isVerified: update.isVerified
              }
            : contact
        ));
      };

      subscribe('contact:status_changed', handleContactStatusUpdate);
      subscribe('contact:verification_updated', handleContactVerificationUpdate);

      return () => {
        unsubscribe('contact:status_changed', handleContactStatusUpdate);
        unsubscribe('contact:verification_updated', handleContactVerificationUpdate);
      };
    }
  }, [enableRealTimeUpdates, subscribe, unsubscribe]);

  // Helper functions
  const handleContactSelect = (selectedContact: IContact | any) => {
    // Handle both enhanced and legacy contact formats
    const contactName = selectedContact.name || selectedContact.contact?.customer?.name;
    const contactId = selectedContact.id || selectedContact.contactId;
    const contactEmail = selectedContact.email || selectedContact.contact?.email;
    const contactAvatar = selectedContact.avatar || selectedContact.contact?.customer?.profileImage;

    setSelected(contactName);
    onChange(contactId?.toString());

    // Set user in both formats for compatibility
    const userObject = {
      id: selectedContact.id || selectedContact.contact?.customer?.id,
      avatar: contactAvatar,
      name: contactName,
      email: contactEmail,
      // Enhanced properties if available
      ...(selectedContact.status && { status: selectedContact.status }),
      ...(selectedContact.verificationLevel && { verificationLevel: selectedContact.verificationLevel }),
      ...(selectedContact.isVerified !== undefined && { isVerified: selectedContact.isVerified }),
    };

    setUser(userObject);
    onContactSelect?.(selectedContact);
    setOpen(false);
  };

  const getContactStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'blocked':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getVerificationIcon = (level: string) => {
    switch (level) {
      case 'premium':
        return <Shield className="h-3 w-3 text-blue-600" />;
      case 'enhanced':
        return <Shield className="h-3 w-3 text-green-600" />;
      case 'basic':
        return <Shield className="h-3 w-3 text-gray-600" />;
      default:
        return null;
    }
  };

  const getRiskIndicator = (riskScore: number) => {
    if (riskScore >= 70) {
      return <AlertTriangle className="h-3 w-3 text-red-600" />;
    } else if (riskScore >= 40) {
      return <AlertTriangle className="h-3 w-3 text-yellow-600" />;
    }
    return null;
  };

  // Determine which data to use (enhanced or legacy)
  const isLoading = isLoadingEnhanced || legacyLoading;
  const hasEnhancedData = contacts.length > 0 || recentContacts.length > 0 || frequentContacts.length > 0;
  const displayData = hasEnhancedData ? contacts : (legacyData?.data || []);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger
        className={cn(
          "h-12 w-full rounded-lg bg-input px-3 py-2 text-left text-input-placeholder",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        disabled={disabled}
      >
        {selected ? (
          <div className="flex items-center space-x-2">
            {user?.avatar && (
              <Avatar className="h-6 w-6">
                <AvatarImage src={imageURL(user.avatar)} alt={user.name} />
                <AvatarFallback className="text-xs">
                  {user.name?.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
            )}
            <span className="text-foreground truncate">{selected}</span>
            {showVerificationBadge && user?.verificationLevel && getVerificationIcon(user.verificationLevel)}
          </div>
        ) : (
          <span className="text-input-placeholder">
            {t(placeholder)}
          </span>
        )}
      </PopoverTrigger>

      <PopoverContent
        className="w-[var(--radix-popover-trigger-width)] p-0"
        align="start"
      >
        <Command>
          <CommandInput
            placeholder={t("Search...")}
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <CommandList>
            {isLoading ? (
              <div className="w-full px-2.5 py-2">
                <Loader />
              </div>
            ) : (
              <>
                {displayData.length === 0 && searchValue && (
                  <CommandEmpty>
                    <div className="text-center py-4">
                      <p className="text-sm text-muted-foreground mb-2">
                        {t("No contacts found")}
                      </p>
                      {allowNewContact && onCreateNewContact && (
                        <Button
                          size="sm"
                          onClick={() => {
                            onCreateNewContact({ name: searchValue, email: '' });
                            setOpen(false);
                          }}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          {t("Add new contact")}
                        </Button>
                      )}
                    </div>
                  </CommandEmpty>
                )}

                {displayData.length === 0 && !searchValue && (
                  <CommandEmpty>{t("No contacts found.")}</CommandEmpty>
                )}

                {/* Enhanced contacts display */}
                {hasEnhancedData && (
                  <>
                    {/* Recent Contacts */}
                    {showRecentContacts && recentContacts.length > 0 && (
                      <CommandGroup heading={t("Recent")}>
                        {recentContacts.slice(0, 3).map((contact) => (
                          <EnhancedContactItem
                            key={`recent-${contact.id}`}
                            contact={contact}
                            selected={selected}
                            onSelect={handleContactSelect}
                            showStatus={showContactStatus}
                            showVerification={showVerificationBadge}
                            showRisk={showRiskIndicator}
                            getStatusColor={getContactStatusColor}
                            getVerificationIcon={getVerificationIcon}
                            getRiskIndicator={getRiskIndicator}
                          />
                        ))}
                      </CommandGroup>
                    )}

                    {/* Frequent Contacts */}
                    {showFrequentContacts && frequentContacts.length > 0 && (
                      <CommandGroup heading={t("Frequent")}>
                        {frequentContacts.slice(0, 3).map((contact) => (
                          <EnhancedContactItem
                            key={`frequent-${contact.id}`}
                            contact={contact}
                            selected={selected}
                            onSelect={handleContactSelect}
                            showStatus={showContactStatus}
                            showVerification={showVerificationBadge}
                            showRisk={showRiskIndicator}
                            getStatusColor={getContactStatusColor}
                            getVerificationIcon={getVerificationIcon}
                            getRiskIndicator={getRiskIndicator}
                          />
                        ))}
                      </CommandGroup>
                    )}

                    {/* Separator if we have recent/frequent contacts */}
                    {((showRecentContacts && recentContacts.length > 0) ||
                      (showFrequentContacts && frequentContacts.length > 0)) &&
                      contacts.length > 0 && (
                      <CommandSeparator />
                    )}

                    {/* All Enhanced Contacts */}
                    {contacts.length > 0 && (
                      <CommandGroup heading={searchValue ? t("Search Results") : t("All Contacts")}>
                        {contacts.map((contact) => (
                          <EnhancedContactItem
                            key={`enhanced-${contact.id}`}
                            contact={contact}
                            selected={selected}
                            onSelect={handleContactSelect}
                            showStatus={showContactStatus}
                            showVerification={showVerificationBadge}
                            showRisk={showRiskIndicator}
                            getStatusColor={getContactStatusColor}
                            getVerificationIcon={getVerificationIcon}
                            getRiskIndicator={getRiskIndicator}
                          />
                        ))}
                      </CommandGroup>
                    )}
                  </>
                )}

                {/* Legacy contacts display for backward compatibility */}
                {!hasEnhancedData && (
                  <CommandGroup>
                    {displayData.map((contact: Record<string, any>) => (
                      <CommandItem
                        key={contact.id}
                        value={contact.contact?.customer?.name}
                        onSelect={() => handleContactSelect(contact)}
                      >
                        <LegacyContactItem
                          title={contact.contact?.customer?.name}
                          avatar={imageURL(contact.contact?.customer?.profileImage)}
                          email={contact.contact?.email}
                          checked={contact.contact?.customer?.name === selected}
                        />
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

// Enhanced contact item component
interface EnhancedContactItemProps {
  contact: IContact;
  selected: string;
  onSelect: (contact: IContact) => void;
  showStatus: boolean;
  showVerification: boolean;
  showRisk: boolean;
  getStatusColor: (status: string) => string;
  getVerificationIcon: (level: string) => React.ReactNode;
  getRiskIndicator: (riskScore: number) => React.ReactNode;
}

function EnhancedContactItem({
  contact,
  selected,
  onSelect,
  showStatus,
  showVerification,
  showRisk,
  getStatusColor,
  getVerificationIcon,
  getRiskIndicator,
}: EnhancedContactItemProps) {
  const { t } = useTranslation();

  return (
    <CommandItem
      key={contact.id}
      value={contact.name}
      onSelect={() => onSelect(contact)}
      className="flex items-center space-x-3 p-3"
    >
      <Avatar className="h-8 w-8">
        <AvatarImage src={contact.avatar ? imageURL(contact.avatar) : undefined} alt={contact.name} />
        <AvatarFallback>
          {contact.name.split(' ').map(n => n[0]).join('').toUpperCase()}
        </AvatarFallback>
      </Avatar>

      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium truncate">{contact.name}</p>
          {showVerification && getVerificationIcon(contact.verificationLevel)}
          {showRisk && getRiskIndicator(contact.riskScore)}
          {contact.relationshipType === 'frequent' && (
            <Star className="h-3 w-3 text-yellow-500" />
          )}
        </div>
        <p className="text-xs text-muted-foreground truncate">{contact.email}</p>
      </div>

      <div className="flex flex-col items-end space-y-1">
        {showStatus && (
          <Badge variant="outline" className={`text-xs ${getStatusColor(contact.status)}`}>
            {t(contact.status)}
          </Badge>
        )}
        <Check
          className={cn(
            "h-4 w-4",
            contact.name === selected ? "opacity-100" : "opacity-0"
          )}
        />
      </div>
    </CommandItem>
  );
}

// Legacy contact item component for backward compatibility
function LegacyContactItem({
  title,
  avatar,
  email,
  checked = false,
}: {
  title: string;
  avatar?: string;
  email: string;
  checked?: boolean;
}) {
  const { t } = useTranslation();
  return (
    <div className="flex w-full items-center gap-3 px-2.5 py-1">
      <div className="size-10 overflow-hidden rounded-md bg-muted">
        {avatar && (
          <Image
            src={avatar}
            alt={title}
            width={32}
            height={32}
            className="size-10"
          />
        )}
      </div>

      <div className="flex flex-1 flex-col gap-1">
        <h6>{title}</h6>
        <p className="block"> {email} </p>
      </div>

      <div
        className={cn(
          "flex items-center gap-1 px-2 text-xs opacity-0",
          checked && "opacity-100",
        )}
      >
        <TickCircle size={16} variant="Bold" className="text-success" />
        {t("Selected")}
      </div>
    </div>
  );
}

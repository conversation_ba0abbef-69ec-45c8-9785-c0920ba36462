import { DateTime } from 'luxon';

class FundraisingPoolValidator {
  /**
   * Validate pool creation data
   */
  static validatePoolCreation(data) {
    const errors = [];

    // Required fields
    if (!data.creatorId || typeof data.creatorId !== 'number') {
      errors.push('Creator ID is required and must be a number');
    }

    if (!data.title || typeof data.title !== 'string') {
      errors.push('Title is required and must be a string');
    } else if (data.title.length < 5) {
      errors.push('Title must be at least 5 characters long');
    } else if (data.title.length > 100) {
      errors.push('Title must be less than 100 characters');
    }

    if (!data.description || typeof data.description !== 'string') {
      errors.push('Description is required and must be a string');
    } else if (data.description.length < 20) {
      errors.push('Description must be at least 20 characters long');
    } else if (data.description.length > 5000) {
      errors.push('Description must be less than 5000 characters');
    }

    if (!data.targetAmount || typeof data.targetAmount !== 'number' || data.targetAmount <= 0) {
      errors.push('Target amount is required and must be a positive number');
    } else if (data.targetAmount > 1000000) {
      errors.push('Target amount cannot exceed 1,000,000');
    }

    if (!data.currencyCode || typeof data.currencyCode !== 'string') {
      errors.push('Currency code is required and must be a string');
    }

    // Optional field validations
    if (data.category && typeof data.category !== 'string') {
      errors.push('Category must be a string');
    }

    if (data.tags && !Array.isArray(data.tags)) {
      errors.push('Tags must be an array');
    } else if (data.tags && data.tags.length > 10) {
      errors.push('Maximum 10 tags allowed');
    }

    if (data.minimumContribution !== undefined) {
      if (typeof data.minimumContribution !== 'number' || data.minimumContribution < 0) {
        errors.push('Minimum contribution must be a non-negative number');
      } else if (data.minimumContribution >= data.targetAmount) {
        errors.push('Minimum contribution must be less than target amount');
      }
    }

    if (data.maximumContribution !== undefined && data.maximumContribution !== null) {
      if (typeof data.maximumContribution !== 'number' || data.maximumContribution <= 0) {
        errors.push('Maximum contribution must be a positive number');
      } else if (data.minimumContribution && data.maximumContribution < data.minimumContribution) {
        errors.push('Maximum contribution must be greater than minimum contribution');
      }
    }

    // Date validations
    if (data.startDate) {
      const startDate = DateTime.fromISO(data.startDate);
      if (!startDate.isValid) {
        errors.push('Invalid start date format');
      }
    }

    if (data.hasDeadline && data.endDate) {
      const endDate = DateTime.fromISO(data.endDate);
      if (!endDate.isValid) {
        errors.push('Invalid end date format');
      } else if (data.startDate) {
        const startDate = DateTime.fromISO(data.startDate);
        if (endDate <= startDate) {
          errors.push('End date must be after start date');
        }
      } else if (endDate <= DateTime.now()) {
        errors.push('End date must be in the future');
      }
    }

    // Enum validations
    if (data.visibility && !['public', 'private', 'invite_only'].includes(data.visibility)) {
      errors.push('Visibility must be public, private, or invite_only');
    }

    if (data.distributionType && !['immediate', 'on_completion', 'manual'].includes(data.distributionType)) {
      errors.push('Distribution type must be immediate, on_completion, or manual');
    }

    // Boolean validations
    const booleanFields = ['hasDeadline', 'allowAnonymous', 'autoDistribute', 'allowComments', 'showContributors', 'sendUpdates'];
    booleanFields.forEach(field => {
      if (data[field] !== undefined && typeof data[field] !== 'boolean') {
        errors.push(`${field} must be a boolean value`);
      }
    });

    // Platform fee validation
    if (data.platformFeePercentage !== undefined) {
      if (typeof data.platformFeePercentage !== 'number' || data.platformFeePercentage < 0 || data.platformFeePercentage > 20) {
        errors.push('Platform fee percentage must be between 0 and 20');
      }
    }

    // Media validations
    if (data.videoUrl && typeof data.videoUrl !== 'string') {
      errors.push('Video URL must be a string');
    }

    if (data.gallery && !Array.isArray(data.gallery)) {
      errors.push('Gallery must be an array');
    } else if (data.gallery && data.gallery.length > 20) {
      errors.push('Maximum 20 gallery images allowed');
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Validate contribution data
   */
  static validateContribution(contributionData, pool) {
    const errors = [];

    // Amount validation
    if (!contributionData.amount || typeof contributionData.amount !== 'number' || contributionData.amount <= 0) {
      errors.push('Contribution amount is required and must be a positive number');
    }

    // Check minimum contribution
    if (pool.minimumContribution > 0 && contributionData.amount < pool.minimumContribution) {
      errors.push(`Minimum contribution is ${pool.minimumContribution} ${pool.currencyCode}`);
    }

    // Check maximum contribution
    if (pool.maximumContribution && contributionData.amount > pool.maximumContribution) {
      errors.push(`Maximum contribution is ${pool.maximumContribution} ${pool.currencyCode}`);
    }

    // Anonymous contribution validation
    if (contributionData.isAnonymous && !pool.allowAnonymous) {
      errors.push('Anonymous contributions are not allowed for this pool');
    }

    // Contributor information validation for anonymous contributions
    if (!contributionData.contributorId && !contributionData.isAnonymous) {
      if (!contributionData.contributorName || typeof contributionData.contributorName !== 'string') {
        errors.push('Contributor name is required for non-anonymous contributions without user account');
      }
      
      if (!contributionData.contributorEmail || typeof contributionData.contributorEmail !== 'string') {
        errors.push('Contributor email is required for non-anonymous contributions without user account');
      }
    }

    // Message validation
    if (contributionData.message && typeof contributionData.message !== 'string') {
      errors.push('Message must be a string');
    } else if (contributionData.message && contributionData.message.length > 1000) {
      errors.push('Message must be less than 1000 characters');
    }

    // Social links validation
    if (contributionData.socialLinks && typeof contributionData.socialLinks !== 'object') {
      errors.push('Social links must be an object');
    }

    // Boolean validations
    const booleanFields = ['isAnonymous', 'showAmount', 'allowContact'];
    booleanFields.forEach(field => {
      if (contributionData[field] !== undefined && typeof contributionData[field] !== 'boolean') {
        errors.push(`${field} must be a boolean value`);
      }
    });

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Validate pool state for receiving contributions
   */
  static validatePoolForContributions(pool) {
    const errors = [];

    if (pool.status !== 'active') {
      errors.push('Pool is not active');
    }

    if (pool.isExpired) {
      errors.push('Pool has expired');
    }

    if (pool.isTargetReached && pool.distributionType === 'immediate') {
      errors.push('Pool has already reached its target and funds were distributed');
    }

    if (pool.fundsDistributed) {
      errors.push('Pool funds have already been distributed');
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Validate pool permissions for user actions
   */
  static validatePoolPermissions(pool, userId, action) {
    const isCreator = pool.creatorId === userId;
    const isPublic = pool.visibility === 'public';

    const permissions = {
      'view': isPublic || isCreator,
      'edit': isCreator,
      'activate': isCreator && pool.status === 'draft',
      'pause': isCreator && pool.status === 'active',
      'cancel': isCreator && !pool.fundsDistributed,
      'distribute': isCreator && pool.currentAmount > 0 && !pool.fundsDistributed,
      'contribute': isPublic && pool.canReceiveContributions,
      'view_contributors': isCreator || (pool.showContributors && isPublic),
      'view_statistics': isCreator
    };

    if (!permissions[action]) {
      throw new Error(`Invalid action: ${action}`);
    }

    if (!permissions[action]) {
      throw new Error(`Unauthorized: Cannot perform action '${action}' on this pool`);
    }

    return true;
  }

  /**
   * Validate pool update data
   */
  static validatePoolUpdate(updateData, currentPool) {
    const errors = [];

    // Can't update certain fields after activation
    if (currentPool.status !== 'draft') {
      const restrictedFields = ['targetAmount', 'currencyCode', 'minimumContribution', 'maximumContribution'];
      restrictedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          errors.push(`Cannot update ${field} after pool activation`);
        }
      });
    }

    // Title validation
    if (updateData.title !== undefined) {
      if (typeof updateData.title !== 'string' || updateData.title.length < 5 || updateData.title.length > 100) {
        errors.push('Title must be a string between 5 and 100 characters');
      }
    }

    // Description validation
    if (updateData.description !== undefined) {
      if (typeof updateData.description !== 'string' || updateData.description.length < 20 || updateData.description.length > 5000) {
        errors.push('Description must be a string between 20 and 5000 characters');
      }
    }

    // End date validation
    if (updateData.endDate !== undefined) {
      const endDate = DateTime.fromISO(updateData.endDate);
      if (!endDate.isValid) {
        errors.push('Invalid end date format');
      } else if (endDate <= DateTime.now()) {
        errors.push('End date must be in the future');
      }
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Validate pool search criteria
   */
  static validateSearchCriteria(criteria) {
    const errors = [];

    if (criteria.page !== undefined) {
      if (typeof criteria.page !== 'number' || criteria.page < 1) {
        errors.push('Page must be a positive number');
      }
    }

    if (criteria.limit !== undefined) {
      if (typeof criteria.limit !== 'number' || criteria.limit < 1 || criteria.limit > 100) {
        errors.push('Limit must be a number between 1 and 100');
      }
    }

    if (criteria.sortBy !== undefined) {
      const validSortFields = ['created_at', 'target_amount', 'current_amount', 'progress_percentage', 'end_date'];
      if (!validSortFields.includes(criteria.sortBy)) {
        errors.push(`Sort by must be one of: ${validSortFields.join(', ')}`);
      }
    }

    if (criteria.sortOrder !== undefined) {
      if (!['asc', 'desc'].includes(criteria.sortOrder)) {
        errors.push('Sort order must be asc or desc');
      }
    }

    if (criteria.status !== undefined) {
      const validStatuses = ['draft', 'active', 'paused', 'completed', 'cancelled', 'expired'];
      if (!validStatuses.includes(criteria.status)) {
        errors.push(`Status must be one of: ${validStatuses.join(', ')}`);
      }
    }

    if (criteria.visibility !== undefined) {
      const validVisibilities = ['public', 'private', 'invite_only'];
      if (!validVisibilities.includes(criteria.visibility)) {
        errors.push(`Visibility must be one of: ${validVisibilities.join(', ')}`);
      }
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Validate pool categories
   */
  static validateCategory(category) {
    const validCategories = [
      'charity',
      'education',
      'health',
      'environment',
      'technology',
      'arts',
      'sports',
      'community',
      'business',
      'personal',
      'emergency',
      'other'
    ];

    if (category && !validCategories.includes(category)) {
      throw new Error(`Invalid category. Must be one of: ${validCategories.join(', ')}`);
    }

    return true;
  }

  /**
   * Validate pool tags
   */
  static validateTags(tags) {
    if (!Array.isArray(tags)) {
      throw new Error('Tags must be an array');
    }

    if (tags.length > 10) {
      throw new Error('Maximum 10 tags allowed');
    }

    tags.forEach((tag, index) => {
      if (typeof tag !== 'string') {
        throw new Error(`Tag ${index + 1} must be a string`);
      }
      
      if (tag.length < 2 || tag.length > 30) {
        throw new Error(`Tag ${index + 1} must be between 2 and 30 characters`);
      }
      
      if (!/^[a-zA-Z0-9\s-_]+$/.test(tag)) {
        throw new Error(`Tag ${index + 1} contains invalid characters`);
      }
    });

    // Check for duplicates
    const uniqueTags = new Set(tags.map(tag => tag.toLowerCase()));
    if (uniqueTags.size !== tags.length) {
      throw new Error('Duplicate tags are not allowed');
    }

    return true;
  }

  /**
   * Validate distribution conditions
   */
  static validateDistributionConditions(pool) {
    const errors = [];

    if (pool.fundsDistributed) {
      errors.push('Funds have already been distributed');
    }

    if (pool.currentAmount <= 0) {
      errors.push('No funds available for distribution');
    }

    if (pool.status === 'cancelled') {
      errors.push('Cannot distribute funds from cancelled pool');
    }

    // Check if pool should be distributed based on conditions
    if (pool.distributionType === 'on_completion' && !pool.isTargetReached && !pool.isExpired) {
      errors.push('Pool has not reached target amount or deadline for distribution');
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }
}

export default FundraisingPoolValidator;

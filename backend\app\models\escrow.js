import { DateTime } from 'luxon';
import { BaseModel, column, belongsTo, hasMany, beforeCreate, computed } from '@adonisjs/lucid/orm';
import { generateNanoId } from '../utils/generate_unique_id.js';
import User from './user.js';
import EscrowMilestone from './escrow_milestone.js';
import Transaction from './transaction.js';
import EscrowNotification from './escrow_notification.js';

export default class Escrow extends BaseModel {
  @column({ isPrimary: true })
  id;

  @column()
  escrowId;

  @column()
  senderId;

  @column()
  recipientId;

  @column()
  amount;

  @column()
  fee;

  @column()
  total;

  @column()
  currencyCode;

  @column()
  feePayer;

  @column()
  senderFeeAmount;

  @column()
  recipientFeeAmount;

  @column()
  status;

  @column.dateTime()
  deadline;

  @column()
  autoRelease;

  @column()
  deadlineHours;

  @column()
  senderConfirmed;

  @column()
  recipientConfirmed;

  @column.dateTime()
  senderConfirmedAt;

  @column.dateTime()
  recipientConfirmedAt;

  @column.dateTime()
  releasedAt;

  @column()
  releaseType;

  @column()
  releasedByUserId;

  @column()
  description;

  @column()
  metadata;

  @column()
  termsConditions;

  @column()
  hasMilestones;

  @column()
  totalMilestones;

  @column()
  completedMilestones;

  @column()
  reminder24hSent;

  @column()
  reminder6hSent;

  @column()
  reminder1hSent;

  @column.dateTime({ autoCreate: true })
  createdAt;

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  updatedAt;

  // Relationships
  @belongsTo(() => User, { foreignKey: 'senderId' })
  sender;

  @belongsTo(() => User, { foreignKey: 'recipientId' })
  recipient;

  @belongsTo(() => User, { foreignKey: 'releasedByUserId' })
  releasedBy;

  @hasMany(() => EscrowMilestone)
  milestones;

  @hasMany(() => Transaction)
  transactions;

  @hasMany(() => EscrowNotification)
  notifications;

  // Computed properties
  @computed()
  get metadataParsed() {
    return typeof this.metadata !== 'object' ? JSON.parse(this?.metadata || '{}') : this?.metadata;
  }

  @computed()
  get isExpired() {
    return DateTime.now() > DateTime.fromJSDate(this.deadline);
  }

  @computed()
  get timeRemaining() {
    const now = DateTime.now();
    const deadline = DateTime.fromJSDate(this.deadline);
    return deadline.diff(now, ['days', 'hours', 'minutes']).toObject();
  }

  @computed()
  get progressPercentage() {
    if (!this.hasMilestones || this.totalMilestones === 0) {
      return this.status === 'completed' ? 100 : 0;
    }
    return Math.round((this.completedMilestones / this.totalMilestones) * 100);
  }

  @computed()
  get canBeReleased() {
    if (this.status !== 'active') return false;
    if (this.hasMilestones) {
      return this.completedMilestones === this.totalMilestones;
    }
    return this.senderConfirmed && this.recipientConfirmed;
  }

  @computed()
  get needsReminder() {
    if (this.status !== 'active') return false;
    const now = DateTime.now();
    const deadline = DateTime.fromJSDate(this.deadline);
    const hoursRemaining = deadline.diff(now, 'hours').hours;
    
    return {
      reminder24h: hoursRemaining <= 24 && !this.reminder24hSent,
      reminder6h: hoursRemaining <= 6 && !this.reminder6hSent,
      reminder1h: hoursRemaining <= 1 && !this.reminder1hSent
    };
  }

  // Hooks
  @beforeCreate()
  static assignEscrowId(escrow) {
    escrow.escrowId = generateNanoId(16);
  }

  // Instance methods
  async confirmBySender() {
    this.senderConfirmed = true;
    this.senderConfirmedAt = DateTime.now();
    await this.save();
  }

  async confirmByRecipient() {
    this.recipientConfirmed = true;
    this.recipientConfirmedAt = DateTime.now();
    await this.save();
  }

  async release(releaseType = 'manual', releasedByUserId = null) {
    this.status = 'completed';
    this.releasedAt = DateTime.now();
    this.releaseType = releaseType;
    this.releasedByUserId = releasedByUserId;
    await this.save();
  }

  async cancel() {
    this.status = 'cancelled';
    await this.save();
  }

  async expire() {
    this.status = 'expired';
    await this.save();
  }

  async updateMilestoneProgress() {
    const milestones = await this.related('milestones').query();
    const completed = milestones.filter(m => m.status === 'released').length;
    this.completedMilestones = completed;
    await this.save();
  }
}

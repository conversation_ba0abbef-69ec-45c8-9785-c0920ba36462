"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PoolFilters, FundraisingPoolStatus, PoolCategory } from "@/types/fundraising-pool";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { X, Filter, RotateCcw, Search } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

interface FundraisingPoolFiltersProps {
  filters: PoolFilters;
  onFiltersChange: (filters: PoolFilters) => void;
}

export function FundraisingPoolFilters({ filters, onFiltersChange }: FundraisingPoolFiltersProps) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState(filters.search || '');

  const statusOptions: { value: FundraisingPoolStatus; label: string }[] = [
    { value: 'draft', label: 'Draft' },
    { value: 'active', label: 'Active' },
    { value: 'paused', label: 'Paused' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'expired', label: 'Expired' },
  ];

  const categoryOptions: { value: PoolCategory; label: string }[] = [
    { value: 'charity', label: 'Charity' },
    { value: 'education', label: 'Education' },
    { value: 'health', label: 'Health' },
    { value: 'environment', label: 'Environment' },
    { value: 'technology', label: 'Technology' },
    { value: 'arts', label: 'Arts' },
    { value: 'sports', label: 'Sports' },
    { value: 'community', label: 'Community' },
    { value: 'business', label: 'Business' },
    { value: 'personal', label: 'Personal' },
    { value: 'emergency', label: 'Emergency' },
    { value: 'other', label: 'Other' },
  ];

  const updateFilter = (key: keyof PoolFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    if (value === '' || value === undefined) {
      delete newFilters[key];
    }
    onFiltersChange(newFilters);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateFilter('search', searchQuery);
  };

  const clearFilters = () => {
    setSearchQuery('');
    onFiltersChange({});
  };

  const getActiveFiltersCount = () => {
    return Object.keys(filters).filter(key => 
      filters[key as keyof PoolFilters] !== undefined && 
      filters[key as keyof PoolFilters] !== ''
    ).length;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <form onSubmit={handleSearchSubmit} className="flex space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder={t('Search pools by title, description, or creator...')}
            className="pl-10"
          />
        </div>
        <Button type="submit" variant="outline">
          {t('Search')}
        </Button>
      </form>

      {/* Advanced Filters Toggle */}
      <div className="flex items-center justify-between">
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger asChild>
            <Button variant="outline" className="flex items-center space-x-2">
              <Filter className="h-4 w-4" />
              <span>{t('Advanced Filters')}</span>
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </CollapsibleTrigger>
          
          <CollapsibleContent className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {/* Status Filter */}
                  <div className="space-y-2">
                    <Label htmlFor="status">{t('Status')}</Label>
                    <Select
                      value={filters.status || ''}
                      onValueChange={(value) => updateFilter('status', value as FundraisingPoolStatus)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('All statuses')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">{t('All statuses')}</SelectItem>
                        {statusOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {t(option.label)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Category Filter */}
                  <div className="space-y-2">
                    <Label htmlFor="category">{t('Category')}</Label>
                    <Select
                      value={filters.category || ''}
                      onValueChange={(value) => updateFilter('category', value as PoolCategory)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('All categories')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">{t('All categories')}</SelectItem>
                        {categoryOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {t(option.label)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Target Amount Range */}
                  <div className="space-y-2">
                    <Label>{t('Target Amount Range')}</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="number"
                        placeholder={t('Min')}
                        value={filters.targetMin || ''}
                        onChange={(e) => updateFilter('targetMin', e.target.value ? parseFloat(e.target.value) : undefined)}
                      />
                      <Input
                        type="number"
                        placeholder={t('Max')}
                        value={filters.targetMax || ''}
                        onChange={(e) => updateFilter('targetMax', e.target.value ? parseFloat(e.target.value) : undefined)}
                      />
                    </div>
                  </div>
                </div>

                {/* Filter Actions */}
                <div className="flex items-center justify-between mt-6 pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    {activeFiltersCount > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearFilters}
                        className="flex items-center space-x-1"
                      >
                        <RotateCcw className="h-3 w-3" />
                        <span>{t('Clear All')}</span>
                      </Button>
                    )}
                  </div>
                  
                  <p className="text-sm text-muted-foreground">
                    {activeFiltersCount > 0 
                      ? t('{{count}} filter(s) applied', { count: activeFiltersCount })
                      : t('No filters applied')
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          </CollapsibleContent>
        </Collapsible>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>{t('Search')}: "{filters.search}"</span>
              <button
                onClick={() => {
                  setSearchQuery('');
                  updateFilter('search', undefined);
                }}
                className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filters.status && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>{t('Status')}: {t(filters.status)}</span>
              <button
                onClick={() => updateFilter('status', undefined)}
                className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filters.category && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>{t('Category')}: {t(filters.category)}</span>
              <button
                onClick={() => updateFilter('category', undefined)}
                className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {(filters.targetMin || filters.targetMax) && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>
                {t('Target')}: {filters.targetMin || 0} - {filters.targetMax || '∞'}
              </span>
              <button
                onClick={() => {
                  updateFilter('targetMin', undefined);
                  updateFilter('targetMax', undefined);
                }}
                className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}

import { DateTime } from 'luxon';
import { BaseModel, column, belongsTo, beforeCreate, computed } from '@adonisjs/lucid/orm';
import { generateNanoId } from '../utils/generate_unique_id.js';
import FundraisingPool from './fundraising_pool.js';
import User from './user.js';
import Transaction from './transaction.js';

export default class PoolContribution extends BaseModel {
  @column({ isPrimary: true })
  id;

  @column()
  contributionId;

  @column()
  poolId;

  @column()
  contributorId;

  @column()
  amount;

  @column()
  fee;

  @column()
  netAmount;

  @column()
  currencyCode;

  @column()
  contributorName;

  @column()
  contributorEmail;

  @column()
  isAnonymous;

  @column()
  showAmount;

  @column()
  status;

  @column()
  paymentMethod;

  @column()
  transactionReference;

  @column()
  transactionId;

  @column()
  message;

  @column()
  allowContact;

  @column()
  socialLinks;

  @column.dateTime()
  refundedAt;

  @column()
  refundReason;

  @column()
  refundAmount;

  @column()
  metadata;

  @column()
  ipAddress;

  @column()
  userAgent;

  @column.dateTime({ autoCreate: true })
  createdAt;

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  updatedAt;

  // Relationships
  @belongsTo(() => FundraisingPool, { foreignKey: 'poolId' })
  pool;

  @belongsTo(() => User, { foreignKey: 'contributorId' })
  contributor;

  @belongsTo(() => Transaction, { foreignKey: 'transactionId' })
  transaction;

  // Computed properties
  @computed()
  get socialLinksParsed() {
    return typeof this.socialLinks !== 'object' 
      ? JSON.parse(this?.socialLinks || '{}') 
      : this?.socialLinks;
  }

  @computed()
  get metadataParsed() {
    return typeof this.metadata !== 'object' 
      ? JSON.parse(this?.metadata || '{}') 
      : this?.metadata;
  }

  @computed()
  get displayName() {
    if (this.isAnonymous) return 'Anonymous';
    if (this.contributorName) return this.contributorName;
    if (this.contributor) return this.contributor.name || this.contributor.email;
    return 'Unknown';
  }

  @computed()
  get displayAmount() {
    return this.showAmount ? this.netAmount : null;
  }

  @computed()
  get isCompleted() {
    return this.status === 'completed';
  }

  @computed()
  get isPending() {
    return this.status === 'pending' || this.status === 'processing';
  }

  @computed()
  get isFailed() {
    return this.status === 'failed';
  }

  @computed()
  get isRefunded() {
    return this.status === 'refunded';
  }

  @computed()
  get canBeRefunded() {
    return this.status === 'completed' && !this.isRefunded;
  }

  @computed()
  get contributionAge() {
    const now = DateTime.now();
    const created = DateTime.fromJSDate(this.createdAt);
    return created.diff(now, ['days', 'hours', 'minutes']).toObject();
  }

  // Hooks
  @beforeCreate()
  static assignContributionId(contribution) {
    contribution.contributionId = generateNanoId(16);
  }

  // Instance methods
  async complete() {
    this.status = 'completed';
    await this.save();
    
    // Update pool progress
    const pool = await this.related('pool').query().first();
    if (pool) {
      await pool.updateProgress();
    }
  }

  async fail(reason = null) {
    this.status = 'failed';
    if (reason && this.metadata) {
      const metadata = this.metadataParsed;
      metadata.failureReason = reason;
      this.metadata = JSON.stringify(metadata);
    }
    await this.save();
  }

  async refund(refundAmount = null, reason = null) {
    this.status = 'refunded';
    this.refundedAt = DateTime.now();
    this.refundAmount = refundAmount || this.netAmount;
    this.refundReason = reason;
    await this.save();
    
    // Update pool progress
    const pool = await this.related('pool').query().first();
    if (pool) {
      await pool.updateProgress();
    }
  }

  async updateTransaction(transactionId, transactionReference = null) {
    this.transactionId = transactionId;
    if (transactionReference) {
      this.transactionReference = transactionReference;
    }
    await this.save();
  }

  async addMetadata(key, value) {
    const metadata = this.metadataParsed;
    metadata[key] = value;
    this.metadata = JSON.stringify(metadata);
    await this.save();
  }

  async updateStatus(newStatus) {
    const oldStatus = this.status;
    this.status = newStatus;
    await this.save();
    
    // If status changed to completed or refunded, update pool progress
    if ((newStatus === 'completed' || newStatus === 'refunded') && oldStatus !== newStatus) {
      const pool = await this.related('pool').query().first();
      if (pool) {
        await pool.updateProgress();
      }
    }
  }

  // Static methods
  static async getContributionsByPool(poolId, options = {}) {
    const query = this.query()
      .where('pool_id', poolId)
      .where('status', 'completed');
    
    if (options.includeAnonymous === false) {
      query.where('is_anonymous', false);
    }
    
    if (options.showAmountOnly === true) {
      query.where('show_amount', true);
    }
    
    if (options.orderBy) {
      query.orderBy(options.orderBy, options.direction || 'desc');
    }
    
    if (options.limit) {
      query.limit(options.limit);
    }
    
    return await query.exec();
  }

  static async getTotalContributionsByUser(userId) {
    const result = await this.query()
      .where('contributor_id', userId)
      .where('status', 'completed')
      .sum('net_amount as total')
      .count('* as count')
      .first();
    
    return {
      totalAmount: result.total || 0,
      totalContributions: result.count || 0
    };
  }
}

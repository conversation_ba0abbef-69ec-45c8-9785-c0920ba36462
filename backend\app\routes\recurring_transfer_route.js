import router from '@adonisjs/core/services/router';
import { middleware } from '#start/kernel';

const RecurringTransfersController = () => import('#controllers/recurring_transfers_controller');

// Recurring transfer routes - require authentication
router.group(() => {
  // Main recurring transfer routes
  router.get('/recurring-transfers', [RecurringTransfersController, 'index']);
  router.post('/recurring-transfers', [RecurringTransfersController, 'store']);
  router.get('/recurring-transfers/statistics', [RecurringTransfersController, 'statistics']);
  router.get('/recurring-transfers/upcoming', [RecurringTransfersController, 'upcoming']);
  router.get('/recurring-transfers/export', [RecurringTransfersController, 'exportCsv']);
  router.get('/recurring-transfers/preview-schedule', [RecurringTransfersController, 'previewSchedule']);
  
  // Individual recurring transfer routes
  router.get('/recurring-transfers/:id', [RecurringTransfersController, 'show']);
  router.put('/recurring-transfers/:id', [RecurringTransfersController, 'update']);
  router.post('/recurring-transfers/:id/pause', [RecurringTransfersController, 'pause']);
  router.post('/recurring-transfers/:id/resume', [RecurringTransfersController, 'resume']);
  router.post('/recurring-transfers/:id/cancel', [RecurringTransfersController, 'cancel']);
  router.get('/recurring-transfers/:id/history', [RecurringTransfersController, 'history']);
  
  // Recurring transfer by public ID
  router.get('/recurring-transfers/by-id/:recurringId', [RecurringTransfersController, 'getByRecurringId']);
  
}).prefix('/api/v1').middleware([middleware.auth()]);

// Public recurring transfer routes (no authentication required)
router.group(() => {
  // Public schedule preview (for planning before signup)
  router.get('/recurring-transfers/preview-schedule', [RecurringTransfersController, 'previewSchedule']);
}).prefix('/api/v1/public');

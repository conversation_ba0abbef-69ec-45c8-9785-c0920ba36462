# PaySnap Frontend Integration Guide

This guide provides comprehensive instructions for integrating and testing the PaySnap frontend components with the AdonisJS backend.

## Table of Contents

1. [Setup and Configuration](#setup-and-configuration)
2. [API Integration](#api-integration)
3. [Component Usage](#component-usage)
4. [Testing Strategy](#testing-strategy)
5. [<PERSON>rro<PERSON> Handling](#error-handling)
6. [Performance Optimization](#performance-optimization)
7. [Deployment](#deployment)

## Setup and Configuration

### Environment Variables

Create a `.env.local` file in the frontend root:

```env
NEXT_PUBLIC_API_URL=http://localhost:3333/api
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_WEBSOCKET_URL=ws://localhost:3333
```

### Dependencies

Ensure all required dependencies are installed:

```bash
npm install
# or
yarn install
```

### TypeScript Configuration

The project uses strict TypeScript configuration. Ensure your `tsconfig.json` includes:

```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true
  }
}
```

## API Integration

### Using the API Client

The `escrowApi` client provides all necessary methods for backend communication:

```typescript
import { escrowApi } from '@/lib/escrow-api';

// Get escrows with filters
const escrows = await escrowApi.getEscrows({
  status: 'active',
  page: 1,
  limit: 10
});

// Create new escrow
const newEscrow = await escrowApi.createEscrow({
  recipientId: 123,
  amount: 1000,
  currencyCode: 'USD',
  description: 'Payment for services'
});
```

### Error Handling

Always wrap API calls in try-catch blocks:

```typescript
try {
  const result = await escrowApi.createEscrow(data);
  // Handle success
} catch (error) {
  const errorMessage = getErrorMessage(error);
  // Handle error
}
```

### Real-time Updates

For real-time features, implement WebSocket connections:

```typescript
const ws = new WebSocket(process.env.NEXT_PUBLIC_WEBSOCKET_URL);

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  // Handle real-time updates
};
```

## Component Usage

### Escrow Components

#### EscrowList
```typescript
import { EscrowList } from '@/components/escrow/EscrowList';

<EscrowList
  escrows={escrows}
  currentUserId={user.id}
  onFiltersChange={handleFiltersChange}
  onAction={handleEscrowAction}
  onRefresh={refreshEscrows}
/>
```

#### CreateEscrowForm
```typescript
import { CreateEscrowForm } from '@/components/escrow/CreateEscrowForm';

<CreateEscrowForm
  onSubmit={handleCreateEscrow}
  isLoading={isCreating}
  error={createError}
/>
```

### Milestone Components

#### MilestoneList
```typescript
import { MilestoneList } from '@/components/milestone/MilestoneList';

<MilestoneList
  escrowId={escrowId}
  milestones={milestones}
  userRole={userRole}
  onAction={handleMilestoneAction}
/>
```

### Recurring Transfer Components

#### RecurringTransferList
```typescript
import { RecurringTransferList } from '@/components/recurring-transfer/RecurringTransferList';

<RecurringTransferList
  transfers={transfers}
  currentUserId={user.id}
  onFiltersChange={handleFiltersChange}
  onAction={handleTransferAction}
/>
```

### Fundraising Pool Components

#### FundraisingPoolList
```typescript
import { FundraisingPoolList } from '@/components/fundraising-pool/FundraisingPoolList';

<FundraisingPoolList
  pools={pools}
  currentUserId={user.id}
  onFiltersChange={handleFiltersChange}
  onAction={handlePoolAction}
/>
```

### Admin Components

#### AdminDashboard
```typescript
import { AdminDashboard } from '@/components/admin/AdminDashboard';

<AdminDashboard
  overview={dashboardData}
  onRefresh={refreshDashboard}
  onExport={exportData}
/>
```

## Testing Strategy

### Unit Tests

Test individual components in isolation:

```bash
npm run test
# or
yarn test
```

### Integration Tests

Test component interactions with API:

```bash
npm run test:integration
# or
yarn test:integration
```

### E2E Tests

Use Playwright or Cypress for end-to-end testing:

```bash
npm run test:e2e
# or
yarn test:e2e
```

### Test Coverage

Maintain high test coverage:

```bash
npm run test:coverage
# or
yarn test:coverage
```

## Error Handling

### Global Error Boundary

Implement a global error boundary:

```typescript
import { ErrorBoundary } from '@/components/common/ErrorBoundary';

<ErrorBoundary>
  <App />
</ErrorBoundary>
```

### API Error Handling

Use consistent error handling patterns:

```typescript
import { isApiError, getErrorMessage } from '@/lib/escrow-api';

try {
  const result = await escrowApi.someMethod();
} catch (error) {
  if (isApiError(error)) {
    // Handle API-specific errors
    console.error('API Error:', error.message);
  } else {
    // Handle other errors
    console.error('Unexpected Error:', getErrorMessage(error));
  }
}
```

### User-Friendly Error Messages

Display meaningful error messages to users:

```typescript
const getDisplayError = (error: any): string => {
  if (isApiError(error)) {
    switch (error.code) {
      case 'INSUFFICIENT_FUNDS':
        return 'You do not have sufficient funds for this transaction.';
      case 'INVALID_RECIPIENT':
        return 'The selected recipient is invalid.';
      default:
        return error.message;
    }
  }
  return 'An unexpected error occurred. Please try again.';
};
```

## Performance Optimization

### Code Splitting

Use dynamic imports for large components:

```typescript
import dynamic from 'next/dynamic';

const AdminDashboard = dynamic(
  () => import('@/components/admin/AdminDashboard'),
  { loading: () => <div>Loading...</div> }
);
```

### Memoization

Use React.memo for expensive components:

```typescript
import { memo } from 'react';

export const EscrowCard = memo(({ escrow, onAction }) => {
  // Component implementation
});
```

### Virtual Scrolling

For large lists, implement virtual scrolling:

```typescript
import { FixedSizeList as List } from 'react-window';

<List
  height={600}
  itemCount={items.length}
  itemSize={100}
  itemData={items}
>
  {Row}
</List>
```

## Deployment

### Build Process

```bash
npm run build
# or
yarn build
```

### Environment Configuration

Set production environment variables:

```env
NEXT_PUBLIC_API_URL=https://api.paysnap.com
NEXT_PUBLIC_APP_URL=https://app.paysnap.com
NEXT_PUBLIC_WEBSOCKET_URL=wss://api.paysnap.com
```

### Docker Deployment

Use the provided Dockerfile:

```bash
docker build -t paysnap-frontend .
docker run -p 3000:3000 paysnap-frontend
```

### Monitoring

Implement error tracking and performance monitoring:

```typescript
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
});
```

## Best Practices

### State Management

Use React Query for server state:

```typescript
import { useQuery } from '@tanstack/react-query';

const { data: escrows, isLoading, error } = useQuery({
  queryKey: ['escrows', filters],
  queryFn: () => escrowApi.getEscrows(filters),
});
```

### Form Handling

Use React Hook Form with Zod validation:

```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

const form = useForm({
  resolver: zodResolver(schema),
  defaultValues: initialValues,
});
```

### Accessibility

Ensure components are accessible:

```typescript
<button
  aria-label="Release escrow funds"
  onClick={handleRelease}
>
  Release Funds
</button>
```

### Internationalization

Use react-i18next for translations:

```typescript
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<h1>{t('Welcome to PaySnap')}</h1>
```

## Troubleshooting

### Common Issues

1. **API Connection Issues**: Check CORS settings and API URL
2. **Type Errors**: Ensure all types are properly imported
3. **Build Failures**: Check for missing dependencies
4. **Performance Issues**: Use React DevTools Profiler

### Debug Mode

Enable debug mode for development:

```env
NODE_ENV=development
DEBUG=true
```

### Logging

Use structured logging:

```typescript
import { logger } from '@/lib/logger';

logger.info('Escrow created', { escrowId, amount });
logger.error('Failed to create escrow', { error, data });
```

## Support

For additional support:

1. Check the component documentation
2. Review the test files for usage examples
3. Consult the backend API documentation
4. Contact the development team

## Contributing

When contributing new components:

1. Follow the established patterns
2. Include comprehensive tests
3. Update type definitions
4. Add documentation
5. Ensure accessibility compliance

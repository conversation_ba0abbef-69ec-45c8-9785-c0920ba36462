import { BaseSchema } from '@adonisjs/lucid/schema';

export default class extends BaseSchema {
  tableName = 'escrow_notifications';

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id');
      
      // Relationship to escrow
      table
        .integer('escrow_id')
        .unsigned()
        .references('id')
        .inTable('escrows')
        .onDelete('CASCADE')
        .notNullable();
      
      // Notification details
      table.enum('type', [
        'deadline_reminder_24h',
        'deadline_reminder_6h',
        'deadline_reminder_1h',
        'escrow_created',
        'escrow_funded',
        'escrow_confirmed',
        'escrow_released',
        'escrow_expired',
        'milestone_completed',
        'milestone_approved',
        'milestone_rejected'
      ]).notNullable();
      
      // Recipient information
      table
        .integer('user_id')
        .unsigned()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .notNullable();
      
      // Notification status
      table.enum('status', ['pending', 'sent', 'failed', 'cancelled']).defaultTo('pending');
      table.timestamp('scheduled_at').notNullable();
      table.timestamp('sent_at').nullable();
      table.integer('retry_count').defaultTo(0);
      table.text('error_message').nullable();
      
      // Notification content
      table.string('title').notNullable();
      table.text('message').notNullable();
      table.json('data').nullable(); // Additional data for the notification
      
      // Delivery channels
      table.boolean('email_sent').defaultTo(false);
      table.boolean('push_sent').defaultTo(false);
      table.boolean('sms_sent').defaultTo(false);
      
      table.timestamp('created_at');
      table.timestamp('updated_at');
      
      // Indexes
      table.index(['escrow_id', 'type']);
      table.index(['user_id', 'status']);
      table.index(['status', 'scheduled_at']);
      table.index(['type', 'status']);
    });
  }

  async down() {
    this.schema.dropTable(this.tableName);
  }
}

# Critical Fixes for PaySnap Frontend Components

## Fix #1: Resolve Type Definition Conflicts

### Problem
Two contact type files exist causing potential import conflicts:
- `frontend/types/contact.ts` (new enhanced)
- `frontend/types/contacts.ts` (existing legacy)

### Solution: Create Unified Contact Types

```typescript
// frontend/types/contact-unified.ts
import { User } from "@/types/auth";

// Legacy contact class for backward compatibility
export class CustomerContact {
  id: number;
  userId: number;
  contactId: number;
  createdAt: Date;
  updatedAt: Date;
  contact: User;

  constructor(data: any) {
    this.id = data?.id;
    this.userId = data?.userId;
    this.contactId = data?.contactId;
    this.createdAt = new Date(data?.createdAt);
    this.updatedAt = new Date(data?.updatedAt);
    this.contact = new User(data?.contact);
  }
}

// Enhanced contact interface
export interface IContact {
  id: number;
  name: string;
  email: string;
  avatar?: string;
  status: ContactStatus;
  isVerified: boolean;
  verificationLevel: VerificationLevel;
  lastTransactionAt?: Date;
  totalTransactions: number;
  totalVolume: number;
  riskScore: number;
  tags: string[];
  capabilities: ContactCapabilities;
  relationshipType: RelationshipType;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Type aliases for clarity
export type ContactStatus = 'active' | 'blocked' | 'pending';
export type VerificationLevel = 'basic' | 'enhanced' | 'premium';
export type RelationshipType = 'contact' | 'frequent' | 'new' | 'blocked';
export type ContactContext = 'escrow' | 'recurring-transfer' | 'fundraising-pool' | 'general';

// Utility function to convert legacy to enhanced format
export function convertLegacyContact(legacy: CustomerContact): IContact {
  return {
    id: legacy.contact.id,
    name: legacy.contact.name || legacy.contact.email,
    email: legacy.contact.email,
    avatar: legacy.contact.avatar,
    status: 'active', // Default for legacy
    isVerified: false, // Default for legacy
    verificationLevel: 'basic', // Default for legacy
    lastTransactionAt: legacy.updatedAt,
    totalTransactions: 0, // Default for legacy
    totalVolume: 0, // Default for legacy
    riskScore: 0, // Default for legacy
    tags: [],
    capabilities: {
      canReceiveEscrow: true,
      canReceiveRecurringTransfer: true,
      canReceiveFundraisingContribution: true,
    },
    relationshipType: 'contact',
    createdAt: legacy.createdAt,
    updatedAt: legacy.updatedAt,
  };
}
```

## Fix #2: Add Missing Dependencies

### Problem
External dependencies not declared in package.json

### Solution: Package.json Updates

```json
{
  "dependencies": {
    "lucide-react": "^0.294.0",
    "@tanstack/react-query": "^5.8.4",
    "react-window": "^1.8.8",
    "react-window-infinite-loader": "^1.0.9"
  },
  "devDependencies": {
    "@types/react-window": "^1.8.8"
  }
}
```

### Installation Commands
```bash
npm install lucide-react @tanstack/react-query react-window react-window-infinite-loader
npm install -D @types/react-window

# or with yarn
yarn add lucide-react @tanstack/react-query react-window react-window-infinite-loader
yarn add -D @types/react-window
```

## Fix #3: Implement Error Boundaries

### Problem
No error boundaries to catch component crashes

### Solution: Create Error Boundary Component

```typescript
// frontend/components/common/ErrorBoundary.tsx
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Alert variant="destructive" className="m-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <div>
              <p className="font-medium">Something went wrong</p>
              <p className="text-sm text-muted-foreground">
                {this.state.error?.message || 'An unexpected error occurred'}
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={this.handleRetry}
              className="ml-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      );
    }

    return this.props.children;
  }
}

// Specific error boundary for contact selection
export function ContactSelectionErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load contact selection. Please refresh the page.
          </AlertDescription>
        </Alert>
      }
      onError={(error) => {
        // Log to monitoring service
        console.error('Contact selection error:', error);
      }}
    >
      {children}
    </ErrorBoundary>
  );
}
```

## Fix #4: Enhanced WebSocket Subscription Cleanup

### Problem
WebSocket subscriptions may not clean up properly causing memory leaks

### Solution: Improved useWebSocket Hook

```typescript
// frontend/hooks/useWebSocket-enhanced.ts
import { useEffect, useRef, useCallback } from 'react';

export function useWebSocketEnhanced(options: UseWebSocketOptions = {}) {
  const wsRef = useRef<PaySnapWebSocketManager | null>(null);
  const subscriptionsRef = useRef<Map<string, () => void>>(new Map());

  useEffect(() => {
    if (!wsRef.current) {
      wsRef.current = wsManager || new PaySnapWebSocketManager(options);
      if (!wsManager) {
        wsManager = wsRef.current;
      }
    }

    if (options.enabled !== false) {
      wsRef.current.connect().catch(console.error);
    }

    // Cleanup all subscriptions on unmount
    return () => {
      subscriptionsRef.current.forEach(unsubscribe => unsubscribe());
      subscriptionsRef.current.clear();
    };
  }, [options.enabled]);

  const subscribe = useCallback(<T extends WebSocketEventType>(
    eventType: T, 
    handler: WebSocketEventHandler<T>
  ) => {
    const unsubscribe = wsRef.current?.subscribe(eventType, handler) || (() => {});
    
    // Store unsubscribe function for cleanup
    const key = `${eventType}-${Date.now()}-${Math.random()}`;
    subscriptionsRef.current.set(key, unsubscribe);
    
    // Return enhanced unsubscribe that also removes from our tracking
    return () => {
      unsubscribe();
      subscriptionsRef.current.delete(key);
    };
  }, []);

  const unsubscribe = useCallback(<T extends WebSocketEventType>(
    eventType: T, 
    handler: WebSocketEventHandler<T>
  ) => {
    wsRef.current?.unsubscribe(eventType, handler);
    
    // Remove from our tracking (find by handler)
    for (const [key, unsub] of subscriptionsRef.current.entries()) {
      if (unsub.toString().includes(handler.toString())) {
        subscriptionsRef.current.delete(key);
        break;
      }
    }
  }, []);

  return {
    subscribe,
    unsubscribe,
    disconnect: useCallback(() => wsRef.current?.disconnect(), []),
    isConnected: useCallback(() => wsRef.current?.isConnected() || false, []),
    getConnectionState: useCallback(() => wsRef.current?.getConnectionState() || 'disconnected', []),
  };
}
```

## Fix #5: Progressive API Enhancement

### Problem
Frontend expects enhanced endpoints that may not exist yet

### Solution: Progressive Enhancement Pattern

```typescript
// frontend/lib/contact-api-enhanced.ts
class ContactApiClientEnhanced {
  private hasEnhancedEndpoints: boolean | null = null;

  private async checkEnhancedEndpoints(): Promise<boolean> {
    if (this.hasEnhancedEndpoints !== null) {
      return this.hasEnhancedEndpoints;
    }

    try {
      // Test if enhanced endpoint exists
      const response = await fetch(`${API_BASE_URL}/contacts/search?test=true`);
      this.hasEnhancedEndpoints = response.status !== 404;
    } catch {
      this.hasEnhancedEndpoints = false;
    }

    return this.hasEnhancedEndpoints;
  }

  async searchContacts(filters?: ContactFilters): Promise<ContactSearchResponse> {
    const hasEnhanced = await this.checkEnhancedEndpoints();

    if (hasEnhanced) {
      try {
        return await this.searchContactsEnhanced(filters);
      } catch (error) {
        console.warn('Enhanced endpoint failed, falling back to legacy:', error);
        return await this.searchContactsLegacy(filters);
      }
    } else {
      return await this.searchContactsLegacy(filters);
    }
  }

  private async searchContactsEnhanced(filters?: ContactFilters): Promise<ContactSearchResponse> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, String(value));
        }
      });
    }
    
    return this.request<ContactSearchResponse>(`/contacts/search?${params.toString()}`);
  }

  private async searchContactsLegacy(filters?: ContactFilters): Promise<ContactSearchResponse> {
    // Convert to legacy format
    const legacyParams = new URLSearchParams();
    if (filters?.search) {
      legacyParams.append('search', filters.search);
    }
    if (filters?.limit) {
      legacyParams.append('limit', filters.limit.toString());
    }

    const response = await this.request<any>(`/contacts?${legacyParams.toString()}`);
    
    // Convert legacy response to enhanced format
    return {
      success: true,
      data: {
        contacts: response.data?.map(convertLegacyContact) || [],
        suggestions: [],
        recentContacts: [],
        frequentContacts: [],
      },
    };
  }
}
```

## Fix #6: Unified Loading States

### Problem
Multiple loading states not properly coordinated

### Solution: Unified Loading Hook

```typescript
// frontend/hooks/useUnifiedLoading.ts
import { useMemo } from 'react';

interface LoadingStates {
  enhanced?: boolean;
  legacy?: boolean;
  websocket?: boolean;
  api?: boolean;
}

export function useUnifiedLoading(states: LoadingStates) {
  const isLoading = useMemo(() => {
    return Object.values(states).some(state => state === true);
  }, [states]);

  const loadingDetails = useMemo(() => {
    const activeStates = Object.entries(states)
      .filter(([_, isActive]) => isActive)
      .map(([key, _]) => key);

    return {
      isLoading,
      activeStates,
      count: activeStates.length,
    };
  }, [states, isLoading]);

  return loadingDetails;
}

// Usage in SelectRecipientCombo
const loadingState = useUnifiedLoading({
  enhanced: isLoadingEnhanced,
  legacy: legacyLoading,
  websocket: !isConnected(),
});

const { isLoading } = loadingState;
```

## Fix #7: Enhanced WebSocket Reconnection

### Problem
Reconnection attempts may not handle all edge cases

### Solution: Exponential Backoff with Circuit Breaker

```typescript
// Enhanced reconnection logic for WebSocket manager
class PaySnapWebSocketManagerEnhanced extends PaySnapWebSocketManager {
  private circuitBreakerState: 'closed' | 'open' | 'half-open' = 'closed';
  private failureCount = 0;
  private lastFailureTime = 0;
  private circuitBreakerTimeout = 60000; // 1 minute

  private scheduleReconnect(): void {
    // Circuit breaker logic
    if (this.circuitBreakerState === 'open') {
      const timeSinceLastFailure = Date.now() - this.lastFailureTime;
      if (timeSinceLastFailure < this.circuitBreakerTimeout) {
        console.log('Circuit breaker open, skipping reconnection attempt');
        return;
      } else {
        this.circuitBreakerState = 'half-open';
      }
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached, opening circuit breaker');
      this.circuitBreakerState = 'open';
      this.lastFailureTime = Date.now();
      return;
    }

    // Exponential backoff with jitter
    const baseDelay = this.reconnectInterval;
    const exponentialDelay = baseDelay * Math.pow(2, this.reconnectAttempts);
    const jitter = Math.random() * 1000; // Add up to 1 second jitter
    const maxDelay = 30000; // Max 30 seconds
    
    const delay = Math.min(exponentialDelay + jitter, maxDelay);

    this.clearReconnectTimer();
    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) after ${delay}ms`);
      
      this.connect()
        .then(() => {
          // Success - reset circuit breaker
          this.circuitBreakerState = 'closed';
          this.failureCount = 0;
          this.reconnectAttempts = 0;
        })
        .catch((error) => {
          console.error('Reconnection failed:', error);
          this.failureCount++;
          
          if (this.circuitBreakerState === 'half-open') {
            this.circuitBreakerState = 'open';
            this.lastFailureTime = Date.now();
          }
          
          // Continue trying if under limit
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        });
    }, delay);
  }
}
```

## Implementation Priority

1. **Immediate (Day 1)**: Fix #2 (Dependencies), Fix #6 (Loading States)
2. **High Priority (Day 2)**: Fix #1 (Type Conflicts), Fix #3 (Error Boundaries)
3. **Medium Priority (Day 3)**: Fix #4 (Memory Leaks), Fix #5 (API Fallbacks)
4. **Low Priority (Week 2)**: Fix #7 (Enhanced Reconnection)

These fixes will resolve all critical issues and make the components production-ready.

import { BaseSchema } from '@adonisjs/lucid/schema';

export default class extends BaseSchema {
  tableName = 'escrows';

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id');
      table.string('escrow_id').notNullable().unique();
      
      // Parties involved
      table
        .integer('sender_id')
        .unsigned()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .notNullable();
      
      table
        .integer('recipient_id')
        .unsigned()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .notNullable();
      
      // Financial details
      table.double('amount').notNullable();
      table.double('fee').notNullable().defaultTo(0);
      table.double('total').notNullable();
      table.string('currency_code').notNullable();
      
      // Fee management
      table.enum('fee_payer', ['sender', 'recipient', 'split']).defaultTo('sender');
      table.double('sender_fee_amount').defaultTo(0);
      table.double('recipient_fee_amount').defaultTo(0);
      
      // Escrow status and timing
      table.enum('status', [
        'pending',
        'active',
        'completed',
        'cancelled',
        'expired',
        'disputed'
      ]).defaultTo('pending');
      
      table.timestamp('deadline').notNullable();
      table.boolean('auto_release').defaultTo(true);
      table.integer('deadline_hours').defaultTo(72);
      
      // Confirmations
      table.boolean('sender_confirmed').defaultTo(false);
      table.boolean('recipient_confirmed').defaultTo(false);
      table.timestamp('sender_confirmed_at').nullable();
      table.timestamp('recipient_confirmed_at').nullable();
      
      // Release information
      table.timestamp('released_at').nullable();
      table.enum('release_type', ['manual', 'automatic', 'admin']).nullable();
      table.integer('released_by_user_id').unsigned().nullable();
      
      // Additional metadata
      table.text('description').nullable();
      table.json('metadata').nullable();
      table.text('terms_conditions').nullable();
      
      // Milestone support
      table.boolean('has_milestones').defaultTo(false);
      table.integer('total_milestones').defaultTo(0);
      table.integer('completed_milestones').defaultTo(0);
      
      // Notifications
      table.boolean('reminder_24h_sent').defaultTo(false);
      table.boolean('reminder_6h_sent').defaultTo(false);
      table.boolean('reminder_1h_sent').defaultTo(false);
      
      table.timestamp('created_at');
      table.timestamp('updated_at');
      
      // Indexes for performance
      table.index(['sender_id', 'status']);
      table.index(['recipient_id', 'status']);
      table.index(['status', 'deadline']);
      table.index(['currency_code']);
      table.index(['created_at']);
    });
  }

  async down() {
    this.schema.dropTable(this.tableName);
  }
}

import vine from '@vinejs/vine';

/**
 * Validator for creating escrow
 */
export const createEscrowSchema = vine.compile(
  vine.object({
    recipientId: vine.number().positive(),
    amount: vine.number().positive().decimal([0, 2]),
    currencyCode: vine.string().minLength(3).maxLength(3),
    description: vine.string().optional().maxLength(1000),
    termsConditions: vine.string().optional().maxLength(5000),
    deadlineHours: vine.number().optional().min(1).max(8760), // Max 1 year
    feePayer: vine.enum(['sender', 'recipient', 'split']).optional(),
    milestones: vine.array(
      vine.object({
        title: vine.string().minLength(3).maxLength(100),
        description: vine.string().optional().maxLength(1000),
        percentage: vine.number().positive().max(100),
        deliverables: vine.array(vine.string()).optional(),
        dueDate: vine.date().optional()
      })
    ).optional()
  })
);

/**
 * Validator for confirming escrow
 */
export const confirmEscrowSchema = vine.compile(
  vine.object({
    userType: vine.enum(['sender', 'recipient'])
  })
);

/**
 * Validator for cancelling escrow
 */
export const cancelEscrowSchema = vine.compile(
  vine.object({
    reason: vine.string().optional().maxLength(500)
  })
);

/**
 * Validator for escrow filters
 */
export const escrowFiltersSchema = vine.compile(
  vine.object({
    status: vine.enum(['pending', 'active', 'completed', 'cancelled', 'expired', 'disputed']).optional(),
    type: vine.enum(['sent', 'received']).optional(),
    startDate: vine.date().optional(),
    endDate: vine.date().optional(),
    page: vine.number().positive().optional(),
    limit: vine.number().positive().max(100).optional()
  })
);

import errorHandler from '#exceptions/error_handler';
import Escrow from '#models/escrow';
import EscrowMilestone from '#models/escrow_milestone';
import escrowService from '#services/escrow_service';
import milestoneService from '#services/milestone_service';
import { createEscrowSchema, confirmEscrowSchema, cancelEscrowSchema } from '#validators/escrow';
import exportService from '#services/export_service';

export default class EscrowsController {
  /**
   * Get user's escrows
   */
  async index(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const { page, limit, status, type, ...filters } = request.qs();
      
      let query = Escrow.query()
        .where((builder) => {
          builder.where('sender_id', auth.user.id).orWhere('recipient_id', auth.user.id);
        })
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .preload('milestones')
        .orderBy('created_at', 'desc');

      // Apply filters
      if (status) {
        query = query.where('status', status);
      }

      if (type === 'sent') {
        query = query.where('sender_id', auth.user.id);
      } else if (type === 'received') {
        query = query.where('recipient_id', auth.user.id);
      }

      // Apply date filters
      if (filters.startDate) {
        query = query.where('created_at', '>=', filters.startDate);
      }
      
      if (filters.endDate) {
        query = query.where('created_at', '<=', filters.endDate);
      }

      const data = page && limit ? await query.paginate(page, limit) : await query.exec();
      
      return response.json(data);
    } catch (error) {
      errorHandler(error, ctx, 'Escrows index error');
    }
  }

  /**
   * Get escrow by ID
   */
  async show(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const escrowId = request.param('id');
      
      const escrow = await Escrow.query()
        .where('id', escrowId)
        .where((builder) => {
          builder.where('sender_id', auth.user.id).orWhere('recipient_id', auth.user.id);
        })
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .preload('milestones', (query) => query.orderBy('order_index'))
        .preload('transactions')
        .preload('notifications', (query) => query.orderBy('created_at', 'desc'))
        .first();

      if (!escrow) {
        return response.notFound({ success: false, message: 'Escrow not found' });
      }

      return response.json(escrow);
    } catch (error) {
      errorHandler(error, ctx, 'Escrow show error');
    }
  }

  /**
   * Create new escrow
   */
  async store(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const data = await request.validateUsing(createEscrowSchema);
      
      // Add sender ID from authenticated user
      data.senderId = auth.user.id;
      
      const escrow = await escrowService.createEscrow(data);
      
      return response.created({
        success: true,
        message: 'Escrow created successfully',
        data: escrow
      });
    } catch (error) {
      errorHandler(error, ctx, 'Escrow creation error');
    }
  }

  /**
   * Fund an escrow
   */
  async fund(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const escrowId = request.param('id');
      
      const result = await escrowService.fundEscrow(escrowId, auth.user.id);
      
      return response.json({
        success: true,
        message: 'Escrow funded successfully',
        data: result
      });
    } catch (error) {
      errorHandler(error, ctx, 'Escrow funding error');
    }
  }

  /**
   * Confirm escrow
   */
  async confirm(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const escrowId = request.param('id');
      const { userType } = await request.validateUsing(confirmEscrowSchema);
      
      const escrow = await escrowService.confirmEscrow(escrowId, auth.user.id, userType);
      
      return response.json({
        success: true,
        message: 'Escrow confirmed successfully',
        data: escrow
      });
    } catch (error) {
      errorHandler(error, ctx, 'Escrow confirmation error');
    }
  }

  /**
   * Release escrow funds
   */
  async release(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const escrowId = request.param('id');
      
      const result = await escrowService.releaseEscrow(escrowId, auth.user.id, 'manual');
      
      return response.json({
        success: true,
        message: 'Escrow released successfully',
        data: result
      });
    } catch (error) {
      errorHandler(error, ctx, 'Escrow release error');
    }
  }

  /**
   * Cancel escrow
   */
  async cancel(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const escrowId = request.param('id');
      const { reason } = await request.validateUsing(cancelEscrowSchema);
      
      const escrow = await escrowService.cancelEscrow(escrowId, auth.user.id, reason);
      
      return response.json({
        success: true,
        message: 'Escrow cancelled successfully',
        data: escrow
      });
    } catch (error) {
      errorHandler(error, ctx, 'Escrow cancellation error');
    }
  }

  /**
   * Get escrow statistics for user
   */
  async statistics(ctx) {
    const { auth, response } = ctx;
    
    try {
      const userId = auth.user.id;
      
      const stats = await Escrow.query()
        .where((builder) => {
          builder.where('sender_id', userId).orWhere('recipient_id', userId);
        })
        .select('status')
        .groupBy('status')
        .count('* as count');

      const sentStats = await Escrow.query()
        .where('sender_id', userId)
        .select('status')
        .groupBy('status')
        .count('* as count');

      const receivedStats = await Escrow.query()
        .where('recipient_id', userId)
        .select('status')
        .groupBy('status')
        .count('* as count');

      const totalAmount = await Escrow.query()
        .where((builder) => {
          builder.where('sender_id', userId).orWhere('recipient_id', userId);
        })
        .where('status', 'completed')
        .sum('amount as total');

      return response.json({
        overall: stats,
        sent: sentStats,
        received: receivedStats,
        totalCompletedAmount: totalAmount[0]?.total || 0
      });
    } catch (error) {
      errorHandler(error, ctx, 'Escrow statistics error');
    }
  }

  /**
   * Export escrows to CSV
   */
  async exportCsv(ctx) {
    try {
      await exportService.exportData(ctx, 'escrows');
    } catch (error) {
      errorHandler(error, ctx, 'Escrow export error');
    }
  }

  /**
   * Get escrow by escrow_id (public identifier)
   */
  async getByEscrowId(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const escrowId = request.param('escrowId');
      
      const escrow = await Escrow.query()
        .where('escrow_id', escrowId)
        .where((builder) => {
          builder.where('sender_id', auth.user.id).orWhere('recipient_id', auth.user.id);
        })
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .preload('milestones', (query) => query.orderBy('order_index'))
        .first();

      if (!escrow) {
        return response.notFound({ success: false, message: 'Escrow not found' });
      }

      return response.json(escrow);
    } catch (error) {
      errorHandler(error, ctx, 'Escrow getByEscrowId error');
    }
  }

  /**
   * Get user's active escrows count
   */
  async activeCount(ctx) {
    const { auth, response } = ctx;
    
    try {
      const count = await Escrow.query()
        .where((builder) => {
          builder.where('sender_id', auth.user.id).orWhere('recipient_id', auth.user.id);
        })
        .where('status', 'active')
        .count('* as total');

      return response.json({ activeEscrows: count[0]?.total || 0 });
    } catch (error) {
      errorHandler(error, ctx, 'Active escrows count error');
    }
  }

  /**
   * Get escrows requiring action from user
   */
  async requiresAction(ctx) {
    const { auth, response } = ctx;
    
    try {
      const userId = auth.user.id;
      
      // Escrows where user needs to take action
      const escrows = await Escrow.query()
        .where('status', 'active')
        .where((builder) => {
          // Sender needs to confirm or approve milestones
          builder.where((subBuilder) => {
            subBuilder
              .where('sender_id', userId)
              .where((confirmBuilder) => {
                confirmBuilder
                  .where('sender_confirmed', false)
                  .orWhereHas('milestones', (milestoneBuilder) => {
                    milestoneBuilder
                      .where('status', 'completed')
                      .where('sender_approved', false);
                  });
              });
          })
          // Recipient needs to confirm or complete milestones
          .orWhere((subBuilder) => {
            subBuilder
              .where('recipient_id', userId)
              .where((confirmBuilder) => {
                confirmBuilder
                  .where('recipient_confirmed', false)
                  .orWhereHas('milestones', (milestoneBuilder) => {
                    milestoneBuilder
                      .whereIn('status', ['pending', 'in_progress']);
                  });
              });
          });
        })
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .preload('milestones', (query) => {
          query.whereIn('status', ['pending', 'in_progress', 'completed']).orderBy('order_index');
        })
        .orderBy('deadline')
        .limit(10);

      return response.json(escrows);
    } catch (error) {
      errorHandler(error, ctx, 'Escrows requiring action error');
    }
  }
}

import vine from '@vinejs/vine';

/**
 * Validator for creating fundraising pool
 */
export const createPoolSchema = vine.compile(
  vine.object({
    title: vine.string().minLength(5).maxLength(100),
    description: vine.string().minLength(20).maxLength(5000),
    category: vine.enum([
      'charity', 'education', 'health', 'environment', 'technology', 
      'arts', 'sports', 'community', 'business', 'personal', 'emergency', 'other'
    ]).optional(),
    tags: vine.array(vine.string().minLength(2).maxLength(30)).maxLength(10).optional(),
    targetAmount: vine.number().positive().max(1000000),
    currencyCode: vine.string().minLength(3).maxLength(3),
    minimumContribution: vine.number().min(0).optional(),
    maximumContribution: vine.number().positive().optional(),
    startDate: vine.date().optional(),
    endDate: vine.date().optional().afterField('startDate'),
    hasDeadline: vine.boolean().optional(),
    visibility: vine.enum(['public', 'private', 'invite_only']).optional(),
    allowAnonymous: vine.boolean().optional(),
    autoDistribute: vine.boolean().optional(),
    distributionType: vine.enum(['immediate', 'on_completion', 'manual']).optional(),
    coverImage: vine.string().url().optional(),
    gallery: vine.array(vine.string().url()).maxLength(20).optional(),
    videoUrl: vine.string().url().optional(),
    allowComments: vine.boolean().optional(),
    showContributors: vine.boolean().optional(),
    sendUpdates: vine.boolean().optional(),
    platformFeePercentage: vine.number().min(0).max(20).optional()
  })
);

/**
 * Validator for updating fundraising pool
 */
export const updatePoolSchema = vine.compile(
  vine.object({
    title: vine.string().minLength(5).maxLength(100).optional(),
    description: vine.string().minLength(20).maxLength(5000).optional(),
    category: vine.enum([
      'charity', 'education', 'health', 'environment', 'technology', 
      'arts', 'sports', 'community', 'business', 'personal', 'emergency', 'other'
    ]).optional(),
    tags: vine.array(vine.string().minLength(2).maxLength(30)).maxLength(10).optional(),
    endDate: vine.date().optional(),
    coverImage: vine.string().url().optional(),
    gallery: vine.array(vine.string().url()).maxLength(20).optional(),
    videoUrl: vine.string().url().optional(),
    allowComments: vine.boolean().optional(),
    showContributors: vine.boolean().optional(),
    sendUpdates: vine.boolean().optional()
  })
);

/**
 * Validator for contributing to pool
 */
export const contributeToPoolSchema = vine.compile(
  vine.object({
    amount: vine.number().positive().decimal([0, 2]),
    contributorName: vine.string().optional().maxLength(100),
    contributorEmail: vine.string().email().optional(),
    isAnonymous: vine.boolean().optional(),
    showAmount: vine.boolean().optional(),
    message: vine.string().optional().maxLength(1000),
    allowContact: vine.boolean().optional(),
    socialLinks: vine.object({
      twitter: vine.string().url().optional(),
      linkedin: vine.string().url().optional(),
      website: vine.string().url().optional()
    }).optional(),
    paymentMethod: vine.string().optional()
  })
);

/**
 * Validator for pool search criteria
 */
export const poolSearchSchema = vine.compile(
  vine.object({
    q: vine.string().optional().maxLength(100),
    category: vine.enum([
      'charity', 'education', 'health', 'environment', 'technology', 
      'arts', 'sports', 'community', 'business', 'personal', 'emergency', 'other'
    ]).optional(),
    status: vine.enum(['draft', 'active', 'paused', 'completed', 'cancelled', 'expired']).optional(),
    visibility: vine.enum(['public', 'private', 'invite_only']).optional(),
    sortBy: vine.enum(['created_at', 'target_amount', 'current_amount', 'progress_percentage', 'end_date']).optional(),
    sortOrder: vine.enum(['asc', 'desc']).optional(),
    page: vine.number().positive().optional(),
    limit: vine.number().positive().max(100).optional()
  })
);

import errorHandler from '#exceptions/error_handler';
import Escrow from '#models/escrow';
import EscrowMilestone from '#models/escrow_milestone';
import Transaction from '#models/transaction';
import User from '#models/user';
import escrowService from '#services/escrow_service';
import { DateTime } from 'luxon';
import exportService from '#services/export_service';

export default class AdminEscrowsController {
  /**
   * Get all escrows with admin filters
   */
  async index(ctx) {
    const { request, response } = ctx;
    
    try {
      const { 
        page = 1, 
        limit = 20, 
        status, 
        search, 
        dateFrom, 
        dateTo,
        amountMin,
        amountMax,
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = request.qs();
      
      let query = Escrow.query()
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .preload('milestones');

      // Apply filters
      if (status) {
        query = query.where('status', status);
      }

      if (search) {
        query = query.where((builder) => {
          builder
            .where('escrow_id', 'like', `%${search}%`)
            .orWhere('description', 'like', `%${search}%`)
            .orWhereHas('sender', (userBuilder) => {
              userBuilder.where('email', 'like', `%${search}%`);
            })
            .orWhereHas('recipient', (userBuilder) => {
              userBuilder.where('email', 'like', `%${search}%`);
            });
        });
      }

      if (dateFrom) {
        query = query.where('created_at', '>=', dateFrom);
      }

      if (dateTo) {
        query = query.where('created_at', '<=', dateTo);
      }

      if (amountMin) {
        query = query.where('amount', '>=', parseFloat(amountMin));
      }

      if (amountMax) {
        query = query.where('amount', '<=', parseFloat(amountMax));
      }

      query = query.orderBy(sortBy, sortOrder);

      const escrows = await query.paginate(page, limit);
      
      return response.json(escrows);
    } catch (error) {
      errorHandler(error, ctx, 'Admin escrows index error');
    }
  }

  /**
   * Get escrow details for admin
   */
  async show(ctx) {
    const { request, response } = ctx;
    
    try {
      const escrowId = request.param('id');
      
      const escrow = await Escrow.query()
        .where('id', escrowId)
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .preload('milestones', (query) => query.orderBy('order_index'))
        .preload('transactions', (query) => query.orderBy('created_at', 'desc'))
        .preload('notifications', (query) => query.orderBy('created_at', 'desc'))
        .first();

      if (!escrow) {
        return response.notFound({ success: false, message: 'Escrow not found' });
      }

      return response.json(escrow);
    } catch (error) {
      errorHandler(error, ctx, 'Admin escrow show error');
    }
  }

  /**
   * Force release escrow (admin action)
   */
  async forceRelease(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const escrowId = request.param('id');
      const { reason } = request.only(['reason']);
      
      const result = await escrowService.releaseEscrow(escrowId, auth.user.id, 'admin');
      
      // Log admin action
      await this.logAdminAction(auth.user.id, 'force_release_escrow', {
        escrowId,
        reason,
        timestamp: DateTime.now().toISO()
      });
      
      return response.json({
        success: true,
        message: 'Escrow force released successfully',
        data: result
      });
    } catch (error) {
      errorHandler(error, ctx, 'Admin escrow force release error');
    }
  }

  /**
   * Force cancel escrow (admin action)
   */
  async forceCancel(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const escrowId = request.param('id');
      const { reason } = request.only(['reason']);
      
      const escrow = await escrowService.cancelEscrow(escrowId, auth.user.id, reason);
      
      // Log admin action
      await this.logAdminAction(auth.user.id, 'force_cancel_escrow', {
        escrowId,
        reason,
        timestamp: DateTime.now().toISO()
      });
      
      return response.json({
        success: true,
        message: 'Escrow force cancelled successfully',
        data: escrow
      });
    } catch (error) {
      errorHandler(error, ctx, 'Admin escrow force cancel error');
    }
  }

  /**
   * Get escrow statistics for admin dashboard
   */
  async statistics(ctx) {
    const { request, response } = ctx;
    
    try {
      const { period = '30d' } = request.qs();
      
      let dateFrom;
      switch (period) {
        case '7d':
          dateFrom = DateTime.now().minus({ days: 7 });
          break;
        case '30d':
          dateFrom = DateTime.now().minus({ days: 30 });
          break;
        case '90d':
          dateFrom = DateTime.now().minus({ days: 90 });
          break;
        case '1y':
          dateFrom = DateTime.now().minus({ years: 1 });
          break;
        default:
          dateFrom = DateTime.now().minus({ days: 30 });
      }

      // Overall statistics
      const totalEscrows = await Escrow.query().count('* as total');
      const activeEscrows = await Escrow.query().where('status', 'active').count('* as total');
      const completedEscrows = await Escrow.query().where('status', 'completed').count('* as total');
      const expiredEscrows = await Escrow.query().where('status', 'expired').count('* as total');

      // Period statistics
      const periodEscrows = await Escrow.query()
        .where('created_at', '>=', dateFrom.toSQL())
        .count('* as total');

      const periodVolume = await Escrow.query()
        .where('created_at', '>=', dateFrom.toSQL())
        .where('status', 'completed')
        .sum('amount as volume');

      // Status breakdown
      const statusBreakdown = await Escrow.query()
        .select('status')
        .groupBy('status')
        .count('* as count');

      // Currency breakdown
      const currencyBreakdown = await Escrow.query()
        .select('currency_code')
        .groupBy('currency_code')
        .count('* as count')
        .sum('amount as volume');

      // High value escrows (>$10,000 equivalent)
      const highValueEscrows = await Escrow.query()
        .where('amount', '>', 10000)
        .where('created_at', '>=', dateFrom.toSQL())
        .count('* as total');

      // Disputed escrows
      const disputedEscrows = await Escrow.query()
        .where('status', 'disputed')
        .count('* as total');

      // Average escrow amount
      const avgAmount = await Escrow.query()
        .where('status', 'completed')
        .avg('amount as average');

      // Escrows by day (for charts)
      const escrowsByDay = await Escrow.query()
        .where('created_at', '>=', dateFrom.toSQL())
        .select('created_at')
        .groupByRaw('DATE(created_at)')
        .count('* as count')
        .orderBy('created_at');

      return response.json({
        overview: {
          total: totalEscrows[0]?.total || 0,
          active: activeEscrows[0]?.total || 0,
          completed: completedEscrows[0]?.total || 0,
          expired: expiredEscrows[0]?.total || 0,
          disputed: disputedEscrows[0]?.total || 0
        },
        period: {
          escrows: periodEscrows[0]?.total || 0,
          volume: periodVolume[0]?.volume || 0,
          highValue: highValueEscrows[0]?.total || 0,
          averageAmount: avgAmount[0]?.average || 0
        },
        breakdowns: {
          status: statusBreakdown,
          currency: currencyBreakdown
        },
        charts: {
          escrowsByDay
        }
      });
    } catch (error) {
      errorHandler(error, ctx, 'Admin escrow statistics error');
    }
  }

  /**
   * Get escrows requiring admin attention
   */
  async requiresAttention(ctx) {
    const { response } = ctx;
    
    try {
      // Disputed escrows
      const disputedEscrows = await Escrow.query()
        .where('status', 'disputed')
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .orderBy('updated_at', 'desc')
        .limit(10);

      // Expired escrows not yet processed
      const expiredEscrows = await Escrow.query()
        .where('status', 'active')
        .where('deadline', '<', DateTime.now().toSQL())
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .orderBy('deadline')
        .limit(10);

      // High value escrows (>$50,000)
      const highValueEscrows = await Escrow.query()
        .where('amount', '>', 50000)
        .where('status', 'active')
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .orderBy('amount', 'desc')
        .limit(5);

      // Long running escrows (>30 days active)
      const longRunningEscrows = await Escrow.query()
        .where('status', 'active')
        .where('created_at', '<', DateTime.now().minus({ days: 30 }).toSQL())
        .preload('sender', (query) => query.preload('customer'))
        .preload('recipient', (query) => query.preload('customer'))
        .orderBy('created_at')
        .limit(10);

      return response.json({
        disputed: disputedEscrows,
        expired: expiredEscrows,
        highValue: highValueEscrows,
        longRunning: longRunningEscrows
      });
    } catch (error) {
      errorHandler(error, ctx, 'Admin escrows requiring attention error');
    }
  }

  /**
   * Export escrows for admin
   */
  async exportCsv(ctx) {
    try {
      await exportService.exportData(ctx, 'admin_escrows');
    } catch (error) {
      errorHandler(error, ctx, 'Admin escrows export error');
    }
  }

  /**
   * Get escrow audit trail
   */
  async auditTrail(ctx) {
    const { request, response } = ctx;
    
    try {
      const escrowId = request.param('id');
      
      // Get all transactions related to this escrow
      const transactions = await Transaction.query()
        .where('escrow_id', escrowId)
        .orderBy('created_at');

      // Get all milestone changes
      const milestones = await EscrowMilestone.query()
        .where('escrow_id', escrowId)
        .orderBy('created_at');

      // Get escrow status changes (this would require an audit log table in a real implementation)
      const escrow = await Escrow.find(escrowId);

      return response.json({
        escrow,
        transactions,
        milestones,
        // In a real implementation, you'd have an audit log table
        statusChanges: []
      });
    } catch (error) {
      errorHandler(error, ctx, 'Admin escrow audit trail error');
    }
  }

  /**
   * Log admin action (helper method)
   */
  async logAdminAction(adminId, action, data) {
    // In a real implementation, you'd log this to an admin actions table
    console.log(`Admin Action: ${action} by user ${adminId}`, data);
  }
}

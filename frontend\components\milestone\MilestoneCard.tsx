"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { IMilestone } from "@/types/milestone";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Clock, 
  DollarSign, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  MoreHorizontal,
  Target,
  Calendar,
  FileText,
  Eye
} from "lucide-react";
import { MilestoneActions } from "./MilestoneActions";

interface MilestoneCardProps {
  milestone: IMilestone;
  userRole: 'sender' | 'recipient';
  onAction?: (action: string, escrowId: number, data?: any) => Promise<void>;
  isLoading?: boolean;
}

export function MilestoneCard({ 
  milestone, 
  userRole, 
  onAction,
  isLoading = false 
}: MilestoneCardProps) {
  const { t } = useTranslation();
  const [showDetails, setShowDetails] = useState(false);

  const getStatusIcon = () => {
    switch (milestone.status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'in_progress':
        return <Target className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      case 'released':
        return <DollarSign className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = () => {
    switch (milestone.status) {
      case 'pending':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'released':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const canComplete = milestone.canBeCompleted && userRole === 'recipient';
  const canApprove = milestone.canBeApproved && userRole === 'sender';
  const canRelease = milestone.canBeReleased && userRole === 'sender';

  return (
    <Card className={`w-full transition-all ${milestone.isOverdue ? 'border-red-200 bg-red-50' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-muted-foreground">
                #{milestone.orderIndex}
              </span>
              <Badge 
                variant="outline" 
                className={`${getStatusColor()} flex items-center space-x-1`}
              >
                {getStatusIcon()}
                <span className="capitalize">{t(milestone.status)}</span>
              </Badge>
              {milestone.isOverdue && (
                <Badge variant="destructive" className="text-xs">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  {t('Overdue')}
                </Badge>
              )}
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setShowDetails(!showDetails)}>
                <Eye className="h-4 w-4 mr-2" />
                {showDetails ? t('Hide Details') : t('Show Details')}
              </DropdownMenuItem>
              {canComplete && (
                <DropdownMenuItem onClick={() => onAction?.('complete', milestone.id)}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {t('Mark Complete')}
                </DropdownMenuItem>
              )}
              {canApprove && (
                <DropdownMenuItem onClick={() => onAction?.('approve', milestone.id)}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {t('Approve')}
                </DropdownMenuItem>
              )}
              {canRelease && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => onAction?.('release', milestone.id)}>
                    <DollarSign className="h-4 w-4 mr-2" />
                    {t('Release Funds')}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div>
          <h3 className="font-semibold text-lg">{milestone.title}</h3>
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span className="flex items-center space-x-1">
                <DollarSign className="h-3 w-3" />
                <span>{milestone.amount.toLocaleString()}</span>
              </span>
              <span className="flex items-center space-x-1">
                <Target className="h-3 w-3" />
                <span>{milestone.percentage}%</span>
              </span>
              {milestone.dueDate && (
                <span className="flex items-center space-x-1">
                  <Calendar className="h-3 w-3" />
                  <span>{milestone.getDueDate()}</span>
                </span>
              )}
            </div>
            <span className={`text-sm font-medium ${milestone.isOverdue ? 'text-red-600' : 'text-green-600'}`}>
              {milestone.timeRemaining}
            </span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {milestone.description && (
          <p className="text-sm text-muted-foreground">
            {milestone.description}
          </p>
        )}

        {/* Deliverables */}
        {milestone.deliverables && milestone.deliverables.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium flex items-center space-x-1">
              <FileText className="h-3 w-3" />
              <span>{t('Deliverables')}</span>
            </h4>
            <div className="space-y-1">
              {milestone.deliverables.map((deliverable, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm">
                  <div className="w-1.5 h-1.5 bg-muted-foreground rounded-full" />
                  <span>{deliverable}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Evidence */}
        {milestone.evidence && milestone.evidence.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">{t('Evidence')}</h4>
            <div className="grid grid-cols-2 gap-2">
              {milestone.evidence.map((evidence, index) => (
                <div key={index} className="p-2 border rounded text-xs">
                  <div className="flex items-center space-x-1 mb-1">
                    <Badge variant="outline" className="text-xs">
                      {evidence.type}
                    </Badge>
                  </div>
                  {evidence.description && (
                    <p className="text-muted-foreground">{evidence.description}</p>
                  )}
                  <a 
                    href={evidence.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {evidence.filename || t('View')}
                  </a>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Approval Status */}
        {milestone.status === 'completed' && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">{t('Approval Status')}</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center space-x-1">
                {milestone.senderApproved ? (
                  <CheckCircle className="h-3 w-3 text-green-600" />
                ) : (
                  <Clock className="h-3 w-3 text-yellow-600" />
                )}
                <span>{t('Sender')}</span>
              </div>
              <div className="flex items-center space-x-1">
                {milestone.recipientApproved ? (
                  <CheckCircle className="h-3 w-3 text-green-600" />
                ) : (
                  <Clock className="h-3 w-3 text-yellow-600" />
                )}
                <span>{t('Recipient')}</span>
              </div>
            </div>
          </div>
        )}

        {/* Completion Notes */}
        {milestone.completionNotes && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">{t('Completion Notes')}</h4>
            <p className="text-sm text-muted-foreground bg-muted p-2 rounded">
              {milestone.completionNotes}
            </p>
          </div>
        )}

        {/* Rejection Reason */}
        {milestone.status === 'rejected' && milestone.rejectionReason && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-red-600">{t('Rejection Reason')}</h4>
            <p className="text-sm text-red-600 bg-red-50 p-2 rounded border border-red-200">
              {milestone.rejectionReason}
            </p>
          </div>
        )}

        {/* Expanded Details */}
        {showDetails && (
          <>
            <Separator />
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div>
                <p className="text-muted-foreground">{t('Created')}</p>
                <p>{milestone.getCreatedAt()}</p>
              </div>
              <div>
                <p className="text-muted-foreground">{t('Updated')}</p>
                <p>{milestone.getUpdatedAt()}</p>
              </div>
              {milestone.completedAt && (
                <div>
                  <p className="text-muted-foreground">{t('Completed')}</p>
                  <p>{milestone.getCompletedAt()}</p>
                </div>
              )}
              {milestone.releasedAt && (
                <div>
                  <p className="text-muted-foreground">{t('Released')}</p>
                  <p>{milestone.getReleasedAt()}</p>
                </div>
              )}
            </div>
          </>
        )}

        {/* Actions */}
        <MilestoneActions
          milestone={milestone}
          userRole={userRole}
          onAction={onAction}
          isLoading={isLoading}
        />
      </CardContent>
    </Card>
  );
}

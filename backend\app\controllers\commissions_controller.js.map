{"version": 3, "file": "commissions_controller.js", "sourceRoot": "", "sources": ["../../../app/controllers/commissions_controller.ts"], "names": [], "mappings": "AAAA,OAAO,YAAY,MAAM,2BAA2B,CAAC;AACrD,OAAO,KAAK,MAAM,eAAe,CAAC;AAClC,OAAO,UAAU,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,kBAAkB,EAAE,MAAM,wBAAwB,CAAC;AAE5D,OAAO,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AACjC,OAAO,EAAE,MAAM,IAAI,cAAc,EAAE,MAAM,UAAU,CAAC;AACpD,OAAO,eAAe,MAAM,yBAAyB,CAAC;AACtD,OAAO,eAAe,MAAM,4BAA4B,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAEtD,MAAM,CAAC,OAAO,OAAO,qBAAqB;IACxC,KAAK,CAAC,KAAK,CAAC,GAAgB;QAC1B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QACxC,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC,EAAE,EAAE,CAAC;YAC/C,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEvC,IAAI,IAAI,CAAC,IAAK,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,IAAK,CAAC,EAAE,KAAK,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzE,OAAO,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YACpF,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;YAC5D,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YAClF,CAAC;YAED,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC;iBACvC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC;iBAC1B,OAAO,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,EAAE;oBAC/B,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;iBACD,OAAO,CAAC,aAAa,CAAC;iBACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACzB,MAAM,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;YAC5F,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,GAAgB;QAC9B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QACxC,IAAI,CAAC;YACH,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC,EAAE,EAAE,CAAC;YACpD,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEvC,IAAI,IAAI,CAAC,IAAK,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,IAAK,CAAC,EAAE,KAAK,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzE,OAAO,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YACpF,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;YAC5D,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YAClF,CAAC;YACD,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC;iBACvC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC;iBAC1B,OAAO,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,EAAE;oBAC/B,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;iBACD,OAAO,CAAC,aAAa,CAAC;iBACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAEzB,MAAM,iBAAiB,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YACnF,MAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAE7E,IAAI,iBAAiB,EAAE,CAAC;gBACtB,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;YACxD,CAAC;YAED,IAAI,eAAe,EAAE,CAAC;gBACpB,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;YACtD,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;YAEpC,MAAM,MAAM,GAAG;gBACb;oBACE,KAAK,EAAE,MAAM;oBACb,KAAK,EAAE,CAAC,GAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,qBAAqB,CAAC;iBACrF;gBACD,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;gBACpC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;gBACpC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,mBAAmB,EAAE;aAChD,CAAC;YAEF,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YACtD,MAAM,GAAG,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEvC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;YAC5C,QAAQ,CAAC,MAAM,CAAC,qBAAqB,EAAE,sCAAsC,CAAC,CAAC;YAE/E,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,GAAgB;QAC/B,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QAClC,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC,EAAE,EAAE,CAAC;YAC/C,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC;iBACvC,OAAO,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,EAAE;oBAC/B,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oBAC3B,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oBAC3B,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC1B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;iBACD,OAAO,CAAC,aAAa,CAAC;iBACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACzB,MAAM,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;YAC5F,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,mBAAmB,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,GAAgB;QAC5B,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QAClC,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,IAAI,GAAG,MAAM,UAAU,CAAC,KAAK,EAAE;iBAClC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;iBACf,OAAO,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,EAAE;oBAC/B,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;iBACD,OAAO,CAAC,aAAa,CAAC;iBACtB,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAC1E,CAAC;YACD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,iBAAiB,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,GAAgB;QACrC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QACxC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEvC,IAAI,IAAI,CAAC,IAAK,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,IAAK,CAAC,EAAE,KAAK,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzE,OAAO,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YACpF,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;YAC5D,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YAClF,CAAC;YAED,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,KAAK,EAAE;iBACzC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC;iBAC3B,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC;iBAC7B,GAAG,CAAC,iBAAiB,CAAC;iBACtB,KAAK,EAAE,CAAC;YACX,MAAM,QAAQ,GAAG,MAAM,eAAe,EAAE,CAAC;YACzC,MAAM,KAAK,GAAG;gBACZ,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnE,QAAQ,EAAE,QAAQ,CAAC,eAAe;aACnC,CAAC;YAEF,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,wBAAwB,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,GAAgB;QACrC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QAClC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;YAC5D,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YAClF,CAAC;YAED,MAAM,qBAAqB,GAAG,MAAM,UAAU,CAAC,KAAK,EAAE;iBACnD,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC;iBAC1B,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC;iBAC7B,GAAG,CAAC,iBAAiB,CAAC;iBACtB,KAAK,EAAE,CAAC;YACX,MAAM,QAAQ,GAAG,MAAM,eAAe,EAAE,CAAC;YACzC,MAAM,UAAU,CACd,qBAAqB,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAC/C,QAAQ,CAAC,eAAgB,EACzB,KAAK,CAAC,MAAM,CACb,CAAC;YAEF,MAAM,YAAY,GAAiB,MAAM,UAAU,CAAC,KAAK,EAAE;iBACxD,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC;iBAC1B,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC;iBAC7B,MAAM,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;YAEnC,IAAI,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxC,OAAO,QAAQ,CAAC,UAAU,CAAC;oBACzB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,8BAA8B;iBACxC,CAAC,CAAC;YACL,CAAC;YAED,OAAO,QAAQ,CAAC,EAAE,CAAC;gBACjB,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,0CAA0C;aACpD,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,6BAA6B,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,GAAgB;QACjC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QAClC,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;YACnE,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;YAEpE,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAC1E,CAAC;YAED,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;YAC3B,MAAM,UAAU,CAAC,IAAI,EAAE,CAAC;YAExB,OAAO,QAAQ,CAAC,EAAE,CAAC;gBACjB,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,6BAA6B;aACvC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,GAAgB;QAC3B,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QAClC,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;YAEpE,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAC1E,CAAC;YAED,MAAM,UAAU,CAAC,MAAM,EAAE,CAAC;YAE1B,OAAO,QAAQ,CAAC,EAAE,CAAC;gBACjB,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,iCAAiC;aAC3C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,gBAAgB,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;CACF"}
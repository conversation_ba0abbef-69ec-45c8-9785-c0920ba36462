import { DateTime } from 'luxon';
import { BaseModel, column, belongsTo, computed } from '@adonisjs/lucid/orm';
import Escrow from './escrow.js';
import User from './user.js';

export default class EscrowMilestone extends BaseModel {
  @column({ isPrimary: true })
  id;

  @column()
  escrowId;

  @column()
  title;

  @column()
  description;

  @column()
  orderIndex;

  @column()
  amount;

  @column()
  percentage;

  @column()
  status;

  @column()
  senderApproved;

  @column()
  recipientApproved;

  @column.dateTime()
  senderApprovedAt;

  @column.dateTime()
  recipientApprovedAt;

  @column.dateTime()
  releasedAt;

  @column()
  releasedByUserId;

  @column()
  deliverables;

  @column()
  evidence;

  @column()
  completionNotes;

  @column()
  rejectionReason;

  @column.dateTime()
  dueDate;

  @column.dateTime()
  completedAt;

  @column.dateTime({ autoCreate: true })
  createdAt;

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  updatedAt;

  // Relationships
  @belongsTo(() => Escrow)
  escrow;

  @belongsTo(() => User, { foreignKey: 'releasedByUserId' })
  releasedBy;

  // Computed properties
  @computed()
  get deliverablesParsed() {
    return typeof this.deliverables !== 'object' 
      ? JSON.parse(this?.deliverables || '[]') 
      : this?.deliverables;
  }

  @computed()
  get evidenceParsed() {
    return typeof this.evidence !== 'object' 
      ? JSON.parse(this?.evidence || '[]') 
      : this?.evidence;
  }

  @computed()
  get isOverdue() {
    if (!this.dueDate || this.status === 'completed' || this.status === 'released') {
      return false;
    }
    return DateTime.now() > DateTime.fromJSDate(this.dueDate);
  }

  @computed()
  get canBeApproved() {
    return this.status === 'completed' && !this.senderApproved && !this.recipientApproved;
  }

  @computed()
  get canBeReleased() {
    return this.status === 'approved' || (this.senderApproved && this.recipientApproved);
  }

  @computed()
  get timeRemaining() {
    if (!this.dueDate) return null;
    const now = DateTime.now();
    const due = DateTime.fromJSDate(this.dueDate);
    return due.diff(now, ['days', 'hours', 'minutes']).toObject();
  }

  // Instance methods
  async markCompleted(completionNotes = null, evidence = null) {
    this.status = 'completed';
    this.completedAt = DateTime.now();
    if (completionNotes) this.completionNotes = completionNotes;
    if (evidence) this.evidence = JSON.stringify(evidence);
    await this.save();
  }

  async approveBySender(userId) {
    this.senderApproved = true;
    this.senderApprovedAt = DateTime.now();
    
    // Check if both parties have approved
    if (this.recipientApproved) {
      this.status = 'approved';
    }
    
    await this.save();
  }

  async approveByRecipient(userId) {
    this.recipientApproved = true;
    this.recipientApprovedAt = DateTime.now();
    
    // Check if both parties have approved
    if (this.senderApproved) {
      this.status = 'approved';
    }
    
    await this.save();
  }

  async reject(rejectionReason, userId) {
    this.status = 'rejected';
    this.rejectionReason = rejectionReason;
    await this.save();
  }

  async release(releasedByUserId) {
    this.status = 'released';
    this.releasedAt = DateTime.now();
    this.releasedByUserId = releasedByUserId;
    await this.save();
    
    // Update parent escrow milestone progress
    const escrow = await this.related('escrow').query().first();
    if (escrow) {
      await escrow.updateMilestoneProgress();
    }
  }

  async reset() {
    this.status = 'pending';
    this.senderApproved = false;
    this.recipientApproved = false;
    this.senderApprovedAt = null;
    this.recipientApprovedAt = null;
    this.completedAt = null;
    this.releasedAt = null;
    this.releasedByUserId = null;
    this.completionNotes = null;
    this.rejectionReason = null;
    this.evidence = null;
    await this.save();
  }
}

import { DateTime } from 'luxon';
import { BaseModel, column, belongsTo, hasMany, beforeCreate, computed } from '@adonisjs/lucid/orm';
import { generateNanoId } from '../utils/generate_unique_id.js';
import User from './user.js';
import PoolContribution from './pool_contribution.js';

export default class FundraisingPool extends BaseModel {
  @column({ isPrimary: true })
  id;

  @column()
  poolId;

  @column()
  creatorId;

  @column()
  title;

  @column()
  description;

  @column()
  category;

  @column()
  tags;

  @column()
  targetAmount;

  @column()
  currentAmount;

  @column()
  currencyCode;

  @column()
  minimumContribution;

  @column()
  maximumContribution;

  @column.dateTime()
  startDate;

  @column.dateTime()
  endDate;

  @column()
  hasDeadline;

  @column()
  status;

  @column()
  visibility;

  @column()
  allowAnonymous;

  @column()
  contributorsCount;

  @column()
  progressPercentage;

  @column.dateTime()
  targetReachedAt;

  @column()
  autoDistribute;

  @column()
  distributionType;

  @column.dateTime()
  distributedAt;

  @column()
  fundsDistributed;

  @column()
  coverImage;

  @column()
  gallery;

  @column()
  videoUrl;

  @column()
  allowComments;

  @column()
  showContributors;

  @column()
  sendUpdates;

  @column()
  platformFeePercentage;

  @column()
  platformFeeAmount;

  @column()
  metadata;

  @column()
  settings;

  @column.dateTime({ autoCreate: true })
  createdAt;

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  updatedAt;

  // Relationships
  @belongsTo(() => User, { foreignKey: 'creatorId' })
  creator;

  @hasMany(() => PoolContribution)
  contributions;

  // Computed properties
  @computed()
  get tagsParsed() {
    return typeof this.tags !== 'object' 
      ? JSON.parse(this?.tags || '[]') 
      : this?.tags;
  }

  @computed()
  get galleryParsed() {
    return typeof this.gallery !== 'object' 
      ? JSON.parse(this?.gallery || '[]') 
      : this?.gallery;
  }

  @computed()
  get metadataParsed() {
    return typeof this.metadata !== 'object' 
      ? JSON.parse(this?.metadata || '{}') 
      : this?.metadata;
  }

  @computed()
  get settingsParsed() {
    return typeof this.settings !== 'object' 
      ? JSON.parse(this?.settings || '{}') 
      : this?.settings;
  }

  @computed()
  get isActive() {
    return this.status === 'active';
  }

  @computed()
  get isExpired() {
    if (!this.hasDeadline || !this.endDate) return false;
    return DateTime.now() > DateTime.fromJSDate(this.endDate);
  }

  @computed()
  get isTargetReached() {
    return this.currentAmount >= this.targetAmount;
  }

  @computed()
  get remainingAmount() {
    return Math.max(0, this.targetAmount - this.currentAmount);
  }

  @computed()
  get timeRemaining() {
    if (!this.hasDeadline || !this.endDate) return null;
    const now = DateTime.now();
    const end = DateTime.fromJSDate(this.endDate);
    return end.diff(now, ['days', 'hours', 'minutes']).toObject();
  }

  @computed()
  get daysRemaining() {
    const timeRemaining = this.timeRemaining;
    return timeRemaining ? Math.ceil(timeRemaining.days || 0) : null;
  }

  @computed()
  get canReceiveContributions() {
    if (this.status !== 'active') return false;
    if (this.isExpired) return false;
    if (this.isTargetReached && this.distributionType === 'immediate') return false;
    return true;
  }

  @computed()
  get shouldAutoDistribute() {
    return this.autoDistribute && 
           this.isTargetReached && 
           !this.fundsDistributed &&
           this.distributionType !== 'manual';
  }

  // Hooks
  @beforeCreate()
  static assignPoolId(pool) {
    pool.poolId = generateNanoId(16);
  }

  // Instance methods
  async updateProgress() {
    // Recalculate current amount and contributors count
    const contributions = await this.related('contributions')
      .query()
      .where('status', 'completed');
    
    this.currentAmount = contributions.reduce((sum, contrib) => sum + contrib.netAmount, 0);
    this.contributorsCount = contributions.length;
    this.progressPercentage = Math.min(100, Math.round((this.currentAmount / this.targetAmount) * 100));
    
    // Check if target reached for the first time
    if (this.isTargetReached && !this.targetReachedAt) {
      this.targetReachedAt = DateTime.now();
    }
    
    await this.save();
  }

  async addContribution(contributionData) {
    const contribution = await this.related('contributions').create(contributionData);
    await this.updateProgress();
    return contribution;
  }

  async activate() {
    this.status = 'active';
    if (!this.startDate) {
      this.startDate = DateTime.now();
    }
    await this.save();
  }

  async pause() {
    this.status = 'paused';
    await this.save();
  }

  async complete() {
    this.status = 'completed';
    await this.save();
  }

  async cancel() {
    this.status = 'cancelled';
    await this.save();
  }

  async expire() {
    this.status = 'expired';
    await this.save();
  }

  async distributeFunds() {
    if (this.fundsDistributed) {
      throw new Error('Funds have already been distributed');
    }

    // Mark as distributed
    this.fundsDistributed = true;
    this.distributedAt = DateTime.now();
    
    // If target reached, complete the pool
    if (this.isTargetReached) {
      this.status = 'completed';
    }
    
    await this.save();
  }

  async calculatePlatformFee() {
    if (this.platformFeePercentage > 0) {
      this.platformFeeAmount = (this.currentAmount * this.platformFeePercentage) / 100;
    }
    await this.save();
  }

  async getTopContributors(limit = 10) {
    return await this.related('contributions')
      .query()
      .where('status', 'completed')
      .where('show_amount', true)
      .orderBy('net_amount', 'desc')
      .limit(limit)
      .preload('contributor');
  }

  async getRecentContributions(limit = 20) {
    return await this.related('contributions')
      .query()
      .where('status', 'completed')
      .orderBy('created_at', 'desc')
      .limit(limit)
      .preload('contributor');
  }
}

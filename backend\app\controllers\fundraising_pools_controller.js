import errorHandler from '#exceptions/error_handler';
import FundraisingPool from '#models/fundraising_pool';
import PoolContribution from '#models/pool_contribution';
import fundraisingPoolService from '#services/fundraising_pool_service';
import FundraisingPoolValidator from '#utils/fundraising_pool_validator';
import { 
  createPoolSchema, 
  updatePoolSchema, 
  contributeToPoolSchema 
} from '#validators/fundraising_pool';
import exportService from '#services/export_service';

export default class FundraisingPoolsController {
  /**
   * Get fundraising pools (public and user's own)
   */
  async index(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const { page = 1, limit = 20, ...criteria } = request.qs();
      
      // Validate search criteria
      FundraisingPoolValidator.validateSearchCriteria({ page, limit, ...criteria });
      
      const result = await fundraisingPoolService.searchPools({
        page: parseInt(page),
        limit: parseInt(limit),
        ...criteria
      });
      
      return response.json(result);
    } catch (error) {
      errorHandler(error, ctx, 'Fundraising pools index error');
    }
  }

  /**
   * Get user's own fundraising pools
   */
  async myPools(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const { page, limit, status, ...filters } = request.qs();
      
      let query = FundraisingPool.query()
        .where('creator_id', auth.user.id)
        .preload('creator', (query) => query.preload('customer'))
        .orderBy('created_at', 'desc');

      // Apply filters
      if (status) {
        query = query.where('status', status);
      }

      if (filters.category) {
        query = query.where('category', filters.category);
      }

      // Apply date filters
      if (filters.startDate) {
        query = query.where('created_at', '>=', filters.startDate);
      }
      
      if (filters.endDate) {
        query = query.where('created_at', '<=', filters.endDate);
      }

      const data = page && limit ? await query.paginate(page, limit) : await query.exec();
      
      return response.json(data);
    } catch (error) {
      errorHandler(error, ctx, 'My pools error');
    }
  }

  /**
   * Get fundraising pool by ID
   */
  async show(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const poolId = request.param('id');
      
      const pool = await FundraisingPool.query()
        .where('id', poolId)
        .preload('creator', (query) => query.preload('customer'))
        .preload('contributions', (query) => {
          query
            .where('status', 'completed')
            .where('show_amount', true)
            .orderBy('created_at', 'desc')
            .limit(10)
            .preload('contributor');
        })
        .first();

      if (!pool) {
        return response.notFound({ success: false, message: 'Fundraising pool not found' });
      }

      // Check if user has permission to view this pool
      if (pool.visibility === 'private' && pool.creatorId !== auth.user?.id) {
        return response.forbidden({ success: false, message: 'Access denied' });
      }

      return response.json(pool);
    } catch (error) {
      errorHandler(error, ctx, 'Fundraising pool show error');
    }
  }

  /**
   * Create new fundraising pool
   */
  async store(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const data = await request.validateUsing(createPoolSchema);
      
      // Add creator ID from authenticated user
      data.creatorId = auth.user.id;
      
      // Validate pool creation data
      FundraisingPoolValidator.validatePoolCreation(data);
      
      // Validate category and tags
      if (data.category) {
        FundraisingPoolValidator.validateCategory(data.category);
      }
      
      if (data.tags) {
        FundraisingPoolValidator.validateTags(data.tags);
      }
      
      const pool = await fundraisingPoolService.createPool(data);
      
      return response.created({
        success: true,
        message: 'Fundraising pool created successfully',
        data: pool
      });
    } catch (error) {
      errorHandler(error, ctx, 'Fundraising pool creation error');
    }
  }

  /**
   * Update fundraising pool
   */
  async update(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const poolId = request.param('id');
      const updateData = await request.validateUsing(updatePoolSchema);
      
      const pool = await FundraisingPool.query()
        .where('id', poolId)
        .where('creator_id', auth.user.id)
        .first();

      if (!pool) {
        return response.notFound({ success: false, message: 'Fundraising pool not found' });
      }

      // Validate pool update
      FundraisingPoolValidator.validatePoolUpdate(updateData, pool);
      
      // Update pool
      pool.merge(updateData);
      await pool.save();
      
      return response.json({
        success: true,
        message: 'Fundraising pool updated successfully',
        data: pool
      });
    } catch (error) {
      errorHandler(error, ctx, 'Fundraising pool update error');
    }
  }

  /**
   * Activate fundraising pool
   */
  async activate(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const poolId = request.param('id');
      
      const pool = await fundraisingPoolService.activatePool(poolId, auth.user.id);
      
      return response.json({
        success: true,
        message: 'Fundraising pool activated successfully',
        data: pool
      });
    } catch (error) {
      errorHandler(error, ctx, 'Fundraising pool activation error');
    }
  }

  /**
   * Contribute to fundraising pool
   */
  async contribute(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const poolId = request.param('id');
      const contributionData = await request.validateUsing(contributeToPoolSchema);
      
      // Add contributor ID if user is authenticated
      if (auth.user) {
        contributionData.contributorId = auth.user.id;
      }
      
      const pool = await FundraisingPool.find(poolId);
      if (!pool) {
        return response.notFound({ success: false, message: 'Fundraising pool not found' });
      }

      // Validate contribution
      FundraisingPoolValidator.validateContribution(contributionData, pool);
      FundraisingPoolValidator.validatePoolForContributions(pool);
      
      const contribution = await fundraisingPoolService.contributeToPool(poolId, contributionData);
      
      return response.created({
        success: true,
        message: 'Contribution made successfully',
        data: contribution
      });
    } catch (error) {
      errorHandler(error, ctx, 'Pool contribution error');
    }
  }

  /**
   * Distribute pool funds
   */
  async distributeFunds(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const poolId = request.param('id');
      
      const pool = await FundraisingPool.query()
        .where('id', poolId)
        .where('creator_id', auth.user.id)
        .first();

      if (!pool) {
        return response.notFound({ success: false, message: 'Fundraising pool not found' });
      }

      // Validate distribution conditions
      FundraisingPoolValidator.validateDistributionConditions(pool);
      
      const result = await fundraisingPoolService.distributeFunds(poolId, auth.user.id);
      
      return response.json({
        success: true,
        message: 'Funds distributed successfully',
        data: result
      });
    } catch (error) {
      errorHandler(error, ctx, 'Fund distribution error');
    }
  }

  /**
   * Get pool statistics
   */
  async statistics(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const poolId = request.param('id');
      
      const pool = await FundraisingPool.query()
        .where('id', poolId)
        .where((builder) => {
          builder.where('creator_id', auth.user.id).orWhere('visibility', 'public');
        })
        .first();

      if (!pool) {
        return response.notFound({ success: false, message: 'Fundraising pool not found' });
      }

      const stats = await fundraisingPoolService.getPoolStatistics(poolId);
      
      return response.json(stats);
    } catch (error) {
      errorHandler(error, ctx, 'Pool statistics error');
    }
  }

  /**
   * Get pool contributions
   */
  async contributions(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const poolId = request.param('id');
      const { page = 1, limit = 20, includeAnonymous = true } = request.qs();
      
      const pool = await FundraisingPool.query()
        .where('id', poolId)
        .where((builder) => {
          builder.where('creator_id', auth.user.id).orWhere('visibility', 'public');
        })
        .first();

      if (!pool) {
        return response.notFound({ success: false, message: 'Fundraising pool not found' });
      }

      let query = PoolContribution.query()
        .where('pool_id', poolId)
        .where('status', 'completed')
        .preload('contributor')
        .orderBy('created_at', 'desc');

      if (!includeAnonymous) {
        query = query.where('is_anonymous', false);
      }

      const contributions = await query.paginate(page, limit);
      
      return response.json(contributions);
    } catch (error) {
      errorHandler(error, ctx, 'Pool contributions error');
    }
  }

  /**
   * Get user's contributions
   */
  async myContributions(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const { page, limit, status } = request.qs();
      
      let query = PoolContribution.query()
        .where('contributor_id', auth.user.id)
        .preload('pool', (query) => query.preload('creator'))
        .orderBy('created_at', 'desc');

      if (status) {
        query = query.where('status', status);
      }

      const data = page && limit ? await query.paginate(page, limit) : await query.exec();
      
      return response.json(data);
    } catch (error) {
      errorHandler(error, ctx, 'My contributions error');
    }
  }

  /**
   * Get fundraising categories
   */
  async categories(ctx) {
    const { response } = ctx;
    
    try {
      const categories = [
        'charity',
        'education',
        'health',
        'environment',
        'technology',
        'arts',
        'sports',
        'community',
        'business',
        'personal',
        'emergency',
        'other'
      ];
      
      return response.json(categories);
    } catch (error) {
      errorHandler(error, ctx, 'Categories error');
    }
  }

  /**
   * Search pools by query
   */
  async search(ctx) {
    const { request, response } = ctx;
    
    try {
      const { q, category, page = 1, limit = 20, sortBy = 'created_at', sortOrder = 'desc' } = request.qs();
      
      const criteria = {
        query: q,
        category,
        page: parseInt(page),
        limit: parseInt(limit),
        sortBy,
        sortOrder,
        status: 'active',
        visibility: 'public'
      };
      
      const result = await fundraisingPoolService.searchPools(criteria);
      
      return response.json(result);
    } catch (error) {
      errorHandler(error, ctx, 'Pool search error');
    }
  }

  /**
   * Export pools to CSV
   */
  async exportCsv(ctx) {
    try {
      await exportService.exportData(ctx, 'fundraising_pools');
    } catch (error) {
      errorHandler(error, ctx, 'Fundraising pools export error');
    }
  }

  /**
   * Get pool by pool_id (public identifier)
   */
  async getByPoolId(ctx) {
    const { request, response } = ctx;
    
    try {
      const poolId = request.param('poolId');
      
      const pool = await FundraisingPool.query()
        .where('pool_id', poolId)
        .where('visibility', 'public')
        .preload('creator', (query) => query.preload('customer'))
        .first();

      if (!pool) {
        return response.notFound({ success: false, message: 'Fundraising pool not found' });
      }

      return response.json(pool);
    } catch (error) {
      errorHandler(error, ctx, 'Pool getByPoolId error');
    }
  }
}

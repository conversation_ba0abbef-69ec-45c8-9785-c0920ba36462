import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { jest } from '@jest/globals';
import userEvent from '@testing-library/user-event';
import SelectRecipientCombo from '@/components/common/form/SelectRecipientCombo';
import { CreateEscrowForm } from '@/components/escrow/CreateEscrowForm';
import { CreateRecurringTransferForm } from '@/components/recurring-transfer/CreateRecurringTransferForm';
import { CreateFundraisingPoolForm } from '@/components/fundraising-pool/CreateFundraisingPoolForm';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { IContact } from '@/types/contact';

// Mock data
const mockEnhancedContact: IContact = {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  avatar: 'https://example.com/avatar.jpg',
  status: 'active',
  isVerified: true,
  verificationLevel: 'enhanced',
  lastTransactionAt: new Date(),
  totalTransactions: 5,
  totalVolume: 1000,
  riskScore: 25,
  tags: ['trusted', 'frequent'],
  capabilities: {
    canReceiveEscrow: true,
    canReceiveRecurringTransfer: true,
    canReceiveFundraisingContribution: true,
  },
  relationshipType: 'frequent',
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockLegacyContact = {
  id: 1,
  contactId: 1,
  userId: 2,
  contact: {
    customer: {
      id: 1,
      name: 'Jane Smith',
      email: '<EMAIL>',
      profileImage: 'https://example.com/jane.jpg',
    },
    email: '<EMAIL>',
  },
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Mock API responses
const mockEnhancedResponse = {
  success: true,
  data: {
    contacts: [mockEnhancedContact],
    recentContacts: [mockEnhancedContact],
    frequentContacts: [mockEnhancedContact],
    suggestions: [],
  },
};

const mockLegacyResponse = {
  data: [mockLegacyContact],
};

// Mock APIs
const mockContactApi = {
  searchContacts: jest.fn().mockResolvedValue(mockEnhancedResponse),
  getContacts: jest.fn().mockResolvedValue({ success: true, data: mockLegacyResponse }),
};

const mockWebSocket = {
  subscribe: jest.fn().mockReturnValue(() => {}),
  unsubscribe: jest.fn(),
  isConnected: jest.fn().mockReturnValue(true),
  getConnectionState: jest.fn().mockReturnValue('connected'),
  disconnect: jest.fn(),
};

// Mock hooks
jest.mock('@/lib/contact-api', () => ({
  contactApi: mockContactApi,
}));

jest.mock('@/hooks/useWebSocket', () => ({
  useWebSocket: () => mockWebSocket,
}));

jest.mock('@/hooks/useSWR', () => ({
  useSWR: (url: string) => {
    if (url.includes('/contacts')) {
      return {
        data: mockLegacyResponse,
        isLoading: false,
        error: null,
      };
    }
    return { data: null, isLoading: false, error: null };
  },
}));

describe('Comprehensive Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('1. Component Integration Verification', () => {
    describe('SelectRecipientCombo Backward Compatibility', () => {
      it('should work with legacy props interface', async () => {
        const mockOnChange = jest.fn();
        const mockSetUser = jest.fn();

        render(
          <SelectRecipientCombo
            user={null}
            onChange={mockOnChange}
            setUser={mockSetUser}
          />
        );

        expect(screen.getByText('Enter recipient account')).toBeInTheDocument();
        
        // Click to open dropdown
        fireEvent.click(screen.getByRole('button'));
        
        await waitFor(() => {
          expect(screen.getByText('Jane Smith')).toBeInTheDocument();
        });

        // Select contact
        fireEvent.click(screen.getByText('Jane Smith'));

        expect(mockSetUser).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'Jane Smith',
            email: '<EMAIL>',
          })
        );
      });

      it('should work with enhanced props', async () => {
        const mockOnChange = jest.fn();
        const mockSetUser = jest.fn();
        const mockOnContactSelect = jest.fn();

        render(
          <SelectRecipientCombo
            user={null}
            onChange={mockOnChange}
            setUser={mockSetUser}
            context="escrow"
            showVerificationBadge={true}
            showContactStatus={true}
            onContactSelect={mockOnContactSelect}
            filterBy={{
              canReceiveEscrow: true,
              verificationLevel: 'enhanced',
            }}
          />
        );

        // Should call enhanced API with filters
        await waitFor(() => {
          expect(mockContactApi.searchContacts).toHaveBeenCalledWith(
            expect.objectContaining({
              canReceiveEscrow: true,
              verificationLevel: 'enhanced',
            })
          );
        });
      });
    });

    describe('Type Definition Compatibility', () => {
      it('should handle both enhanced and legacy contact formats', () => {
        const mockSetUser = jest.fn();

        const { rerender } = render(
          <SelectRecipientCombo
            user={mockEnhancedContact}
            onChange={jest.fn()}
            setUser={mockSetUser}
          />
        );

        expect(screen.getByText('John Doe')).toBeInTheDocument();

        // Test with legacy format
        rerender(
          <SelectRecipientCombo
            user={{
              id: 1,
              name: 'Legacy User',
              email: '<EMAIL>',
            }}
            onChange={jest.fn()}
            setUser={mockSetUser}
          />
        );

        expect(screen.getByText('Legacy User')).toBeInTheDocument();
      });
    });

    describe('WebSocket Integration', () => {
      it('should subscribe to real-time updates when enabled', () => {
        render(
          <SelectRecipientCombo
            user={null}
            onChange={jest.fn()}
            setUser={jest.fn()}
            enableRealTimeUpdates={true}
          />
        );

        expect(mockWebSocket.subscribe).toHaveBeenCalledWith(
          'contact:status_changed',
          expect.any(Function)
        );
        expect(mockWebSocket.subscribe).toHaveBeenCalledWith(
          'contact:verification_updated',
          expect.any(Function)
        );
      });

      it('should not subscribe when real-time updates disabled', () => {
        render(
          <SelectRecipientCombo
            user={null}
            onChange={jest.fn()}
            setUser={jest.fn()}
            enableRealTimeUpdates={false}
          />
        );

        expect(mockWebSocket.subscribe).not.toHaveBeenCalled();
      });
    });
  });

  describe('2. Cross-Component Compatibility', () => {
    describe('CreateEscrowForm Integration', () => {
      it('should integrate seamlessly with escrow form', async () => {
        const mockOnSubmit = jest.fn();

        render(
          <CreateEscrowForm onSubmit={mockOnSubmit}>
            <SelectRecipientCombo
              user={null}
              onChange={jest.fn()}
              setUser={jest.fn()}
              context="escrow"
              filterBy={{ canReceiveEscrow: true }}
            />
          </CreateEscrowForm>
        );

        // Should filter for escrow-capable contacts
        await waitFor(() => {
          expect(mockContactApi.searchContacts).toHaveBeenCalledWith(
            expect.objectContaining({
              canReceiveEscrow: true,
            })
          );
        });
      });
    });

    describe('CreateRecurringTransferForm Integration', () => {
      it('should integrate with recurring transfer form', async () => {
        render(
          <CreateRecurringTransferForm onSubmit={jest.fn()}>
            <SelectRecipientCombo
              user={null}
              onChange={jest.fn()}
              setUser={jest.fn()}
              context="recurring-transfer"
              showFrequentContacts={true}
            />
          </CreateRecurringTransferForm>
        );

        await waitFor(() => {
          expect(mockContactApi.searchContacts).toHaveBeenCalledWith(
            expect.objectContaining({
              canReceiveRecurringTransfer: true,
            })
          );
        });
      });
    });

    describe('CreateFundraisingPoolForm Integration', () => {
      it('should integrate with fundraising pool form', async () => {
        render(
          <CreateFundraisingPoolForm onSubmit={jest.fn()}>
            <SelectRecipientCombo
              user={null}
              onChange={jest.fn()}
              setUser={jest.fn()}
              context="fundraising-pool"
              allowNewContact={true}
            />
          </CreateFundraisingPoolForm>
        );

        await waitFor(() => {
          expect(mockContactApi.searchContacts).toHaveBeenCalledWith(
            expect.objectContaining({
              canReceiveFundraisingContribution: true,
            })
          );
        });
      });
    });

    describe('Context-Aware Filtering', () => {
      it('should apply correct filters for each context', async () => {
        const contexts = [
          { context: 'escrow', expectedFilter: { canReceiveEscrow: true } },
          { context: 'recurring-transfer', expectedFilter: { canReceiveRecurringTransfer: true } },
          { context: 'fundraising-pool', expectedFilter: { canReceiveFundraisingContribution: true } },
        ];

        for (const { context, expectedFilter } of contexts) {
          const { unmount } = render(
            <SelectRecipientCombo
              user={null}
              onChange={jest.fn()}
              setUser={jest.fn()}
              context={context as any}
            />
          );

          await waitFor(() => {
            expect(mockContactApi.searchContacts).toHaveBeenCalledWith(
              expect.objectContaining(expectedFilter)
            );
          });

          unmount();
          jest.clearAllMocks();
        }
      });
    });
  });

  describe('3. Error Handling and Fallbacks', () => {
    describe('API Fallback Mechanism', () => {
      it('should fallback to legacy API when enhanced API fails', async () => {
        // Mock enhanced API failure
        mockContactApi.searchContacts.mockRejectedValueOnce(new Error('Enhanced API unavailable'));

        render(
          <SelectRecipientCombo
            user={null}
            onChange={jest.fn()}
            setUser={jest.fn()}
          />
        );

        // Should still show legacy contacts
        await waitFor(() => {
          expect(screen.getByText('Jane Smith')).toBeInTheDocument();
        });
      });
    });

    describe('Error Boundary Integration', () => {
      it('should catch and handle component errors', () => {
        const ThrowError = () => {
          throw new Error('Test error');
        };

        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

        render(
          <ErrorBoundary>
            <ThrowError />
          </ErrorBoundary>
        );

        expect(screen.getByText('Something went wrong')).toBeInTheDocument();
        expect(screen.getByText('Test error')).toBeInTheDocument();

        consoleSpy.mockRestore();
      });

      it('should allow retry after error', async () => {
        let shouldThrow = true;
        const ConditionalError = () => {
          if (shouldThrow) {
            throw new Error('Conditional error');
          }
          return <div>Success</div>;
        };

        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

        render(
          <ErrorBoundary>
            <ConditionalError />
          </ErrorBoundary>
        );

        expect(screen.getByText('Something went wrong')).toBeInTheDocument();

        // Click retry
        shouldThrow = false;
        fireEvent.click(screen.getByText('Retry'));

        await waitFor(() => {
          expect(screen.getByText('Success')).toBeInTheDocument();
        });

        consoleSpy.mockRestore();
      });
    });

    describe('Loading State Management', () => {
      it('should coordinate multiple loading states', () => {
        const { rerender } = render(
          <SelectRecipientCombo
            user={null}
            onChange={jest.fn()}
            setUser={jest.fn()}
          />
        );

        // Should show loading when any state is loading
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();

        // Mock loading state
        jest.mocked(mockContactApi.searchContacts).mockImplementation(
          () => new Promise(() => {}) // Never resolves
        );

        rerender(
          <SelectRecipientCombo
            user={null}
            onChange={jest.fn()}
            setUser={jest.fn()}
          />
        );

        // Should show unified loading state
        fireEvent.click(screen.getByRole('button'));
        // Loading state would be visible in the dropdown
      });
    });
  });

  describe('4. Real-time Features', () => {
    describe('Contact Status Updates', () => {
      it('should update contact status in real-time', async () => {
        let statusUpdateHandler: Function;
        mockWebSocket.subscribe.mockImplementation((event, handler) => {
          if (event === 'contact:status_changed') {
            statusUpdateHandler = handler;
          }
          return () => {};
        });

        render(
          <SelectRecipientCombo
            user={null}
            onChange={jest.fn()}
            setUser={jest.fn()}
            enableRealTimeUpdates={true}
          />
        );

        // Open dropdown to show contacts
        fireEvent.click(screen.getByRole('button'));

        await waitFor(() => {
          expect(screen.getByText('John Doe')).toBeInTheDocument();
        });

        // Simulate status update
        act(() => {
          statusUpdateHandler!({
            contactId: 1,
            status: 'blocked',
            timestamp: new Date(),
          });
        });

        // Should update the contact status in UI
        await waitFor(() => {
          expect(screen.getByText('blocked')).toBeInTheDocument();
        });
      });
    });

    describe('Verification Updates', () => {
      it('should update verification level in real-time', async () => {
        let verificationUpdateHandler: Function;
        mockWebSocket.subscribe.mockImplementation((event, handler) => {
          if (event === 'contact:verification_updated') {
            verificationUpdateHandler = handler;
          }
          return () => {};
        });

        render(
          <SelectRecipientCombo
            user={null}
            onChange={jest.fn()}
            setUser={jest.fn()}
            enableRealTimeUpdates={true}
            showVerificationBadge={true}
          />
        );

        // Simulate verification update
        act(() => {
          verificationUpdateHandler!({
            contactId: 1,
            verificationLevel: 'premium',
            isVerified: true,
            timestamp: new Date(),
          });
        });

        // Should update verification level
        // (This would be visible in the contact display)
      });
    });

    describe('Subscription Cleanup', () => {
      it('should clean up subscriptions on unmount', () => {
        const unsubscribeFn = jest.fn();
        mockWebSocket.subscribe.mockReturnValue(unsubscribeFn);

        const { unmount } = render(
          <SelectRecipientCombo
            user={null}
            onChange={jest.fn()}
            setUser={jest.fn()}
            enableRealTimeUpdates={true}
          />
        );

        unmount();

        // Should call unsubscribe functions
        expect(unsubscribeFn).toHaveBeenCalled();
      });
    });
  });

  describe('5. Performance and Optimization', () => {
    describe('Debounced Search', () => {
      it('should debounce search input', async () => {
        const user = userEvent.setup();

        render(
          <SelectRecipientCombo
            user={null}
            onChange={jest.fn()}
            setUser={jest.fn()}
          />
        );

        fireEvent.click(screen.getByRole('button'));
        
        const searchInput = screen.getByPlaceholderText('Search...');
        
        // Type rapidly
        await user.type(searchInput, 'john');

        // Should only call API once after debounce
        await waitFor(() => {
          expect(mockContactApi.searchContacts).toHaveBeenCalledTimes(1);
        }, { timeout: 500 });
      });
    });

    describe('Memory Management', () => {
      it('should not leak memory with multiple mounts/unmounts', () => {
        const initialSubscriptions = mockWebSocket.subscribe.mock.calls.length;

        for (let i = 0; i < 5; i++) {
          const { unmount } = render(
            <SelectRecipientCombo
              user={null}
              onChange={jest.fn()}
              setUser={jest.fn()}
              enableRealTimeUpdates={true}
            />
          );
          unmount();
        }

        // Should not accumulate subscriptions
        const finalSubscriptions = mockWebSocket.subscribe.mock.calls.length;
        expect(finalSubscriptions - initialSubscriptions).toBeLessThanOrEqual(10); // 2 per mount
      });
    });
  });
});

// Integration test for complete workflow
describe('End-to-End Workflow Integration', () => {
  it('should complete full escrow creation workflow', async () => {
    const mockOnSubmit = jest.fn();

    render(
      <ErrorBoundary>
        <CreateEscrowForm onSubmit={mockOnSubmit}>
          <SelectRecipientCombo
            user={null}
            onChange={jest.fn()}
            setUser={jest.fn()}
            context="escrow"
            showVerificationBadge={true}
            enableRealTimeUpdates={true}
          />
        </CreateEscrowForm>
      </ErrorBoundary>
    );

    // Select recipient
    fireEvent.click(screen.getByRole('button'));
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    fireEvent.click(screen.getByText('John Doe'));

    // Fill other form fields and submit
    // (This would involve filling amount, description, etc.)
    
    // Verify the workflow completed successfully
    expect(mockContactApi.searchContacts).toHaveBeenCalledWith(
      expect.objectContaining({
        canReceiveEscrow: true,
      })
    );
  });
});

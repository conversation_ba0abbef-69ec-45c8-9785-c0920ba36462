"use client";

import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { DatePicker } from "@/components/common/form/DatePicker";
import { CreateMilestoneData } from "@/types/escrow";
import { Plus, X } from "lucide-react";

interface MilestoneFormProps {
  milestone: CreateMilestoneData;
  onChange: (milestone: CreateMilestoneData) => void;
}

export function MilestoneForm({ milestone, onChange }: MilestoneFormProps) {
  const { t } = useTranslation();
  const [newDeliverable, setNewDeliverable] = useState("");

  const updateMilestone = (field: keyof CreateMilestoneData, value: any) => {
    onChange({
      ...milestone,
      [field]: value,
    });
  };

  const addDeliverable = () => {
    if (newDeliverable.trim()) {
      const deliverables = milestone.deliverables || [];
      updateMilestone('deliverables', [...deliverables, newDeliverable.trim()]);
      setNewDeliverable("");
    }
  };

  const removeDeliverable = (index: number) => {
    const deliverables = milestone.deliverables || [];
    updateMilestone('deliverables', deliverables.filter((_, i) => i !== index));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addDeliverable();
    }
  };

  return (
    <div className="space-y-4">
      {/* Title */}
      <div className="space-y-2">
        <Label htmlFor="title">{t('Title')} *</Label>
        <Input
          id="title"
          value={milestone.title}
          onChange={(e) => updateMilestone('title', e.target.value)}
          placeholder={t('Enter milestone title...')}
        />
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">{t('Description')}</Label>
        <Textarea
          id="description"
          value={milestone.description || ''}
          onChange={(e) => updateMilestone('description', e.target.value)}
          placeholder={t('Describe what needs to be completed...')}
          rows={3}
        />
      </div>

      {/* Percentage and Due Date */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="percentage">{t('Percentage of Total')} *</Label>
          <div className="relative">
            <Input
              id="percentage"
              type="number"
              min="0.01"
              max="100"
              step="0.01"
              value={milestone.percentage || ''}
              onChange={(e) => updateMilestone('percentage', parseFloat(e.target.value) || 0)}
              placeholder="0.00"
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
              %
            </span>
          </div>
        </div>

        <div className="space-y-2">
          <Label>{t('Due Date')}</Label>
          <DatePicker
            date={milestone.dueDate ? new Date(milestone.dueDate) : undefined}
            onDateChange={(date) => 
              updateMilestone('dueDate', date?.toISOString().split('T')[0])
            }
            placeholder={t('Select due date')}
          />
        </div>
      </div>

      {/* Deliverables */}
      <div className="space-y-2">
        <Label>{t('Deliverables')}</Label>
        
        {/* Existing Deliverables */}
        {milestone.deliverables && milestone.deliverables.length > 0 && (
          <div className="space-y-2">
            {milestone.deliverables.map((deliverable, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Badge variant="secondary" className="flex-1 justify-between">
                  <span className="truncate">{deliverable}</span>
                  <button
                    type="button"
                    onClick={() => removeDeliverable(index)}
                    className="ml-2 hover:bg-muted-foreground/20 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              </div>
            ))}
          </div>
        )}

        {/* Add New Deliverable */}
        <div className="flex items-center space-x-2">
          <Input
            value={newDeliverable}
            onChange={(e) => setNewDeliverable(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={t('Add a deliverable...')}
            className="flex-1"
          />
          <Button
            type="button"
            size="sm"
            onClick={addDeliverable}
            disabled={!newDeliverable.trim()}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        
        <p className="text-xs text-muted-foreground">
          {t('Press Enter or click + to add deliverables')}
        </p>
      </div>
    </div>
  );
}

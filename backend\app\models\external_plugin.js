var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
import { DateTime } from 'luxon';
import { BaseModel, column } from '@adonisjs/lucid/orm';
import { compose } from '@adonisjs/core/helpers';
import { Filterable } from 'adonis-lucid-filter';
import ExternalPluginFilter from './filters/external_plugin_filter.js';
export default class ExternalPlugin extends compose(BaseModel, Filterable) {
    static $filter = () => ExternalPluginFilter;
}
__decorate([
    column({ isPrimary: true }),
    __metadata("design:type", Number)
], ExternalPlugin.prototype, "id", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], ExternalPlugin.prototype, "name", void 0);
__decorate([
    column(),
    __metadata("design:type", String)
], ExternalPlugin.prototype, "value", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], ExternalPlugin.prototype, "apiKey", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], ExternalPlugin.prototype, "apiKey2", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], ExternalPlugin.prototype, "apiKey3", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], ExternalPlugin.prototype, "secretKey", void 0);
__decorate([
    column(),
    __metadata("design:type", Object)
], ExternalPlugin.prototype, "ex1", void 0);
__decorate([
    column(),
    __metadata("design:type", Boolean)
], ExternalPlugin.prototype, "active", void 0);
__decorate([
    column.dateTime({ autoCreate: true }),
    __metadata("design:type", DateTime)
], ExternalPlugin.prototype, "createdAt", void 0);
__decorate([
    column.dateTime({ autoCreate: true, autoUpdate: true }),
    __metadata("design:type", DateTime)
], ExternalPlugin.prototype, "updatedAt", void 0);
//# sourceMappingURL=external_plugin.js.map
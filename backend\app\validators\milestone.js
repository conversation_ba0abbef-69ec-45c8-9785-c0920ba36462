import vine from '@vinejs/vine';

/**
 * Validator for completing milestone
 */
export const completeMilestoneSchema = vine.compile(
  vine.object({
    completionNotes: vine.string().optional().maxLength(2000),
    evidence: vine.array(
      vine.object({
        type: vine.enum(['image', 'document', 'video', 'link']),
        url: vine.string().url(),
        description: vine.string().optional().maxLength(500),
        filename: vine.string().optional().maxLength(255)
      })
    ).optional()
  })
);

/**
 * Validator for approving milestone
 */
export const approveMilestoneSchema = vine.compile(
  vine.object({
    userType: vine.enum(['sender', 'recipient'])
  })
);

/**
 * Validator for rejecting milestone
 */
export const rejectMilestoneSchema = vine.compile(
  vine.object({
    rejectionReason: vine.string().minLength(10).maxLength(1000)
  })
);

/**
 * Validator for updating milestone deliverables
 */
export const updateDeliverablesSchema = vine.compile(
  vine.object({
    deliverables: vine.array(vine.string().minLength(1).maxLength(500))
  })
);

/**
 * Validator for submitting milestone evidence
 */
export const submitEvidenceSchema = vine.compile(
  vine.object({
    evidence: vine.array(
      vine.object({
        type: vine.enum(['image', 'document', 'video', 'link']),
        url: vine.string().url(),
        description: vine.string().optional().maxLength(500),
        filename: vine.string().optional().maxLength(255)
      })
    )
  })
);

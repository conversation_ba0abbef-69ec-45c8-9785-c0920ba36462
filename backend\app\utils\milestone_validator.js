import { DateTime } from 'luxon';

class MilestoneValidator {
  /**
   * Validate milestone data structure
   */
  static validateMilestoneData(milestonesData) {
    if (!Array.isArray(milestonesData)) {
      throw new Error('Milestones must be an array');
    }

    if (milestonesData.length === 0) {
      throw new Error('At least one milestone is required');
    }

    if (milestonesData.length > 20) {
      throw new Error('Maximum 20 milestones allowed');
    }

    let totalPercentage = 0;
    const usedTitles = new Set();

    milestonesData.forEach((milestone, index) => {
      this.validateSingleMilestone(milestone, index);
      
      // Check for duplicate titles
      if (usedTitles.has(milestone.title.toLowerCase())) {
        throw new Error(`Duplicate milestone title: ${milestone.title}`);
      }
      usedTitles.add(milestone.title.toLowerCase());

      totalPercentage += milestone.percentage;
    });

    // Validate total percentage
    if (Math.abs(totalPercentage - 100) > 0.01) {
      throw new Error(`Milestone percentages must total 100%. Current total: ${totalPercentage}%`);
    }

    return true;
  }

  /**
   * Validate a single milestone
   */
  static validateSingleMilestone(milestone, index) {
    const errors = [];

    // Required fields
    if (!milestone.title || typeof milestone.title !== 'string') {
      errors.push(`Milestone ${index + 1}: Title is required and must be a string`);
    } else if (milestone.title.length < 3) {
      errors.push(`Milestone ${index + 1}: Title must be at least 3 characters long`);
    } else if (milestone.title.length > 100) {
      errors.push(`Milestone ${index + 1}: Title must be less than 100 characters`);
    }

    if (typeof milestone.percentage !== 'number') {
      errors.push(`Milestone ${index + 1}: Percentage is required and must be a number`);
    } else if (milestone.percentage <= 0 || milestone.percentage > 100) {
      errors.push(`Milestone ${index + 1}: Percentage must be between 0.01 and 100`);
    }

    // Optional fields validation
    if (milestone.description && typeof milestone.description !== 'string') {
      errors.push(`Milestone ${index + 1}: Description must be a string`);
    } else if (milestone.description && milestone.description.length > 1000) {
      errors.push(`Milestone ${index + 1}: Description must be less than 1000 characters`);
    }

    if (milestone.dueDate) {
      const dueDate = DateTime.fromISO(milestone.dueDate);
      if (!dueDate.isValid) {
        errors.push(`Milestone ${index + 1}: Invalid due date format`);
      } else if (dueDate <= DateTime.now()) {
        errors.push(`Milestone ${index + 1}: Due date must be in the future`);
      }
    }

    if (milestone.deliverables) {
      if (!Array.isArray(milestone.deliverables)) {
        errors.push(`Milestone ${index + 1}: Deliverables must be an array`);
      } else {
        milestone.deliverables.forEach((deliverable, delIndex) => {
          if (typeof deliverable !== 'string' || deliverable.trim().length === 0) {
            errors.push(`Milestone ${index + 1}, Deliverable ${delIndex + 1}: Must be a non-empty string`);
          }
        });
      }
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Validate milestone completion data
   */
  static validateCompletionData(completionData) {
    const errors = [];

    if (completionData.completionNotes && typeof completionData.completionNotes !== 'string') {
      errors.push('Completion notes must be a string');
    } else if (completionData.completionNotes && completionData.completionNotes.length > 2000) {
      errors.push('Completion notes must be less than 2000 characters');
    }

    if (completionData.evidence) {
      if (!Array.isArray(completionData.evidence)) {
        errors.push('Evidence must be an array');
      } else {
        completionData.evidence.forEach((evidence, index) => {
          if (!evidence.type || !evidence.url) {
            errors.push(`Evidence ${index + 1}: Type and URL are required`);
          }
          
          if (!['image', 'document', 'video', 'link'].includes(evidence.type)) {
            errors.push(`Evidence ${index + 1}: Invalid type. Must be image, document, video, or link`);
          }

          if (evidence.description && evidence.description.length > 500) {
            errors.push(`Evidence ${index + 1}: Description must be less than 500 characters`);
          }
        });
      }
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Validate milestone order and dependencies
   */
  static validateMilestoneOrder(milestones) {
    // Sort milestones by order index
    const sortedMilestones = [...milestones].sort((a, b) => a.orderIndex - b.orderIndex);

    // Check for gaps in order
    for (let i = 0; i < sortedMilestones.length; i++) {
      if (sortedMilestones[i].orderIndex !== i + 1) {
        throw new Error(`Milestone order must be sequential starting from 1. Found gap at position ${i + 1}`);
      }
    }

    // Validate due date order (if provided)
    for (let i = 1; i < sortedMilestones.length; i++) {
      const current = sortedMilestones[i];
      const previous = sortedMilestones[i - 1];

      if (current.dueDate && previous.dueDate) {
        const currentDate = DateTime.fromJSDate(current.dueDate);
        const previousDate = DateTime.fromJSDate(previous.dueDate);

        if (currentDate <= previousDate) {
          throw new Error(`Milestone ${current.orderIndex} due date must be after milestone ${previous.orderIndex} due date`);
        }
      }
    }

    return true;
  }

  /**
   * Validate milestone state transitions
   */
  static validateStateTransition(currentStatus, newStatus, userType) {
    const validTransitions = {
      'pending': ['in_progress', 'completed'],
      'in_progress': ['completed', 'pending'],
      'completed': ['approved', 'rejected', 'pending'],
      'approved': ['released'],
      'rejected': ['pending'],
      'released': [] // Terminal state
    };

    if (!validTransitions[currentStatus]) {
      throw new Error(`Invalid current status: ${currentStatus}`);
    }

    if (!validTransitions[currentStatus].includes(newStatus)) {
      throw new Error(`Invalid state transition from ${currentStatus} to ${newStatus}`);
    }

    // Additional validation based on user type
    if (newStatus === 'completed' && userType !== 'recipient') {
      throw new Error('Only recipient can mark milestone as completed');
    }

    if (newStatus === 'rejected' && userType !== 'sender') {
      throw new Error('Only sender can reject milestones');
    }

    if (newStatus === 'approved' && !['sender', 'recipient'].includes(userType)) {
      throw new Error('Only sender or recipient can approve milestones');
    }

    return true;
  }

  /**
   * Validate milestone release conditions
   */
  static validateReleaseConditions(milestone, escrow) {
    const errors = [];

    if (milestone.status !== 'approved') {
      errors.push('Milestone must be approved before release');
    }

    if (!milestone.senderApproved || !milestone.recipientApproved) {
      errors.push('Both parties must approve the milestone before release');
    }

    if (escrow.status !== 'active') {
      errors.push('Escrow must be active to release milestones');
    }

    if (escrow.isExpired && !escrow.autoRelease) {
      errors.push('Cannot release milestone from expired escrow without auto-release');
    }

    if (errors.length > 0) {
      throw new Error(errors.join('; '));
    }

    return true;
  }

  /**
   * Calculate milestone amounts based on percentages
   */
  static calculateMilestoneAmounts(totalAmount, milestones) {
    let calculatedTotal = 0;
    const calculatedMilestones = [];

    milestones.forEach((milestone, index) => {
      const amount = (milestone.percentage / 100) * totalAmount;
      const roundedAmount = Math.round(amount * 100) / 100; // Round to 2 decimal places
      
      calculatedMilestones.push({
        ...milestone,
        amount: roundedAmount
      });
      
      calculatedTotal += roundedAmount;
    });

    // Adjust for rounding differences
    const difference = totalAmount - calculatedTotal;
    if (Math.abs(difference) > 0.01) {
      // Add the difference to the last milestone
      const lastIndex = calculatedMilestones.length - 1;
      calculatedMilestones[lastIndex].amount += difference;
    }

    return calculatedMilestones;
  }

  /**
   * Validate milestone permissions
   */
  static validateMilestonePermissions(milestone, escrow, userId, action) {
    const isSender = escrow.senderId === userId;
    const isRecipient = escrow.recipientId === userId;

    if (!isSender && !isRecipient) {
      throw new Error('Unauthorized: User is not part of this escrow');
    }

    const permissions = {
      'view': isSender || isRecipient,
      'complete': isRecipient,
      'approve': isSender || isRecipient,
      'reject': isSender,
      'update_deliverables': isSender,
      'submit_evidence': isRecipient,
      'release': isSender || isRecipient, // Admin can also release
      'reset': isSender
    };

    if (!permissions[action]) {
      throw new Error(`Invalid action: ${action}`);
    }

    if (!permissions[action]) {
      throw new Error(`Unauthorized: Cannot perform action '${action}' on this milestone`);
    }

    return true;
  }
}

export default MilestoneValidator;

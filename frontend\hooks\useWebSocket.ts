import { useEffect, useRef, useCallback } from 'react';
import { 
  ContactStatusUpdate, 
  ContactVerificationUpdate, 
  ContactRiskScoreUpdate 
} from '@/types/contact';

// WebSocket Event Types
export interface WebSocketEvents {
  // Contact events
  'contact:status_changed': ContactStatusUpdate;
  'contact:verification_updated': ContactVerificationUpdate;
  'contact:risk_score_updated': ContactRiskScoreUpdate;

  // Escrow events
  'escrow:status_changed': { escrowId: number; status: string; timestamp: Date };
  'escrow:confirmed': { escrowId: number; userType: 'sender' | 'recipient'; timestamp: Date };
  'escrow:milestone_completed': { escrowId: number; milestoneId: number; timestamp: Date };
  'escrow:funds_released': { escrowId: number; amount: number; timestamp: Date };

  // Recurring transfer events
  'recurring_transfer:executed': { transferId: number; executionId: number; amount: number; timestamp: Date };
  'recurring_transfer:failed': { transferId: number; error: string; timestamp: Date };
  'recurring_transfer:status_changed': { transferId: number; status: string; timestamp: Date };

  // Fundraising events
  'fundraising_pool:contribution': { poolId: number; amount: number; contributorName?: string; timestamp: Date };
  'fundraising_pool:target_reached': { poolId: number; targetAmount: number; timestamp: Date };
  'fundraising_pool:status_changed': { poolId: number; status: string; timestamp: Date };

  // Notification events
  'notification:new': { userId: number; notification: any; timestamp: Date };
  'notification:read': { userId: number; notificationId: number; timestamp: Date };

  // Admin events
  'admin:system_alert': { type: string; message: string; severity: 'low' | 'medium' | 'high' | 'critical'; timestamp: Date };
  'admin:user_action': { adminId: number; action: string; targetUserId: number; timestamp: Date };

  // System events
  'system:maintenance': { message: string; startTime: Date; endTime: Date };
  'system:health_update': { healthScore: number; issues: string[]; timestamp: Date };
}

export type WebSocketEventType = keyof WebSocketEvents;
export type WebSocketEventHandler<T extends WebSocketEventType> = (data: WebSocketEvents[T]) => void;

interface UseWebSocketOptions {
  enabled?: boolean;
  reconnectAttempts?: number;
  reconnectInterval?: number;
  heartbeatInterval?: number;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Event) => void;
  onReconnect?: (attempt: number) => void;
}

interface WebSocketSubscription {
  eventType: WebSocketEventType;
  handler: WebSocketEventHandler<any>;
}

class PaySnapWebSocketManager {
  private ws: WebSocket | null = null;
  private subscriptions: Map<string, Set<WebSocketSubscription>> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;
  private heartbeatInterval = 30000;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private options: UseWebSocketOptions = {};

  // Circuit breaker properties
  private circuitBreakerState: 'closed' | 'open' | 'half-open' = 'closed';
  private failureCount = 0;
  private lastFailureTime = 0;
  private circuitBreakerTimeout = 60000; // 1 minute
  private maxFailures = 5;

  constructor(options: UseWebSocketOptions = {}) {
    this.options = {
      enabled: true,
      reconnectAttempts: 5,
      reconnectInterval: 5000,
      heartbeatInterval: 30000,
      ...options,
    };

    this.maxReconnectAttempts = this.options.reconnectAttempts!;
    this.reconnectInterval = this.options.reconnectInterval!;
    this.heartbeatInterval = this.options.heartbeatInterval!;
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.options.enabled) {
        resolve();
        return;
      }

      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        resolve();
        return;
      }

      this.isConnecting = true;
      const wsUrl = process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:3333';
      
      try {
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.options.onConnect?.();
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.stopHeartbeat();
          this.options.onDisconnect?.();

          // Handle connection failures
          if (!event.wasClean) {
            this.handleConnectionFailure();

            if (this.options.enabled && this.circuitBreakerState !== 'open') {
              this.scheduleReconnect();
            }
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          this.handleConnectionFailure();
          this.options.onError?.(error);
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  disconnect(): void {
    this.options.enabled = false;
    this.stopHeartbeat();
    this.clearReconnectTimer();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
  }

  subscribe<T extends WebSocketEventType>(
    eventType: T, 
    handler: WebSocketEventHandler<T>
  ): () => void {
    const subscription: WebSocketSubscription = { eventType, handler };
    
    if (!this.subscriptions.has(eventType)) {
      this.subscriptions.set(eventType, new Set());
    }
    
    this.subscriptions.get(eventType)!.add(subscription);

    // Return unsubscribe function
    return () => {
      this.subscriptions.get(eventType)?.delete(subscription);
    };
  }

  unsubscribe<T extends WebSocketEventType>(
    eventType: T, 
    handler: WebSocketEventHandler<T>
  ): void {
    const subscriptions = this.subscriptions.get(eventType);
    if (subscriptions) {
      for (const subscription of subscriptions) {
        if (subscription.handler === handler) {
          subscriptions.delete(subscription);
          break;
        }
      }
    }
  }

  private handleMessage(data: any): void {
    const { type, payload, timestamp } = data;
    
    if (type === 'heartbeat') {
      this.sendHeartbeat();
      return;
    }

    const subscriptions = this.subscriptions.get(type);
    if (subscriptions) {
      subscriptions.forEach(subscription => {
        try {
          subscription.handler({ ...payload, timestamp: new Date(timestamp) });
        } catch (error) {
          console.error(`Error handling WebSocket event ${type}:`, error);
        }
      });
    }
  }

  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatTimer = setInterval(() => {
      this.sendHeartbeat();
    }, this.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private sendHeartbeat(): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ type: 'heartbeat', timestamp: Date.now() }));
    }
  }

  private handleConnectionFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    // Open circuit breaker if too many failures
    if (this.failureCount >= this.maxFailures) {
      this.circuitBreakerState = 'open';
      console.warn('Circuit breaker opened due to repeated connection failures');
    }
  }

  private scheduleReconnect(): void {
    // Circuit breaker logic
    if (this.circuitBreakerState === 'open') {
      const timeSinceLastFailure = Date.now() - this.lastFailureTime;
      if (timeSinceLastFailure < this.circuitBreakerTimeout) {
        console.log('Circuit breaker open, skipping reconnection attempt');
        // Schedule a check for when circuit breaker should transition to half-open
        setTimeout(() => {
          if (this.circuitBreakerState === 'open') {
            this.circuitBreakerState = 'half-open';
            console.log('Circuit breaker transitioned to half-open');
            this.scheduleReconnect();
          }
        }, this.circuitBreakerTimeout - timeSinceLastFailure);
        return;
      } else {
        this.circuitBreakerState = 'half-open';
        console.log('Circuit breaker transitioned to half-open');
      }
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      if (this.circuitBreakerState === 'half-open') {
        this.circuitBreakerState = 'open';
        this.lastFailureTime = Date.now();
      }
      return;
    }

    // Exponential backoff with jitter
    const baseDelay = this.reconnectInterval;
    const exponentialDelay = baseDelay * Math.pow(2, this.reconnectAttempts);
    const jitter = Math.random() * 1000; // Add up to 1 second jitter
    const maxDelay = 30000; // Max 30 seconds

    const delay = Math.min(exponentialDelay + jitter, maxDelay);

    this.clearReconnectTimer();
    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) after ${delay}ms`);
      this.options.onReconnect?.(this.reconnectAttempts);

      this.connect()
        .then(() => {
          // Success - reset circuit breaker and counters
          this.circuitBreakerState = 'closed';
          this.failureCount = 0;
          this.reconnectAttempts = 0;
          console.log('WebSocket reconnected successfully, circuit breaker reset');
        })
        .catch((error) => {
          console.error('Reconnection failed:', error);
          this.handleConnectionFailure();

          if (this.circuitBreakerState === 'half-open') {
            this.circuitBreakerState = 'open';
            this.lastFailureTime = Date.now();
            console.log('Circuit breaker opened after failed half-open attempt');
          }

          // Continue trying if under limit and circuit breaker allows
          if (this.reconnectAttempts < this.maxReconnectAttempts && this.circuitBreakerState !== 'open') {
            this.scheduleReconnect();
          }
        });
    }, delay);
  }

  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  getConnectionState(): string {
    if (!this.ws) return 'disconnected';

    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }

  getCircuitBreakerState(): {
    state: string;
    failureCount: number;
    lastFailureTime: number;
    timeUntilHalfOpen: number;
  } {
    const timeUntilHalfOpen = this.circuitBreakerState === 'open'
      ? Math.max(0, this.circuitBreakerTimeout - (Date.now() - this.lastFailureTime))
      : 0;

    return {
      state: this.circuitBreakerState,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      timeUntilHalfOpen,
    };
  }

  getReconnectionInfo(): {
    attempts: number;
    maxAttempts: number;
    isReconnecting: boolean;
    nextAttemptIn: number;
  } {
    return {
      attempts: this.reconnectAttempts,
      maxAttempts: this.maxReconnectAttempts,
      isReconnecting: Boolean(this.reconnectTimer),
      nextAttemptIn: 0, // Could be enhanced to track exact timing
    };
  }
}

// Global WebSocket manager instance
let wsManager: PaySnapWebSocketManager | null = null;

export function useWebSocket(options: UseWebSocketOptions = {}) {
  const wsRef = useRef<PaySnapWebSocketManager | null>(null);
  const subscriptionsRef = useRef<Map<string, () => void>>(new Map());
  const componentIdRef = useRef<string>(`component-${Date.now()}-${Math.random()}`);

  useEffect(() => {
    if (!wsRef.current) {
      wsRef.current = wsManager || new PaySnapWebSocketManager(options);
      if (!wsManager) {
        wsManager = wsRef.current;
      }
    }

    if (options.enabled !== false) {
      wsRef.current.connect().catch(console.error);
    }

    // Cleanup all subscriptions on unmount
    return () => {
      subscriptionsRef.current.forEach(unsubscribe => {
        try {
          unsubscribe();
        } catch (error) {
          console.warn('Error unsubscribing from WebSocket event:', error);
        }
      });
      subscriptionsRef.current.clear();
    };
  }, [options.enabled]);

  const subscribe = useCallback(<T extends WebSocketEventType>(
    eventType: T,
    handler: WebSocketEventHandler<T>
  ) => {
    const unsubscribe = wsRef.current?.subscribe(eventType, handler) || (() => {});

    // Store unsubscribe function for cleanup with unique key
    const key = `${componentIdRef.current}-${eventType}-${Date.now()}-${Math.random()}`;
    subscriptionsRef.current.set(key, unsubscribe);

    // Return enhanced unsubscribe that also removes from our tracking
    return () => {
      try {
        unsubscribe();
        subscriptionsRef.current.delete(key);
      } catch (error) {
        console.warn('Error during manual unsubscribe:', error);
      }
    };
  }, []);

  const unsubscribe = useCallback(<T extends WebSocketEventType>(
    eventType: T,
    handler: WebSocketEventHandler<T>
  ) => {
    try {
      wsRef.current?.unsubscribe(eventType, handler);

      // Remove from our tracking (find by event type and handler)
      for (const [key, unsub] of subscriptionsRef.current.entries()) {
        if (key.includes(eventType)) {
          subscriptionsRef.current.delete(key);
          break;
        }
      }
    } catch (error) {
      console.warn('Error during unsubscribe:', error);
    }
  }, []);

  const disconnect = useCallback(() => {
    try {
      // Clean up all subscriptions first
      subscriptionsRef.current.forEach(unsubscribe => unsubscribe());
      subscriptionsRef.current.clear();

      wsRef.current?.disconnect();
    } catch (error) {
      console.warn('Error during disconnect:', error);
    }
  }, []);

  const isConnected = useCallback(() => {
    try {
      return wsRef.current?.isConnected() || false;
    } catch (error) {
      console.warn('Error checking connection state:', error);
      return false;
    }
  }, []);

  const getConnectionState = useCallback(() => {
    try {
      return wsRef.current?.getConnectionState() || 'disconnected';
    } catch (error) {
      console.warn('Error getting connection state:', error);
      return 'disconnected';
    }
  }, []);

  // Debug information for development
  const getDebugInfo = useCallback(() => {
    if (process.env.NODE_ENV === 'development') {
      return {
        componentId: componentIdRef.current,
        subscriptionCount: subscriptionsRef.current.size,
        subscriptionKeys: Array.from(subscriptionsRef.current.keys()),
        connectionState: getConnectionState(),
        isConnected: isConnected(),
        circuitBreaker: wsRef.current?.getCircuitBreakerState(),
        reconnection: wsRef.current?.getReconnectionInfo(),
      };
    }
    return null;
  }, [getConnectionState, isConnected]);

  // Get circuit breaker status
  const getCircuitBreakerStatus = useCallback(() => {
    return wsRef.current?.getCircuitBreakerState() || {
      state: 'closed',
      failureCount: 0,
      lastFailureTime: 0,
      timeUntilHalfOpen: 0,
    };
  }, []);

  // Get reconnection status
  const getReconnectionStatus = useCallback(() => {
    return wsRef.current?.getReconnectionInfo() || {
      attempts: 0,
      maxAttempts: 0,
      isReconnecting: false,
      nextAttemptIn: 0,
    };
  }, []);

  return {
    subscribe,
    unsubscribe,
    disconnect,
    isConnected,
    getConnectionState,
    getDebugInfo,
    getCircuitBreakerStatus,
    getReconnectionStatus,
  };
}

// Cleanup function for app shutdown
export function cleanupWebSocket(): void {
  wsManager?.disconnect();
  wsManager = null;
}

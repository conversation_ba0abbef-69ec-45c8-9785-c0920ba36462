import router from '@adonisjs/core/services/router';
import { middleware } from '#start/kernel';

const EscrowsController = () => import('#controllers/escrows_controller');
const MilestonesController = () => import('#controllers/milestones_controller');

// Escrow routes - require authentication
router.group(() => {
  // Main escrow routes
  router.get('/escrows', [EscrowsController, 'index']);
  router.post('/escrows', [EscrowsController, 'store']);
  router.get('/escrows/statistics', [EscrowsController, 'statistics']);
  router.get('/escrows/active-count', [EscrowsController, 'activeCount']);
  router.get('/escrows/requires-action', [EscrowsController, 'requiresAction']);
  router.get('/escrows/export', [EscrowsController, 'exportCsv']);
  
  // Individual escrow routes
  router.get('/escrows/:id', [EscrowsController, 'show']);
  router.post('/escrows/:id/fund', [EscrowsController, 'fund']);
  router.post('/escrows/:id/confirm', [EscrowsController, 'confirm']);
  router.post('/escrows/:id/release', [EscrowsController, 'release']);
  router.post('/escrows/:id/cancel', [EscrowsController, 'cancel']);
  
  // Escrow by public ID
  router.get('/escrows/by-id/:escrowId', [EscrowsController, 'getByEscrowId']);
  
  // Milestone routes
  router.get('/escrows/:escrowId/milestones', [MilestonesController, 'index']);
  router.get('/escrows/:escrowId/milestones/progress', [MilestonesController, 'progress']);
  router.get('/escrows/:escrowId/milestones/performance', [MilestonesController, 'performance']);
  
  router.get('/milestones/:id', [MilestonesController, 'show']);
  router.post('/milestones/:id/complete', [MilestonesController, 'complete']);
  router.post('/milestones/:id/approve', [MilestonesController, 'approve']);
  router.post('/milestones/:id/reject', [MilestonesController, 'reject']);
  router.post('/milestones/:id/release', [MilestonesController, 'release']);
  router.post('/milestones/:id/reset', [MilestonesController, 'reset']);
  router.put('/milestones/:id/deliverables', [MilestonesController, 'updateDeliverables']);
  router.post('/milestones/:id/evidence', [MilestonesController, 'submitEvidence']);
  
  // Milestone utility routes
  router.get('/milestones/overdue', [MilestonesController, 'overdue']);
  router.get('/milestones/upcoming-deadlines', [MilestonesController, 'upcomingDeadlines']);
  
}).prefix('/api/v1').middleware([middleware.auth()]);

// Public escrow routes (no authentication required)
router.group(() => {
  // Public escrow lookup by escrow ID (for sharing/viewing)
  router.get('/escrows/public/:escrowId', [EscrowsController, 'getByEscrowId']);
}).prefix('/api/v1/public');

import { useMemo } from 'react';

// Define different types of loading states
export interface LoadingStates {
  enhanced?: boolean;
  legacy?: boolean;
  websocket?: boolean;
  api?: boolean;
  search?: boolean;
  upload?: boolean;
  submit?: boolean;
  [key: string]: boolean | undefined;
}

export interface LoadingDetails {
  isLoading: boolean;
  activeStates: string[];
  count: number;
  hasEnhanced: boolean;
  hasLegacy: boolean;
  hasWebSocket: boolean;
  hasApi: boolean;
  primaryState: string | null;
  loadingMessage: string;
}

// Priority order for loading states (higher priority shows first)
const LOADING_PRIORITY = {
  submit: 10,
  upload: 9,
  api: 8,
  enhanced: 7,
  search: 6,
  legacy: 5,
  websocket: 4,
} as const;

// Default loading messages for different states
const LOADING_MESSAGES = {
  submit: 'Submitting...',
  upload: 'Uploading...',
  api: 'Loading data...',
  enhanced: 'Loading contacts...',
  search: 'Searching...',
  legacy: 'Loading...',
  websocket: 'Connecting...',
  default: 'Loading...',
} as const;

/**
 * Hook to manage unified loading states across components
 * Coordinates multiple loading states and provides intelligent prioritization
 */
export function useUnifiedLoading(states: LoadingStates): LoadingDetails {
  const loadingDetails = useMemo(() => {
    // Filter active loading states
    const activeStates = Object.entries(states)
      .filter(([_, isActive]) => isActive === true)
      .map(([key, _]) => key);

    const isLoading = activeStates.length > 0;

    // Determine primary state based on priority
    const primaryState = activeStates.length > 0 
      ? activeStates.reduce((highest, current) => {
          const currentPriority = LOADING_PRIORITY[current as keyof typeof LOADING_PRIORITY] || 0;
          const highestPriority = LOADING_PRIORITY[highest as keyof typeof LOADING_PRIORITY] || 0;
          return currentPriority > highestPriority ? current : highest;
        })
      : null;

    // Generate appropriate loading message
    const loadingMessage = primaryState 
      ? (LOADING_MESSAGES[primaryState as keyof typeof LOADING_MESSAGES] || LOADING_MESSAGES.default)
      : LOADING_MESSAGES.default;

    return {
      isLoading,
      activeStates,
      count: activeStates.length,
      hasEnhanced: Boolean(states.enhanced),
      hasLegacy: Boolean(states.legacy),
      hasWebSocket: Boolean(states.websocket),
      hasApi: Boolean(states.api),
      primaryState,
      loadingMessage,
    };
  }, [states]);

  return loadingDetails;
}

/**
 * Hook specifically for contact-related loading states
 * Provides specialized handling for contact selection scenarios
 */
export function useContactLoadingStates(states: {
  enhancedApi?: boolean;
  legacyApi?: boolean;
  websocketConnection?: boolean;
  search?: boolean;
}) {
  const unifiedStates: LoadingStates = {
    enhanced: states.enhancedApi,
    legacy: states.legacyApi,
    websocket: states.websocketConnection,
    search: states.search,
  };

  const loadingDetails = useUnifiedLoading(unifiedStates);

  // Additional contact-specific logic
  const contactSpecificDetails = useMemo(() => {
    const isSearching = Boolean(states.search);
    const isConnecting = Boolean(states.websocketConnection);
    const isLoadingData = Boolean(states.enhancedApi || states.legacyApi);

    // Determine the most appropriate loading state for contacts
    let contactLoadingMessage = 'Loading contacts...';
    if (isSearching) {
      contactLoadingMessage = 'Searching contacts...';
    } else if (isConnecting) {
      contactLoadingMessage = 'Connecting...';
    } else if (states.enhancedApi) {
      contactLoadingMessage = 'Loading enhanced contacts...';
    } else if (states.legacyApi) {
      contactLoadingMessage = 'Loading contacts...';
    }

    return {
      ...loadingDetails,
      isSearching,
      isConnecting,
      isLoadingData,
      contactLoadingMessage,
      shouldShowSpinner: isLoadingData || isSearching,
      shouldShowConnectionStatus: isConnecting,
    };
  }, [loadingDetails, states]);

  return contactSpecificDetails;
}

/**
 * Hook for form-related loading states
 * Handles submission, validation, and API calls
 */
export function useFormLoadingStates(states: {
  submitting?: boolean;
  validating?: boolean;
  uploading?: boolean;
  apiCall?: boolean;
}) {
  const unifiedStates: LoadingStates = {
    submit: states.submitting,
    api: states.validating || states.apiCall,
    upload: states.uploading,
  };

  const loadingDetails = useUnifiedLoading(unifiedStates);

  const formSpecificDetails = useMemo(() => {
    const isSubmitting = Boolean(states.submitting);
    const isValidating = Boolean(states.validating);
    const isUploading = Boolean(states.uploading);
    const isApiCall = Boolean(states.apiCall);

    // Form-specific loading messages
    let formLoadingMessage = 'Processing...';
    if (isSubmitting) {
      formLoadingMessage = 'Submitting form...';
    } else if (isUploading) {
      formLoadingMessage = 'Uploading files...';
    } else if (isValidating) {
      formLoadingMessage = 'Validating...';
    } else if (isApiCall) {
      formLoadingMessage = 'Saving...';
    }

    return {
      ...loadingDetails,
      isSubmitting,
      isValidating,
      isUploading,
      isApiCall,
      formLoadingMessage,
      shouldDisableForm: isSubmitting || isUploading,
      shouldShowProgress: isUploading || isSubmitting,
    };
  }, [loadingDetails, states]);

  return formSpecificDetails;
}

/**
 * Hook for WebSocket connection states
 * Provides detailed connection status information
 */
export function useWebSocketLoadingStates(connectionState: string) {
  const states: LoadingStates = {
    websocket: connectionState === 'connecting' || connectionState === 'reconnecting',
  };

  const loadingDetails = useUnifiedLoading(states);

  const websocketDetails = useMemo(() => {
    const isConnecting = connectionState === 'connecting';
    const isReconnecting = connectionState === 'reconnecting';
    const isConnected = connectionState === 'connected';
    const isDisconnected = connectionState === 'disconnected';

    let connectionMessage = 'Connected';
    if (isConnecting) {
      connectionMessage = 'Connecting...';
    } else if (isReconnecting) {
      connectionMessage = 'Reconnecting...';
    } else if (isDisconnected) {
      connectionMessage = 'Disconnected';
    }

    return {
      ...loadingDetails,
      isConnecting,
      isReconnecting,
      isConnected,
      isDisconnected,
      connectionMessage,
      shouldShowConnectionIndicator: !isConnected,
      connectionStatus: connectionState,
    };
  }, [loadingDetails, connectionState]);

  return websocketDetails;
}

/**
 * Utility function to create loading state objects
 */
export function createLoadingStates(activeStates: string[]): LoadingStates {
  const states: LoadingStates = {};
  activeStates.forEach(state => {
    states[state] = true;
  });
  return states;
}

/**
 * Utility function to merge multiple loading state objects
 */
export function mergeLoadingStates(...stateObjects: LoadingStates[]): LoadingStates {
  return stateObjects.reduce((merged, current) => {
    return { ...merged, ...current };
  }, {});
}

/**
 * Hook for debugging loading states in development
 */
export function useLoadingDebug(states: LoadingStates, componentName?: string) {
  const loadingDetails = useUnifiedLoading(states);

  // Only log in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`[${componentName || 'Component'}] Loading States:`, {
      states,
      details: loadingDetails,
    });
  }

  return loadingDetails;
}

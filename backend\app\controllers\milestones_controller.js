import errorHandler from '#exceptions/error_handler';
import EscrowMilestone from '#models/escrow_milestone';
import Escrow from '#models/escrow';
import milestoneService from '#services/milestone_service';
import MilestoneProgressTracker from '#utils/milestone_progress_tracker';
import { 
  completeMilestoneSchema, 
  approveMilestoneSchema, 
  rejectMilestoneSchema,
  updateDeliverablesSchema,
  submitEvidenceSchema 
} from '#validators/milestone';

export default class MilestonesController {
  /**
   * Get milestones for an escrow
   */
  async index(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const escrowId = request.param('escrowId');
      
      // Verify user has access to this escrow
      const escrow = await Escrow.query()
        .where('id', escrowId)
        .where((builder) => {
          builder.where('sender_id', auth.user.id).orWhere('recipient_id', auth.user.id);
        })
        .first();

      if (!escrow) {
        return response.notFound({ success: false, message: 'Escrow not found' });
      }

      const milestones = await EscrowMilestone.query()
        .where('escrow_id', escrowId)
        .orderBy('order_index');

      return response.json(milestones);
    } catch (error) {
      errorHandler(error, ctx, 'Milestones index error');
    }
  }

  /**
   * Get milestone progress for an escrow
   */
  async progress(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const escrowId = request.param('escrowId');
      
      // Verify user has access to this escrow
      const escrow = await Escrow.query()
        .where('id', escrowId)
        .where((builder) => {
          builder.where('sender_id', auth.user.id).orWhere('recipient_id', auth.user.id);
        })
        .first();

      if (!escrow) {
        return response.notFound({ success: false, message: 'Escrow not found' });
      }

      const progress = await MilestoneProgressTracker.calculateProgress(escrowId);
      
      return response.json(progress);
    } catch (error) {
      errorHandler(error, ctx, 'Milestone progress error');
    }
  }

  /**
   * Get specific milestone
   */
  async show(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const milestoneId = request.param('id');
      
      const milestone = await EscrowMilestone.query()
        .where('id', milestoneId)
        .preload('escrow', (query) => {
          query.where((builder) => {
            builder.where('sender_id', auth.user.id).orWhere('recipient_id', auth.user.id);
          });
        })
        .first();

      if (!milestone || !milestone.escrow) {
        return response.notFound({ success: false, message: 'Milestone not found' });
      }

      return response.json(milestone);
    } catch (error) {
      errorHandler(error, ctx, 'Milestone show error');
    }
  }

  /**
   * Complete a milestone
   */
  async complete(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const milestoneId = request.param('id');
      const completionData = await request.validateUsing(completeMilestoneSchema);
      
      const milestone = await milestoneService.completeMilestone(
        milestoneId, 
        auth.user.id, 
        completionData
      );
      
      return response.json({
        success: true,
        message: 'Milestone completed successfully',
        data: milestone
      });
    } catch (error) {
      errorHandler(error, ctx, 'Milestone completion error');
    }
  }

  /**
   * Approve a milestone
   */
  async approve(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const milestoneId = request.param('id');
      const { userType } = await request.validateUsing(approveMilestoneSchema);
      
      const milestone = await milestoneService.approveMilestone(
        milestoneId, 
        auth.user.id, 
        userType
      );
      
      return response.json({
        success: true,
        message: 'Milestone approved successfully',
        data: milestone
      });
    } catch (error) {
      errorHandler(error, ctx, 'Milestone approval error');
    }
  }

  /**
   * Reject a milestone
   */
  async reject(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const milestoneId = request.param('id');
      const { rejectionReason } = await request.validateUsing(rejectMilestoneSchema);
      
      const milestone = await milestoneService.rejectMilestone(
        milestoneId, 
        auth.user.id, 
        rejectionReason
      );
      
      return response.json({
        success: true,
        message: 'Milestone rejected',
        data: milestone
      });
    } catch (error) {
      errorHandler(error, ctx, 'Milestone rejection error');
    }
  }

  /**
   * Release milestone funds
   */
  async release(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const milestoneId = request.param('id');
      
      const result = await milestoneService.releaseMilestone(
        milestoneId, 
        auth.user.id, 
        'manual'
      );
      
      return response.json({
        success: true,
        message: 'Milestone funds released successfully',
        data: result
      });
    } catch (error) {
      errorHandler(error, ctx, 'Milestone release error');
    }
  }

  /**
   * Update milestone deliverables
   */
  async updateDeliverables(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const milestoneId = request.param('id');
      const { deliverables } = await request.validateUsing(updateDeliverablesSchema);
      
      const milestone = await milestoneService.updateMilestoneDeliverables(
        milestoneId, 
        auth.user.id, 
        deliverables
      );
      
      return response.json({
        success: true,
        message: 'Milestone deliverables updated successfully',
        data: milestone
      });
    } catch (error) {
      errorHandler(error, ctx, 'Milestone deliverables update error');
    }
  }

  /**
   * Submit evidence for milestone completion
   */
  async submitEvidence(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const milestoneId = request.param('id');
      const { evidence } = await request.validateUsing(submitEvidenceSchema);
      
      const milestone = await milestoneService.submitMilestoneEvidence(
        milestoneId, 
        auth.user.id, 
        evidence
      );
      
      return response.json({
        success: true,
        message: 'Evidence submitted successfully',
        data: milestone
      });
    } catch (error) {
      errorHandler(error, ctx, 'Milestone evidence submission error');
    }
  }

  /**
   * Reset milestone to pending state
   */
  async reset(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const milestoneId = request.param('id');
      
      const milestone = await milestoneService.resetMilestone(milestoneId, auth.user.id);
      
      return response.json({
        success: true,
        message: 'Milestone reset successfully',
        data: milestone
      });
    } catch (error) {
      errorHandler(error, ctx, 'Milestone reset error');
    }
  }

  /**
   * Get overdue milestones for user
   */
  async overdue(ctx) {
    const { auth, response } = ctx;
    
    try {
      const overdueMilestones = await milestoneService.getOverdueMilestones();
      
      // Filter to only include milestones where user is involved
      const userOverdueMilestones = overdueMilestones.filter(milestone => 
        milestone.escrow.senderId === auth.user.id || 
        milestone.escrow.recipientId === auth.user.id
      );
      
      return response.json(userOverdueMilestones);
    } catch (error) {
      errorHandler(error, ctx, 'Overdue milestones error');
    }
  }

  /**
   * Get milestone performance metrics for user
   */
  async performance(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const escrowId = request.param('escrowId');
      
      // Verify user has access to this escrow
      const escrow = await Escrow.query()
        .where('id', escrowId)
        .where((builder) => {
          builder.where('sender_id', auth.user.id).orWhere('recipient_id', auth.user.id);
        })
        .first();

      if (!escrow) {
        return response.notFound({ success: false, message: 'Escrow not found' });
      }

      const milestones = await EscrowMilestone.query()
        .where('escrow_id', escrowId)
        .orderBy('order_index');

      const metrics = MilestoneProgressTracker.calculatePerformanceMetrics(milestones);
      
      return response.json(metrics);
    } catch (error) {
      errorHandler(error, ctx, 'Milestone performance error');
    }
  }

  /**
   * Get upcoming milestone deadlines
   */
  async upcomingDeadlines(ctx) {
    const { auth, request, response } = ctx;
    
    try {
      const { daysAhead = 7 } = request.qs();
      
      // Get all user's active escrows
      const escrows = await Escrow.query()
        .where('status', 'active')
        .where((builder) => {
          builder.where('sender_id', auth.user.id).orWhere('recipient_id', auth.user.id);
        })
        .select('id');

      const escrowIds = escrows.map(escrow => escrow.id);
      
      if (escrowIds.length === 0) {
        return response.json([]);
      }

      const milestones = await EscrowMilestone.query()
        .whereIn('escrow_id', escrowIds)
        .whereNotIn('status', ['released', 'rejected'])
        .preload('escrow');

      const upcomingDeadlines = MilestoneProgressTracker.getUpcomingDeadlines(
        milestones, 
        parseInt(daysAhead)
      );
      
      return response.json(upcomingDeadlines);
    } catch (error) {
      errorHandler(error, ctx, 'Upcoming deadlines error');
    }
  }
}

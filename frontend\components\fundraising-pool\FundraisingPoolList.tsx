"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { FundraisingPoolCard } from "./FundraisingPoolCard";
import { FundraisingPoolFilters } from "./FundraisingPoolFilters";
import { FundraisingPool, PoolFilters } from "@/types/fundraising-pool";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, RefreshCw, Target, TrendingUp, Users, DollarSign } from "lucide-react";
import Link from "next/link";

interface FundraisingPoolListProps {
  pools: FundraisingPool[];
  isLoading?: boolean;
  error?: string;
  currentUserId?: number;
  onFiltersChange?: (filters: PoolFilters) => void;
  onAction?: (action: string, poolId: number, data?: any) => void;
  onRefresh?: () => void;
  showCreateButton?: boolean;
  title?: string;
  viewMode?: 'all' | 'my-pools' | 'my-contributions';
}

export function FundraisingPoolList({
  pools,
  isLoading = false,
  error,
  currentUserId,
  onFiltersChange,
  onAction,
  onRefresh,
  showCreateButton = true,
  title = "Fundraising Pools",
  viewMode = 'all'
}: FundraisingPoolListProps) {
  const { t } = useTranslation();
  const [filters, setFilters] = useState<PoolFilters>({});
  const [activeTab, setActiveTab] = useState("all");

  const handleFiltersChange = (newFilters: PoolFilters) => {
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const getFilteredPools = () => {
    let filtered = pools;

    // Apply tab filter
    if (activeTab !== 'all') {
      filtered = filtered.filter(pool => {
        switch (activeTab) {
          case 'active':
            return pool.status === 'active';
          case 'completed':
            return pool.status === 'completed';
          case 'trending':
            return pool.status === 'active' && pool.progressPercentage > 50;
          case 'ending-soon':
            return pool.status === 'active' && pool.daysRemaining !== null && pool.daysRemaining <= 7;
          default:
            return true;
        }
      });
    }

    // Apply other filters
    if (filters.status && filters.status !== activeTab) {
      filtered = filtered.filter(pool => pool.status === filters.status);
    }
    if (filters.category) {
      filtered = filtered.filter(pool => pool.category === filters.category);
    }

    return filtered;
  };

  const filteredPools = getFilteredPools();

  // Calculate statistics
  const stats = {
    total: pools.length,
    active: pools.filter(p => p.status === 'active').length,
    completed: pools.filter(p => p.status === 'completed').length,
    totalRaised: pools.reduce((sum, p) => sum + p.currentAmount, 0),
    totalContributors: pools.reduce((sum, p) => sum + p.contributorsCount, 0),
  };

  const getTabBadge = (count: number) => {
    if (count === 0) return null;
    return <span className="ml-2 text-xs bg-muted text-muted-foreground px-1.5 py-0.5 rounded-full">{count}</span>;
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold">{t(title)}</CardTitle>
            <div className="flex items-center space-x-2">
              {onRefresh && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRefresh}
                  disabled={isLoading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  {t('Refresh')}
                </Button>
              )}
              {showCreateButton && (
                <Button asChild>
                  <Link href="/fundraising-pools/create">
                    <Plus className="h-4 w-4 mr-2" />
                    {t('Create Pool')}
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <FundraisingPoolFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
          />
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">{t('Total Pools')}</p>
                <p className="text-lg font-semibold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">{t('Active')}</p>
                <p className="text-lg font-semibold">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-emerald-600" />
              <div>
                <p className="text-sm text-muted-foreground">{t('Total Raised')}</p>
                <p className="text-lg font-semibold">{stats.totalRaised.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">{t('Contributors')}</p>
                <p className="text-lg font-semibold">{stats.totalContributors}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Card>
        <CardContent className="pt-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="all" className="text-sm">
                {t('All')}
                {getTabBadge(pools.length)}
              </TabsTrigger>
              <TabsTrigger value="active" className="text-sm">
                {t('Active')}
                {getTabBadge(stats.active)}
              </TabsTrigger>
              <TabsTrigger value="completed" className="text-sm">
                {t('Completed')}
                {getTabBadge(stats.completed)}
              </TabsTrigger>
              <TabsTrigger value="trending" className="text-sm">
                {t('Trending')}
                {getTabBadge(pools.filter(p => p.status === 'active' && p.progressPercentage > 50).length)}
              </TabsTrigger>
              <TabsTrigger value="ending-soon" className="text-sm">
                {t('Ending Soon')}
                {getTabBadge(pools.filter(p => p.status === 'active' && p.daysRemaining !== null && p.daysRemaining <= 7).length)}
              </TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-6">
              {/* Loading State */}
              {isLoading && (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {Array.from({ length: 6 }).map((_, index) => (
                    <Card key={index}>
                      <CardHeader>
                        <Skeleton className="h-48 w-full rounded-lg" />
                        <div className="space-y-2">
                          <Skeleton className="h-6 w-3/4" />
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-2/3" />
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <Skeleton className="h-3 w-full" />
                        <div className="grid grid-cols-3 gap-4">
                          <Skeleton className="h-8 w-full" />
                          <Skeleton className="h-8 w-full" />
                          <Skeleton className="h-8 w-full" />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* Empty State */}
              {!isLoading && filteredPools.length === 0 && (
                <div className="text-center py-12">
                  <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                    <Target className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">{t('No pools found')}</h3>
                  <p className="text-muted-foreground mb-4">
                    {pools.length === 0 
                      ? t('No fundraising pools have been created yet.')
                      : t('No pools match your current filters.')
                    }
                  </p>
                  {showCreateButton && pools.length === 0 && (
                    <Button asChild>
                      <Link href="/fundraising-pools/create">
                        <Plus className="h-4 w-4 mr-2" />
                        {t('Create First Pool')}
                      </Link>
                    </Button>
                  )}
                </div>
              )}

              {/* Pool Grid */}
              {!isLoading && filteredPools.length > 0 && (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {filteredPools.map((pool) => (
                    <FundraisingPoolCard
                      key={pool.id}
                      pool={pool}
                      isOwner={currentUserId === pool.creatorId}
                      onAction={onAction}
                    />
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Results Summary */}
      {!isLoading && filteredPools.length > 0 && (
        <div className="text-center text-sm text-muted-foreground">
          {t('Showing {{count}} of {{total}} pools', {
            count: filteredPools.length,
            total: pools.length
          })}
        </div>
      )}
    </div>
  );
}

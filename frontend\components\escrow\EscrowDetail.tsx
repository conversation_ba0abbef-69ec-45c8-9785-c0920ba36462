"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Escrow } from "@/types/escrow";
import { Badge } from "@/components/ui/badge";
import { <PERSON>ton } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { imageURL } from "@/lib/utils";
import { 
  Clock, 
  DollarSign, 
  User, 
  Shield, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  FileText,
  Calendar,
  Target,
  ArrowLeft
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { EscrowActions } from "./EscrowActions";
import { MilestoneList } from "../milestone/MilestoneList";
import { EscrowTimeline } from "./EscrowTimeline";

interface EscrowDetailProps {
  escrow: Escrow;
  currentUserId: number;
  onAction?: (action: string, escrowId: number, data?: any) => Promise<void>;
  isLoading?: boolean;
}

export function EscrowDetail({ 
  escrow, 
  currentUserId, 
  onAction,
  isLoading = false 
}: EscrowDetailProps) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("overview");

  const userRole = escrow.senderId === currentUserId ? 'sender' : 'recipient';
  const counterparty = userRole === 'sender' ? escrow.recipient : escrow.sender;

  const getStatusIcon = () => {
    switch (escrow.status) {
      case 'pending':
        return <Clock className="h-5 w-5" />;
      case 'active':
        return <Shield className="h-5 w-5" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5" />;
      case 'expired':
        return <AlertTriangle className="h-5 w-5" />;
      case 'disputed':
        return <AlertTriangle className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getStatusColor = () => {
    switch (escrow.status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'active':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'expired':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'disputed':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/escrows">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('Back to Escrows')}
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {escrow.amount.toLocaleString()} {escrow.currencyCode}
            </h1>
            <p className="text-muted-foreground">
              {t('Escrow ID')}: {escrow.escrowId}
            </p>
          </div>
        </div>
        
        <Badge 
          variant="outline" 
          className={`${getStatusColor()} flex items-center space-x-1 text-sm px-3 py-1`}
        >
          {getStatusIcon()}
          <span className="capitalize">{t(escrow.status)}</span>
        </Badge>
      </div>

      {/* Status Alert */}
      {escrow.isExpired && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {t('This escrow has expired and may require admin intervention.')}
          </AlertDescription>
        </Alert>
      )}

      {escrow.status === 'pending' && (
        <Alert>
          <Clock className="h-4 w-4" />
          <AlertDescription>
            {t('This escrow is pending confirmation from both parties.')}
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">{t('Overview')}</TabsTrigger>
              <TabsTrigger value="milestones">{t('Milestones')}</TabsTrigger>
              <TabsTrigger value="timeline">{t('Timeline')}</TabsTrigger>
              <TabsTrigger value="documents">{t('Documents')}</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Description */}
              {escrow.description && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <FileText className="h-5 w-5" />
                      <span>{t('Description')}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground whitespace-pre-wrap">
                      {escrow.description}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Terms and Conditions */}
              {escrow.termsConditions && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Shield className="h-5 w-5" />
                      <span>{t('Terms and Conditions')}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground whitespace-pre-wrap">
                      {escrow.termsConditions}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Progress */}
              {escrow.hasMilestones && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Target className="h-5 w-5" />
                      <span>{t('Progress')}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        {escrow.completedMilestones} of {escrow.totalMilestones} milestones completed
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {escrow.progressPercentage}%
                      </span>
                    </div>
                    <Progress value={escrow.progressPercentage} className="h-3" />
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="milestones">
              {escrow.hasMilestones ? (
                <MilestoneList
                  escrowId={escrow.id}
                  milestones={escrow.milestones || []}
                  userRole={userRole}
                  onAction={onAction}
                />
              ) : (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <Target className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">{t('No Milestones')}</h3>
                    <p className="text-muted-foreground text-center">
                      {t('This escrow does not use milestones.')}
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="timeline">
              <EscrowTimeline escrow={escrow} />
            </TabsContent>

            <TabsContent value="documents">
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">{t('No Documents')}</h3>
                  <p className="text-muted-foreground text-center">
                    {t('No documents have been uploaded for this escrow.')}
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Actions */}
          <EscrowActions
            escrow={escrow}
            userRole={userRole}
            onAction={onAction}
            isLoading={isLoading}
          />

          {/* Counterparty Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>{userRole === 'sender' ? t('Recipient') : t('Sender')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-3">
                {counterparty?.customer?.avatar && (
                  <Image
                    src={imageURL(counterparty.customer.avatar)}
                    alt={counterparty.name}
                    width={40}
                    height={40}
                    className="rounded-full"
                  />
                )}
                <div>
                  <p className="font-medium">{counterparty?.name || t('Unknown User')}</p>
                  <p className="text-sm text-muted-foreground">
                    {counterparty?.email}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Escrow Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <span>{t('Escrow Details')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">{t('Amount')}</p>
                  <p className="font-medium">
                    {escrow.amount.toLocaleString()} {escrow.currencyCode}
                  </p>
                </div>
                <div>
                  <p className="text-muted-foreground">{t('Fee')}</p>
                  <p className="font-medium">
                    {escrow.fee} {escrow.currencyCode}
                  </p>
                </div>
                <div>
                  <p className="text-muted-foreground">{t('Total')}</p>
                  <p className="font-medium">
                    {escrow.total.toLocaleString()} {escrow.currencyCode}
                  </p>
                </div>
                <div>
                  <p className="text-muted-foreground">{t('Fee Payer')}</p>
                  <p className="font-medium capitalize">{t(escrow.feePayer)}</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('Created')}</span>
                  <span>{escrow.getCreatedAt()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('Deadline')}</span>
                  <span>{escrow.getDeadline()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('Time Remaining')}</span>
                  <span className={escrow.isExpired ? 'text-red-600' : 'text-green-600'}>
                    {escrow.timeRemaining}
                  </span>
                </div>
                {escrow.releasedAt && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">{t('Released')}</span>
                    <span>{escrow.getReleasedAt()}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Confirmation Status */}
          {escrow.status === 'pending' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5" />
                  <span>{t('Confirmation Status')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">{t('Sender')}</span>
                  {escrow.senderConfirmed ? (
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      {t('Confirmed')}
                    </Badge>
                  ) : (
                    <Badge variant="secondary">
                      <Clock className="h-3 w-3 mr-1" />
                      {t('Pending')}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">{t('Recipient')}</span>
                  {escrow.recipientConfirmed ? (
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      {t('Confirmed')}
                    </Badge>
                  ) : (
                    <Badge variant="secondary">
                      <Clock className="h-3 w-3 mr-1" />
                      {t('Pending')}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

import currencyRatesFetcher from '#services/currency_rates_fetcher';
import investmentService from '#services/investment_service';
import recurringTransferService from '#services/recurring_transfer_service';
import escrowService from '#services/escrow_service';
import escrowNotificationService from '#services/escrow_notification_service';
import fundraisingPoolService from '#services/fundraising_pool_service';
import notificationQueueProcessor from '#services/notification_queue_processor';
import scheduler from 'adonisjs-scheduler/services/main';
scheduler
    .call(async () => {
    await currencyRatesFetcher();
})
    .hourly();
scheduler
    .call(async () => {
    await investmentService('daily');
})
    .daily();
scheduler
    .call(async () => {
    await investmentService('weekly');
})
    .weekly();
scheduler
    .call(async () => {
    await investmentService('monthly');
})
    .monthly();
scheduler
    .call(async () => {
    await investmentService('yearly');
})
    .yearly();

// Recurring transfers - execute every 5 minutes
scheduler
    .call(async () => {
    await recurringTransferService.executePendingTransfers();
})
    .everyFiveMinutes();

// Retry failed recurring transfers - every hour
scheduler
    .call(async () => {
    await recurringTransferService.retryFailedTransfers();
})
    .hourly();

// Escrow deadline management - every 10 minutes
scheduler
    .call(async () => {
    await escrowService.handleExpiredEscrows();
})
    .everyTenMinutes();

// Escrow notification processing - every 5 minutes
scheduler
    .call(async () => {
    await escrowNotificationService.processPendingNotifications();
})
    .everyFiveMinutes();

// Escrow deadline reminders - every hour
scheduler
    .call(async () => {
    await escrowNotificationService.checkDeadlineReminders();
})
    .hourly();

// Retry failed escrow notifications - every 2 hours
scheduler
    .call(async () => {
    await escrowNotificationService.retryFailedNotifications();
})
    .everyTwoHours();

// Handle expired fundraising pools - daily
scheduler
    .call(async () => {
    await fundraisingPoolService.handleExpiredPools();
})
    .daily();

// Process notification queue - every 2 minutes
scheduler
    .call(async () => {
    await notificationQueueProcessor.processQueue();
})
    .everyTwoMinutes();

// Clean up old notifications - weekly
scheduler
    .call(async () => {
    await notificationQueueProcessor.cleanupOldNotifications();
})
    .weekly();
//# sourceMappingURL=scheduler.js.map
{"version": 3, "file": "1727645977289_create_notifications_table.js", "sourceRoot": "", "sources": ["../../../database/migrations/1727645977289_create_notifications_table.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AAEpD,MAAM,CAAC,OAAO,MAAO,SAAQ,UAAU;IAC3B,SAAS,GAAG,eAAe,CAAC;IAEtC,KAAK,CAAC,EAAE;QACN,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;YAChD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACvB,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YACnC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YACnC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC1C,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC5C,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAC;YACpC,KAAK;iBACF,OAAO,CAAC,SAAS,CAAC;iBAClB,QAAQ,EAAE;iBACV,UAAU,CAAC,IAAI,CAAC;iBAChB,OAAO,CAAC,OAAO,CAAC;iBAChB,QAAQ,CAAC,SAAS,CAAC;iBACnB,WAAW,EAAE,CAAC;YAEjB,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAC9B,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;CACF"}